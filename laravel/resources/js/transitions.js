import gsap from 'gsap';

// Circle transition function
window.startCircleTransition = function(event, projectSlug, color = '#042c48') {
    // Prevent default link behavior
    event.preventDefault();
    
    console.log('Starting circle transition to:', projectSlug);
    
    // Get the transition elements
    const transitionContainer = document.getElementById('circleTransition');
    const circleElement = transitionContainer.querySelector('.content-after');
    
    if (!transitionContainer || !circleElement) {
        console.error('Transition elements not found');
        window.location.href = '/projects/' + projectSlug;
        return;
    }
    
    // Set the circle color if provided
    if (color) {
        circleElement.style.backgroundColor = color;
    }
    
    // Get click position for centered animation
    const clickX = event.clientX;
    const clickY = event.clientY;
    
    // Position the circle at the click point
    gsap.set(circleElement, {
        top: clickY,
        left: clickX,
        xPercent: -50,
        yPercent: -50,
        scale: 0.1
    });
    
    // Make the transition container visible
    transitionContainer.style.visibility = 'visible';
    document.body.classList.add('overflow-hidden');
    
    // Create the animation timeline
    const tl = gsap.timeline({
        onComplete: () => {
            // Store the transition state
            sessionStorage.setItem('circleTransition', 'true');
            sessionStorage.setItem('transitionColor', color);
            
            // Navigate to the project page
            window.location.href = '/projects/' + projectSlug;
        }
    });
    
    // Animate the page content out
    tl.to('.page-content', {
        opacity: 0,
        duration: 0.5,
        ease: 'power4.out'
    });
    
    // Animate the circle
    tl.to(circleElement, {
        scale: 20,
        duration: 1,
        ease: 'power4.out'
    }, '-=0.3');
};

// Check for incoming transitions
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checking for transitions');
    
    // Check for circle transition
    if (sessionStorage.getItem('circleTransition') === 'true') {
        console.log('Found circle transition');
        
        const transitionColor = sessionStorage.getItem('transitionColor') || '#042c48';
        const transitionContainer = document.getElementById('circleTransition');
        const circleElement = transitionContainer.querySelector('.content-after');
        
        if (!transitionContainer || !circleElement) {
            console.error('Transition elements not found for incoming transition');
            return;
        }
        
        // Set the circle color
        circleElement.style.backgroundColor = transitionColor;
        
        // Make the transition container visible
        transitionContainer.style.visibility = 'visible';
        document.body.classList.add('overflow-hidden');
        
        // Position the circle to cover the screen
        gsap.set(circleElement, {
            top: '50%',
            left: '50%',
            xPercent: -50,
            yPercent: -50,
            scale: 20
        });
        
        // Hide the page content initially
        gsap.set('.page-content', { opacity: 0 });
        
        // Create the reverse animation timeline
        const tl = gsap.timeline({
            onComplete: () => {
                // Clean up
                transitionContainer.style.visibility = 'hidden';
                document.body.classList.remove('overflow-hidden');
                
                // Clear the session storage
                sessionStorage.removeItem('circleTransition');
                sessionStorage.removeItem('transitionColor');
            }
        });
        
        // Animate the circle out
        tl.to(circleElement, {
            scale: 0,
            duration: 1,
            ease: 'power4.out'
        });
        
        // Fade in the page content
        tl.to('.page-content', {
            opacity: 1,
            duration: 0.5,
            ease: 'power4.out'
        }, '-=0.5');
    }
});

export { startCircleTransition };