document.addEventListener("DOMContentLoaded", () => {
    const sections = gsap.utils.toArray(".section");
    const scrollTween = gsap.timeline({
        scrollTrigger: {
            trigger: document.body,
            start: "top top",
            end: "bottom bottom",
            scrub: 1,
            snap: {
                snapTo: 1 / (sections.length - 1),
                duration: 0.5,
                ease: "power1.inOut"
            },
            pin: true
        }
    });

    sections.forEach((sec, i) => {
        scrollTween.to(window, { scrollTo: sec, ease: "none" }, i * (1 / (sections.length - 1)));
    });
});