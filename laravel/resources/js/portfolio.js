// Simple portfolio page animations
import gsap from 'gsap';

document.addEventListener('DOMContentLoaded', () => {
    // Check if we're on the portfolio page
    const portfolioContainer = document.querySelector('.portfolio-container');
    if (!portfolioContainer) return;
    
    // Animate portfolio items with a staggered effect
    const items = document.querySelectorAll('.portfolio-item');
    if (items.length) {
        gsap.from(items, {
            opacity: 0,
            y: 30,
            stagger: 0.1,
            duration: 0.6,
            ease: 'power2.out'
        });
    }
    
    // Add click handlers for any portfolio filters
    const filters = document.querySelectorAll('.portfolio-filter');
    filters.forEach(filter => {
        filter.addEventListener('click', () => {
            // Add your filtering logic here
            
            // Animate new items
            gsap.from('.portfolio-item:not(.hidden)', {
                opacity: 0,
                y: 20,
                stagger: 0.05,
                duration: 0.5,
                delay: 0.2
            });
        });
    });
});