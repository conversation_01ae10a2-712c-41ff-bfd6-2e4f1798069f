@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Email Verification Card -->
        <div class="max-w-md w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-10 h-10 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h2 class="text-4xl font-bold text-bronze mb-2">Verify Your Email</h2>
                <p class="text-white/80 text-lg">We've sent a verification link to your email</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-white font-medium">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-red-400/30 shadow-xl">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-red-400/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="space-y-2">
                            @foreach($errors->all() as $error)
                                <p class="text-white font-medium">{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Verification Instructions -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl">
                <div class="text-center space-y-6">
                    <div class="space-y-4">
                        <h3 class="text-2xl font-bold text-bronze">Check Your Email</h3>
                        <p class="text-white/80 leading-relaxed">
                            We've sent a verification link to <strong class="text-bronze">{{ Auth::user()->email }}</strong>. 
                            Click the link in the email to verify your account and access your dashboard.
                        </p>
                    </div>

                    <div class="border-t border-white/20 pt-6">
                        <h4 class="text-lg font-semibold text-bronze mb-3">Didn't receive the email?</h4>
                        <p class="text-white/70 text-sm mb-4">
                            Check your spam folder, or click below to resend the verification email.
                        </p>
                        
                        <form method="POST" action="{{ route('verification.send') }}">
                            @csrf
                            <button 
                                type="submit" 
                                class="bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                            >
                                Resend Verification Email
                            </button>
                        </form>
                    </div>

                    <div class="border-t border-white/20 pt-6">
                        <p class="text-white/60 text-sm mb-4">
                            Need to update your email address?
                        </p>
                        <a
                            href="{{ route('register') }}"
                            class="inline-block bg-red-500/20 hover:bg-red-500/30 text-red-400 font-medium py-2 px-4 rounded-xl transition-all duration-300"
                        >
                            Register with Different Email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-white/60 hover:text-white transition-colors duration-300">
                    ← Back to Home
                </a>
            </div>
        </div>
    </div>
@endsection
