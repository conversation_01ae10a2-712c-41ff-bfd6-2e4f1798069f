@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Registration Card -->
        <div class="max-w-md w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <h2 class="text-4xl font-bold text-bronze mb-2">Create Account</h2>
                <p class="text-white/80 text-lg">Join our exclusive client portal</p>
            </div>

            <!-- Elegant Success/Error Messages -->
            @if(session('success'))
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-white font-medium">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-red-400/30 shadow-xl">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-red-400/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="space-y-2">
                            @foreach($errors->all() as $error)
                                <p class="text-white font-medium">{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Registration Form -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl">
                <form class="space-y-6" action="{{ route('register.submit') }}" method="POST">
                    @csrf
                    
                    <!-- Name Field -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-white/90 mb-2">
                            Full Name
                        </label>
                        <input 
                            id="name" 
                            name="name" 
                            type="text" 
                            required 
                            value="{{ old('name') }}"
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Enter your full name"
                        >
                    </div>

                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-white/90 mb-2">
                            Email Address
                        </label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            required 
                            value="{{ old('email') }}"
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Enter your email"
                        >
                    </div>

                    <!-- Company Field -->
                    <div>
                        <label for="company" class="block text-sm font-medium text-white/90 mb-2">
                            Company (Optional)
                        </label>
                        <input 
                            id="company" 
                            name="company" 
                            type="text" 
                            value="{{ old('company') }}"
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Enter your company name"
                        >
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-white/90 mb-2">
                            Password
                        </label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Create a password (min 8 characters)"
                        >
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-white/90 mb-2">
                            Confirm Password
                        </label>
                        <input 
                            id="password_confirmation" 
                            name="password_confirmation" 
                            type="password" 
                            required 
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Confirm your password"
                        >
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button 
                            type="submit" 
                            class="w-full bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                            Create Account
                        </button>
                    </div>

                    <!-- Login Link -->
                    <div class="text-center">
                        <p class="text-white/80">
                            Already have an account? 
                            <a href="{{ route('login') }}" class="text-bronze hover:text-bronze/80 font-medium transition-colors duration-300">
                                Sign in here
                            </a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-white/60 hover:text-white transition-colors duration-300">
                    ← Back to Home
                </a>
            </div>
        </div>
    </div>
@endsection
