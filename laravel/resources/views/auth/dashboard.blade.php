@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Dashboard Header -->
        <div class="max-w-7xl mx-auto">
            
            <!-- Welcome Section -->
            <div class="text-center mb-12">
                <h1 class="text-5xl font-bold text-bronze mb-4">Welcome Back!</h1>
                <p class="text-white/80 text-xl">Hello, {{ Auth::user()->name }}</p>
                <p class="text-white/60 text-lg">{{ Auth::user()->user_type ?? 'User' }} Dashboard</p>
            </div>

            <!-- Success Message -->
            @if(session('success'))
                <div class="max-w-md mx-auto mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-white font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Dashboard Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                
                <!-- User Info Card -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <!-- Profile Image Section -->
                        <div class="mb-6">
                            @if(Auth::user()->profile_image)
                                <img src="{{ asset('storage/' . Auth::user()->profile_image) }}"
                                     alt="Profile Image"
                                     class="w-16 h-16 rounded-full mx-auto object-cover border-2 border-bronze/30">
                            @else
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center mx-auto">
                                    <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            @endif

                            <!-- Image Upload Form -->
                            <div class="mt-4">
                                <form action="{{ route('profile.image.upload') }}" method="POST" enctype="multipart/form-data" class="space-y-3">
                                    @csrf
                                    <div>
                                        <input type="file"
                                               name="profile_image"
                                               accept="image/*"
                                               class="hidden"
                                               id="profile-image-input"
                                               onchange="this.form.submit()">
                                        <label for="profile-image-input"
                                               class="cursor-pointer bg-bronze/20 hover:bg-bronze/30 text-bronze text-xs font-medium py-1 px-3 rounded-lg transition-all duration-300 inline-block">
                                            {{ Auth::user()->profile_image ? 'Change Photo' : 'Upload Photo' }}
                                        </label>
                                    </div>
                                </form>

                                @if(Auth::user()->profile_image)
                                    <form action="{{ route('profile.image.remove') }}" method="POST" class="mt-2">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="bg-red-500/20 hover:bg-red-500/30 text-red-400 text-xs font-medium py-1 px-3 rounded-lg transition-all duration-300"
                                                onclick="return confirm('Are you sure you want to remove your profile image?')">
                                            Remove Photo
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>

                        <h3 class="text-2xl font-bold text-bronze mb-4">Your Profile</h3>
                        <p class="text-white/80 mb-2"><strong>Email:</strong> {{ Auth::user()->email }}</p>
                        <p class="text-white/80 mb-2"><strong>Type:</strong> {{ Auth::user()->user_type ?? 'User' }}</p>
                        @if(Auth::user()->company)
                            <p class="text-white/80"><strong>Company:</strong> {{ Auth::user()->company }}</p>
                        @endif
                    </div>
                </div>

                <!-- Projects Card -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-bronze mb-4">Your Projects</h3>
                        <p class="text-white/80 mb-4">View and manage your assigned projects</p>
                        <button class="bg-bronze/20 hover:bg-bronze/30 text-bronze font-medium py-2 px-4 rounded-xl transition-all duration-300">
                            View Projects
                        </button>
                    </div>
                </div>

                <!-- Settings Card -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-bronze mb-4">Account Settings</h3>
                        <p class="text-white/80 mb-4">Update your profile and preferences</p>
                        <a href="{{ route('profile.edit') }}" class="bg-bronze/20 hover:bg-bronze/30 text-bronze font-medium py-2 px-4 rounded-xl transition-all duration-300 inline-block">
                            Settings
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="text-center">
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl inline-block">
                    <h3 class="text-2xl font-bold text-bronze mb-6">Quick Actions</h3>
                    <div class="space-x-4">
                        @if(in_array(Auth::user()->user_type, ['admin', 'super_admin']))
                            <a href="{{ route('admin.dashboard') }}" class="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                                Admin Dashboard
                            </a>
                        @endif
                        <a href="{{ route('home') }}" class="bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                            Back to Home
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="inline-block">
                            @csrf
                            <button type="submit" class="bg-red-500/20 hover:bg-red-500/30 text-red-400 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
