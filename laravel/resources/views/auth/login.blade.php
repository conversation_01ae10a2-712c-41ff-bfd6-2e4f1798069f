@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Login Card -->
        <div class="max-w-md w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <h2 class="text-4xl font-bold text-bronze mb-2">Welcome Back</h2>
                <p class="text-white/80 text-lg">Sign in to your account</p>
            </div>

            <!-- Elegant Success/Error Messages -->
            @if(session('success'))
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-white font-medium">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if($errors->any())
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-red-400/30 shadow-xl">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-red-400/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="space-y-2">
                            @foreach($errors->all() as $error)
                                <p class="text-white font-medium">{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Login Form -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl">
                <form class="space-y-6" action="{{ route('login.submit') }}" method="POST">
                    @csrf
                    
                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-white/90 mb-2">
                            Email Address
                        </label>
                        <input 
                            id="email" 
                            name="email" 
                            type="email" 
                            required 
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Enter your email"
                        >
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-white/90 mb-2">
                            Password
                        </label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="w-full px-4 py-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-bronze transition-all duration-300"
                            placeholder="Enter your password"
                        >
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input 
                                id="remember-me" 
                                name="remember" 
                                type="checkbox" 
                                class="h-4 w-4 text-bronze focus:ring-bronze border-white/30 rounded bg-white/20"
                            >
                            <label for="remember-me" class="ml-2 block text-sm text-white/80">
                                Remember me
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="#" class="text-bronze hover:text-bronze/80 transition-colors duration-300">
                                Forgot your password?
                            </a>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button 
                            type="submit" 
                            class="w-full bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                        >
                            Sign In
                        </button>
                    </div>

                    <!-- Register Link -->
                    <div class="text-center">
                        <p class="text-white/80">
                            Don't have an account?
                            <a href="{{ route('register') }}" class="text-bronze hover:text-bronze/80 font-medium transition-colors duration-300">
                                Register here
                            </a>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Back to Home -->
            <div class="text-center">
                <a href="{{ route('home') }}" class="text-white/60 hover:text-white transition-colors duration-300">
                    ← Back to Home
                </a>
            </div>
        </div>
    </div>
@endsection
