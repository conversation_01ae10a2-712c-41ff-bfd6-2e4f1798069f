@extends('layouts.app')

@php
    // Create gallery images array for JavaScript
    $galleryImages = [];
    foreach (range(1, 6) as $i) {
        $img = $project['acf']["image_$i"] ?? null;
        if ($img) {
            $galleryImages[] = is_array($img) ? $img['url'] : $img;
        }
    }
@endphp

@push('styles')
<link href="https://fonts.googleapis.com/css2?family=Dunbar+Low:ital,wght@0,400;0,500;0,600;1,400;1,500;1,600&display=swap" rel="stylesheet">
<style>
    .dunbar-font {
        font-family: 'Dunbar Low', serif;
        font-style: italic;
        font-weight: 400;
    }
    .dunbar-font-medium {
        font-family: 'Dunbar Low', serif;
        font-style: italic;
        font-weight: 500;
    }
    .dunbar-font-semibold {
        font-family: 'Dunbar Low', serif;
        font-style: italic;
        font-weight: 600;
    }
</style>
@endpush

@section('background')
    <div class="fixed inset-0 -z-10">
        <!-- Blueprint Blue Background -->
        <div class="absolute inset-0 bg-blueprint-blue"></div>

        <!-- Project Image Watermark -->
        <img src="{{ $project['_embedded']['wp:featuredmedia'][0]['source_url'] ?? '' }}"
             alt="{{ $project['title']['rendered'] ?? '' }}"
             class="w-full h-full object-cover opacity-5">

        <!-- Blueprint Grid Pattern -->
        <div class="absolute inset-0 blueprint-grid opacity-80"></div>

        <!-- Blueprint Technical Lines -->
        <div class="absolute inset-0 blueprint-lines opacity-60"></div>
    </div>
@endsection

@section('content')

    <section id="section-hero" class="relative h-screen w-full overflow-hidden bg-gray border-b-2 border-white z-10">
        <div class="absolute inset-0 flicker-container">
            <img id="flicker-img1" src="{{ $project['_embedded']['wp:featuredmedia'][0]['source_url'] ?? '' }}"
                 alt="Primary Hero"
                 class="flicker-img opacity-80" />
            <div class="absolute inset-0 z-5 bg-gradient-to-b from-header to-charcoal opacity-20"></div>
            <img id="flicker-img2" src="{{ $project['acf']['featured_img'] }}"
                 alt="Secondary Hero"
                 class="flicker-img opacity-80" />
        </div>
        <div class="absolute inset-0 bg-gradient-to-b from-black/70 to-black/30 z-5"></div>
        <div class="absolute inset-0 flex flex-col items-center justify-center text-center px-4 z-30">
            <h1 class="text-8xl text-white">{{ $project['title']['rendered'] }}</h1>
            <p class="mt-4 text-xl text-white">{{ $project['subtitle'] ?? '' }}</p>
            <hr class="w-24 mx-auto border-t border-white mb-6">
        </div>
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
            <svg xmlns="http://www.w3.org/2000/svg"
                 class="w-10 h-10 text-white animate-bounce"
                 fill="none"
                 viewBox="0 0 24 24"
                 stroke="currentColor"
                 stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
        </div>
    </section>

    <div style="position:relative; min-height:100vh;">
        <img src="{{ asset('img/bg.jpg') }}"
             style="position:fixed; inset:0; width:100%; height:100vh; object-fit:cover; opacity:0.03; z-index:-1;"
             alt="" />
        <div style="position:relative; z-index:1;">
    <!-- CONTENT & TIMELINE WRAPPER -->
    <div class="sections-wrapper relative z-30" x-data="{
        modalOpen: false,
        currentIndex: 0,
        images: @js($galleryImages ?? []),

        openCarousel(index) {
            this.currentIndex = index;
            this.modalOpen = true;
            document.body.style.overflow = 'hidden';
        },

        closeCarousel() {
            this.modalOpen = false;
            document.body.style.overflow = 'auto';
        },

        nextImage() {
            if (this.images.length > 0) {
                this.currentIndex = (this.currentIndex + 1) % this.images.length;
            }
        },

        previousImage() {
            if (this.images.length > 0) {
                this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
            }
        }
    }"
    @keydown.escape.window="closeCarousel()"
    @keydown.arrow-left.window="previousImage()"
    @keydown.arrow-right.window="nextImage()">
        <!-- Blueprint Background Overlay -->
        <div class="absolute inset-0 opacity-30"></div>

        <!-- TIMELINE: sticky inside wrapper -->
        <div class="timeline-container text-bronze text-sm font-medium">
            <div class="line"></div>
            <div id="dot-overview" class="dot"><span>PROJECT OVERVIEW</span></div>
            <div id="dot-highlights" class="dot"><span>PROJECT HIGHLIGHTS</span></div>
            <div id="dot-narrative" class="dot"><span>PROJECT NARRATIVE</span></div>
            <div id="dot-gallery" class="dot"><span>GALLERY</span></div>
        </div>

        <!-- PAGE CONTENT SECTIONS -->
        <section id="section-overview" class="section relative">
            <div class="container mx-auto px-6 py-20">
                <div class="max-w-6xl mx-auto">
                    <!-- Content Grid -->
                    <div class="grid lg:grid-cols-2 gap-12 items-start">
                        <!-- Project Details -->
                        <div class="rounded-2xl p-8 border-2 border-dashed border-white/60 blueprint-card dunbar-font bg-white/10 backdrop-blur-md shadow-xl">
                            <div class="prose prose-lg text-white max-w-none">
                                {!! $project['acf']['overview'] ?? '<p class="text-gray-300">Project overview coming soon...</p>' !!}
                            </div>
                        </div>

                        <!-- Project Stats -->
                        <div class="space-y-6">
                            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
                                <h3 class="text-bronze text-sm font-semibold uppercase tracking-wider mb-2">Completion Date</h3>
                                <p class="text-white text-2xl font-bold">{{ $project['acf']['end_date'] ?? 'TBD' }}</p>
                            </div>

                            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
                                <h3 class="text-bronze text-sm font-semibold uppercase tracking-wider mb-2">Location</h3>
                                <p class="text-white text-2xl font-bold">{{ $project['acf']['location'] ?? 'TBD' }}</p>
                            </div>

                            @if($project['acf']['client'] ?? false)
                            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
                                <h3 class="text-bronze text-sm font-semibold uppercase tracking-wider mb-2">Client</h3>
                                <p class="text-white text-2xl font-bold">{{ $project['acf']['client'] }}</p>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-highlights" class="section relative">
            <div class="container mx-auto px-6 py-20">
                <div class="max-w-6xl mx-auto text-center">
                    <!-- Highlights Content -->
                    <div class="rounded-2xl p-12 border-2 border-dashed border-white/60 blueprint-card dunbar-font bg-white/10 backdrop-blur-md shadow-xl">
                        <div class="prose prose-xl text-white max-w-none text-center">
                            {!! $project['acf']['highlight_text'] ?? '<p class="text-gray-300">Project highlights coming soon...</p>' !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-narrative" class="section relative">
            <div class="container mx-auto px-6 py-20">
                <div class="max-w-6xl mx-auto">
                    <!-- Narrative Content -->
                    <div class="rounded-2xl p-12 border-2 border-dashed border-white/60 blueprint-card dunbar-font bg-white/10 backdrop-blur-md shadow-xl">
                        <div class="prose prose-lg text-white max-w-none">
                            {!! $project['acf']['full_story'] ?? '<p class="text-gray-300">Project narrative coming soon...</p>' !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="section-gallery" class="section relative">
            <div class="container mx-auto px-6 py-20">
                <div class="max-w-6xl mx-auto">
                    @php
                        $galleryImages = [];
                        foreach (range(1, 6) as $i) {
                            $img = $project['acf']["image_$i"] ?? null;
                            if ($img) {
                                $galleryImages[] = is_array($img) ? $img['url'] : $img;
                            }
                        }
                    @endphp

                    <!-- Gallery Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="galleryGrid">
                        @foreach ($galleryImages as $index => $imageUrl)
                            <div class="gallery-card group relative overflow-hidden rounded-2xl bg-white/10 backdrop-blur-md border border-white/30 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer hover:border-bronze/60"
                                 data-gallery-index="{{ $index }}">
                                <img src="{{ $imageUrl }}"
                                     alt="Gallery Image {{ $index + 1 }}"
                                     class="w-full h-64 object-cover pointer-events-none transition-all duration-700 group-hover:scale-110" />
                                <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                                <div class="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                                    <div class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <!-- Elegant Click Indicator -->
                                <div class="absolute inset-0 bg-bronze/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none flex items-center justify-center">
                                    <div class="bg-bronze/90 backdrop-blur-md rounded-full px-4 py-2 border border-bronze/50">
                                        <span class="text-header font-semibold text-sm">View Gallery</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Luxury Gallery Modal -->
                    <div x-show="modalOpen"
                         x-cloak
                         @keydown.escape.window="closeCarousel()"
                         @keydown.arrow-left.window="previousImage()"
                         @keydown.arrow-right.window="nextImage()"
                         x-transition:enter="transition ease-out duration-500"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-300"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="fixed inset-0 z-[100] flex items-center justify-center"
                         style="background: linear-gradient(135deg, rgba(7, 73, 117, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%); backdrop-filter: blur(20px);"
                         @click.self="closeCarousel()"

                        <!-- Luxury Modal Content -->
                        <div class="relative w-full h-full flex items-center justify-center p-8">

                            <!-- Main Image Container with Blueprint Frame -->
                            <div class="relative max-w-7xl max-h-full">
                                <!-- Blueprint Frame -->
                                <div class="absolute -inset-4 border-2 border-dashed border-bronze/60 rounded-3xl bg-white/5 backdrop-blur-sm"></div>

                                <!-- Main Image -->
                                <img :src="images[currentIndex]"
                                     :alt="'Gallery Image ' + (currentIndex + 1)"
                                     class="max-w-full max-h-[85vh] object-contain rounded-2xl shadow-2xl border-4 border-white/20"
                                     style="filter: drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5));"
                                     x-transition:enter="transition ease-out duration-500"
                                     x-transition:enter-start="opacity-0 transform scale-90 rotate-1"
                                     x-transition:enter-end="opacity-100 transform scale-100 rotate-0">

                                <!-- Luxury Image Counter -->
                                <div class="absolute -top-8 left-0 bg-bronze/90 backdrop-blur-md rounded-full px-6 py-3 border border-bronze/50">
                                    <span class="text-header font-bold text-lg" x-text="(currentIndex + 1) + ' / ' + images.length"></span>
                                </div>
                            </div>

                            <!-- Luxury Navigation Arrows -->
                            <button @click="previousImage()"
                                    x-show="images.length > 1"
                                    class="absolute left-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-bronze/20 backdrop-blur-md rounded-full flex items-center justify-center text-bronze border-2 border-bronze/40 hover:bg-bronze/30 hover:border-bronze/60 transition-all duration-300 hover:scale-110 shadow-xl">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>

                            <button @click="nextImage()"
                                    x-show="images.length > 1"
                                    class="absolute right-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-bronze/20 backdrop-blur-md rounded-full flex items-center justify-center text-bronze border-2 border-bronze/40 hover:bg-bronze/30 hover:border-bronze/60 transition-all duration-300 hover:scale-110 shadow-xl">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>

                            <!-- Luxury Close Button -->
                            <button @click="closeCarousel()"
                                    class="absolute top-8 right-8 w-16 h-16 bg-red-500/20 backdrop-blur-md rounded-full flex items-center justify-center text-red-400 border-2 border-red-400/40 hover:bg-red-500/30 hover:border-red-400/60 transition-all duration-300 hover:scale-110 shadow-xl">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>

                            <!-- Thumbnail Strip -->
                            <div x-show="images.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                <div class="flex space-x-2 bg-white/10 backdrop-blur-sm rounded-2xl p-3">
                                    <template x-for="(image, index) in images" :key="index">
                                        <button @click="currentIndex = index"
                                                class="relative w-16 h-16 rounded-lg overflow-hidden transition-all duration-200 hover:scale-110"
                                                :class="currentIndex === index ? 'ring-2 ring-bronze' : 'opacity-60 hover:opacity-100'">
                                            <img :src="image"
                                                 :alt="'Thumbnail ' + (index + 1)"
                                                 class="w-full h-full object-cover">
                                        </button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </section>

        <section id="section-cta" class="section relative z-50 bg-gradient-to-r from-header to-[#753307]">
            <div class="container mx-auto px-6 py-20 text-center">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-5xl font-bold text-white mb-6 tracking-wide">Ready to Start Your Custom Build?</h2>
                    <p class="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Let's bring your vision to life with our expert craftsmanship and attention to detail.</p>
                    <a href="/contact-us#form"
                       class="inline-flex items-center px-8 py-4 bg-bronze text-header font-semibold rounded-full shadow-xl hover:bg-bronze/90 hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                        <span>Contact Us</span>
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    </div>
        </div>
    </div>

    <!-- Luxury Carousel Gallery Modal -->
    <div id="galleryModal" style="display: none; position: fixed; inset: 0; z-index: 100; background: linear-gradient(135deg, rgba(7, 73, 117, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%); backdrop-filter: blur(20px);" onclick="closeGalleryModal()">
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; padding: 2rem; gap: 1.5rem;" onclick="event.stopPropagation();">

            <!-- Main Image Container -->
            <div style="position: relative; width: 60vw; height: 65vh;">
                <!-- Blueprint Frame (Behind Image) -->
                <div style="position: absolute; inset: 0; border: 2px dashed rgba(203, 160, 21, 0.6); border-radius: 1.5rem; background: rgba(255,255,255,0.05); backdrop-filter: blur(10px); z-index: 1;"></div>

                <!-- Image Container (Fills most of the frame) -->
                <div style="position: relative; z-index: 2; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; padding: 1.5rem; box-sizing: border-box;">
                    <img id="modalImage" src="" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 0.75rem; box-shadow: 0 25px 50px rgba(0,0,0,0.5); border: 4px solid rgba(255,255,255,0.2);">
                </div>

                <!-- Image Counter (Above Everything) -->
                <div style="position: absolute; top: -3rem; left: 0; background: rgba(203, 160, 21, 0.9); backdrop-filter: blur(10px); border-radius: 2rem; padding: 0.75rem 1.5rem; border: 1px solid rgba(203, 160, 21, 0.5); z-index: 3;">
                    <span id="imageCounter" style="color: #074975; font-weight: bold; font-size: 1.1rem;">1 / 6</span>
                </div>
            </div>

            <!-- Thumbnail Strip (Now properly positioned below) -->
            <div id="thumbnailStrip" style="display: flex; gap: 0.5rem; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 1rem; padding: 1rem; border: 1px solid rgba(255,255,255,0.2); z-index: 4;"></div>

            <!-- Navigation Arrows -->
            <button id="prevBtn" onclick="previousImage()" style="position: absolute; left: 2rem; top: 50%; transform: translateY(-50%); width: 4rem; height: 4rem; background: rgba(203, 160, 21, 0.2); backdrop-filter: blur(10px); border: 2px solid rgba(203, 160, 21, 0.4); border-radius: 50%; color: #cba015; font-size: 1.5rem; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;">
                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <button id="nextBtn" onclick="nextImage()" style="position: absolute; right: 2rem; top: 50%; transform: translateY(-50%); width: 4rem; height: 4rem; background: rgba(203, 160, 21, 0.2); backdrop-filter: blur(10px); border: 2px solid rgba(203, 160, 21, 0.4); border-radius: 50%; color: #cba015; font-size: 1.5rem; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;">
                <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Close Button -->
            <button onclick="closeGalleryModal()" style="position: absolute; top: 2rem; right: 2rem; width: 4rem; height: 4rem; background: rgba(239, 68, 68, 0.2); backdrop-filter: blur(10px); border: 2px solid rgba(248, 113, 113, 0.4); border-radius: 50%; color: rgb(248, 113, 113); font-size: 1.5rem; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
    </div>

    <script>
        const galleryImages = @json($galleryImages ?? []);
        let currentImageIndex = 0;

        function openGalleryModal(index) {
            currentImageIndex = index;
            const modal = document.getElementById('galleryModal');

            updateModalImage();
            createThumbnails();
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeGalleryModal() {
            const modal = document.getElementById('galleryModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function updateModalImage() {
            const modalImage = document.getElementById('modalImage');
            const imageCounter = document.getElementById('imageCounter');

            if (galleryImages[currentImageIndex]) {
                modalImage.src = galleryImages[currentImageIndex];
                imageCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
                updateThumbnailSelection();
            }
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
            updateModalImage();
        }

        function previousImage() {
            currentImageIndex = currentImageIndex === 0 ? galleryImages.length - 1 : currentImageIndex - 1;
            updateModalImage();
        }

        function goToImage(index) {
            currentImageIndex = index;
            updateModalImage();
        }

        function createThumbnails() {
            const thumbnailStrip = document.getElementById('thumbnailStrip');
            thumbnailStrip.innerHTML = '';

            galleryImages.forEach((imageUrl, index) => {
                const thumb = document.createElement('div');
                thumb.style.cssText = `
                    width: 4rem; height: 4rem; border-radius: 0.5rem; overflow: hidden;
                    cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;
                    background-image: url('${imageUrl}'); background-size: cover; background-position: center;
                `;
                thumb.onclick = () => goToImage(index);
                thumb.dataset.index = index;
                thumbnailStrip.appendChild(thumb);
            });

            updateThumbnailSelection();
        }

        function updateThumbnailSelection() {
            const thumbnails = document.querySelectorAll('#thumbnailStrip > div');
            thumbnails.forEach((thumb, index) => {
                if (index === currentImageIndex) {
                    thumb.style.border = '2px solid #cba015';
                    thumb.style.transform = 'scale(1.1)';
                } else {
                    thumb.style.border = '2px solid rgba(255,255,255,0.3)';
                    thumb.style.transform = 'scale(1)';
                }
            });
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('galleryModal');
            if (modal.style.display === 'block') {
                switch(e.key) {
                    case 'Escape':
                        closeGalleryModal();
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        previousImage();
                        break;
                    case 'ArrowRight':
                        e.preventDefault();
                        nextImage();
                        break;
                }
            }
        });

        // Add click listeners to gallery cards
        document.addEventListener('DOMContentLoaded', function() {
            const galleryCards = document.querySelectorAll('.gallery-card');

            galleryCards.forEach(function(card, index) {
                card.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const galleryIndex = parseInt(this.dataset.galleryIndex);
                    openGalleryModal(galleryIndex);
                });
            });
        });
    </script>

    <style>
        .blueprint {
            position: fixed; inset: 0; z-index: -10;
            background-color: #074975;
            background-image: linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }
        /* Dissolve transition repeated 3 times */
        .flicker-container {
            position: absolute;
            inset: 0;
            overflow: hidden;
            z-index: 0;
        }
        #flicker-img1,
        #flicker-img2 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }
        #flicker-img1 {
            opacity: 1;
            animation: dissolve 1.5s ease-in-out 0.2s 2 alternate forwards;
        }
        #flicker-img2 {
            opacity: 0;
            animation: dissolve 1.5s ease-in-out 0.2s 2 alternate-reverse forwards;
        }
        @keyframes dissolve {
            0%   { opacity: 1; }
            100% { opacity: 0; }
        }
        .sections-wrapper {
            position: relative;
            margin-top: 0;
            background: transparent;
            min-height: 100vh;
            z-index: 30;
        }

        /* Authentic Blueprint Background */
        .bg-blueprint-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
        }

        .blueprint-grid {
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px),
                linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
            background-position: 0 0, 0 0, 0 0, 0 0;
            animation: blueprint-drift 40s linear infinite;
        }

        .blueprint-lines {
            background-image:
                linear-gradient(45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 40px 40px, 40px 40px, 200px 200px;
            background-position: 0 0, 0 0, 0 0;
            animation: blueprint-lines-drift 60s linear infinite;
        }

        @keyframes blueprint-drift {
            0% { background-position: 0 0, 0 0, 0 0, 0 0; }
            100% { background-position: 100px 100px, 100px 100px, 20px 20px, 20px 20px; }
        }

        @keyframes blueprint-lines-drift {
            0% { background-position: 0 0, 0 0, 0 0; }
            100% { background-position: 40px 40px, -40px -40px, 200px 200px; }
        }

        /* Cream color definitions */
        .from-cream-95 {
            background: rgba(250, 248, 243, 0.95);
        }

        .via-cream-90 {
            background: rgba(250, 248, 243, 0.9);
        }

        .to-cream-95 {
            background: rgba(250, 248, 243, 0.95);
        }

        .bg-gradient-to-br.from-cream-95.via-cream-90.to-cream-95 {
            background: linear-gradient(to bottom right,
                rgba(250, 248, 243, 0.95) 0%,
                rgba(250, 248, 243, 0.9) 50%,
                rgba(250, 248, 243, 0.95) 100%);
        }

        /* Enhanced glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
        }

        /* Blueprint card effect */
        .blueprint-card {
            position: relative;
            background: transparent;
            transition: all 0.4s ease;
        }

        .blueprint-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            border-radius: 1rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .blueprint-card:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            backdrop-filter: blur(15px);
            border-color: rgba(255, 255, 255, 0.8);
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.3),
                0 10px 40px rgba(0, 0, 0, 0.2),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .blueprint-card:hover::before {
            opacity: 1;
        }

        /* Blueprint corner markers */
        .blueprint-card::after {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            width: 20px;
            height: 20px;
            border-top: 2px solid rgba(203, 160, 21, 0.6);
            border-left: 2px solid rgba(203, 160, 21, 0.6);
            transition: all 0.3s ease;
        }

        .blueprint-card:hover::after {
            border-color: rgba(203, 160, 21, 0.9);
            width: 30px;
            height: 30px;
        }

        /* Custom prose styling for blueprint background */
        .prose h1, .prose h2, .prose h3, .prose h4 {
            color: #cba015;
            font-weight: 700;
        }

        .prose p {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.8;
        }

        /* Fix WordPress table layout - center icons above text */
        .prose table {
            margin: 0 auto !important;
            width: auto !important;
        }

        .prose table td {
            vertical-align: middle !important;
            text-align: center !important;
            padding: 2rem !important;
        }

        .prose table td img {
            display: block !important;
            margin: 0 auto 1rem auto !important;
        }

        .prose table td b {
            display: block !important;
            margin-top: 0.5rem !important;
        }

        .prose strong {
            color: #cba015;
        }

        .prose ul li, .prose ol li {
            color: rgba(255, 255, 255, 0.9);
        }

        /* Dunbar font styling for blueprint cards */
        .dunbar-font .prose p,
        .dunbar-font .prose li,
        .dunbar-font .prose blockquote {
            font-family: 'Dunbar Low', serif;
            font-style: italic;
            font-weight: 400;
            letter-spacing: 0.025em;
        }

        .dunbar-font .prose h1,
        .dunbar-font .prose h2,
        .dunbar-font .prose h3,
        .dunbar-font .prose h4 {
            font-family: 'Dunbar Low', serif;
            font-style: italic;
            font-weight: 600;
            letter-spacing: 0.05em;
        }
        .timeline-container {
            position: fixed;
            top: 0;
            left: 2rem;
            width: 0; /* No width so it doesn't affect layout */
            height: 100vh;
            pointer-events: none;
            z-index: 40; /* Above content (z-30) but below hero/footer (z-50) */
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        /* Hide timeline on mobile */
        @media (max-width: 768px) {
            .timeline-container {
                display: none;
            }
        }

        /* Adjust for tablet */
        @media (min-width: 769px) and (max-width: 1024px) {
            .timeline-container {
                left: 1rem;
            }
        }

        /* Desktop positioning - keep it away from content */
        @media (min-width: 1025px) {
            .timeline-container {
                left: max(2rem, calc((100vw - 1200px) / 2 - 100px));
            }
        }

        .timeline-container .line {
            position: absolute;
            left: 20px;
            top: 15%;
            width: 8px;
            height: 0;
            background: linear-gradient(to bottom,
                #cba015 0%,
                #b8941a 25%,
                #8b7014 50%,
                #5a4a0d 75%,
                #074975 100%);
            border-radius: 4px;
            box-shadow:
                0 0 12px rgba(203, 160, 21, 0.5),
                inset 0 0 4px rgba(255, 255, 255, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 10;
            transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: top center;
        }

        .dot {
            position: absolute;
            left: 24px;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: rgba(203, 160, 21, 0.3);
            border: 3px solid rgba(203, 160, 21, 0.6);
            opacity: 0;
            visibility: hidden;
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 11;
            transform: translateX(-50%) scale(0.8);
            box-shadow:
                0 0 8px rgba(203, 160, 21, 0.3),
                inset 0 0 4px rgba(255, 255, 255, 0.2);
        }

        .dot span {
            position: absolute;
            left: 35px;
            top: 50%;
            transform: translateY(-50%);
            white-space: nowrap;
            font-size: 0.7rem;
            font-weight: 600;
            color: rgba(203, 160, 21, 0.8);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            text-shadow: 0 2px 4px rgba(0,0,0,0.8);
            background: rgba(250, 248, 243, 0.95);
            backdrop-filter: blur(10px);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(203, 160, 21, 0.3);
            color: #2d3748;
        }

        .dot.visible {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) scale(1);
            background: rgba(203, 160, 21, 0.7);
            border-color: rgba(203, 160, 21, 0.9);
        }

        .dot.active {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) scale(1.8);
            background: #cba015;
            border-color: #fff;
            box-shadow:
                0 0 30px rgba(203, 160, 21, 1),
                0 0 60px rgba(203, 160, 21, 0.6),
                inset 0 0 8px rgba(255, 255, 255, 0.4);
        }

        .dot.active span {
            opacity: 1;
            color: #2d3748;
            transform: translateY(-50%) translateX(5px);
        }

        /* Content sections - no margins needed since timeline is overlay */
        .section {
            margin-left: 0;
        }
    </style>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            gsap.registerPlugin(ScrollTrigger);

            const line = document.querySelector('.timeline-container .line');
            const wrapper = document.querySelector('.sections-wrapper');
            const timelineContainer = document.querySelector('.timeline-container');
            const keys = ['overview', 'highlights', 'narrative', 'gallery'];
            const sections = keys.map(key => document.getElementById(`section-${key}`));
            const dots = keys.map(key => document.getElementById(`dot-${key}`));

            // Store timeline triggers to manage them separately
            let timelineTriggers = [];

            // Check if timeline should be visible (not on mobile)
            function isTimelineVisible() {
                return window.innerWidth > 768;
            }

            // Function to update timeline visibility
            function updateTimelineVisibility() {
                if (!isTimelineVisible()) {
                    timelineContainer.style.display = 'none';
                    return false;
                } else {
                    timelineContainer.style.display = 'block';
                    return true;
                }
            }

            function killTimelineTriggers() {
                timelineTriggers.forEach(trigger => trigger.kill());
                timelineTriggers = [];
            }

            function initScrollTriggers() {
                if (!updateTimelineVisibility()) return;

                // Kill only timeline triggers
                killTimelineTriggers();

                // Show/hide timeline based on scroll position
                const heroSection = document.getElementById('section-hero');
                const ctaSection = document.getElementById('section-cta');

                timelineTriggers.push(ScrollTrigger.create({
                    trigger: wrapper,
                    start: 'top 90%',
                    end: 'bottom 10%',
                    onEnter: () => {
                        if (isTimelineVisible()) {
                            timelineContainer.style.opacity = '1';
                        }
                    },
                    onLeave: () => {
                        timelineContainer.style.opacity = '0';
                    },
                    onEnterBack: () => {
                        if (isTimelineVisible()) {
                            timelineContainer.style.opacity = '1';
                        }
                    },
                    onLeaveBack: () => {
                        timelineContainer.style.opacity = '0';
                    }
                }));

                // Hide timeline when over hero section
                if (heroSection) {
                    timelineTriggers.push(ScrollTrigger.create({
                        trigger: heroSection,
                        start: 'top bottom',
                        end: 'bottom top',
                        onEnter: () => {
                            timelineContainer.style.opacity = '0';
                        },
                        onLeave: () => {
                            if (isTimelineVisible()) {
                                timelineContainer.style.opacity = '1';
                            }
                        },
                        onEnterBack: () => {
                            timelineContainer.style.opacity = '0';
                        },
                        onLeaveBack: () => {
                            if (isTimelineVisible()) {
                                timelineContainer.style.opacity = '1';
                            }
                        }
                    }));
                }

                // Hide timeline when over footer/CTA section
                if (ctaSection) {
                    timelineTriggers.push(ScrollTrigger.create({
                        trigger: ctaSection,
                        start: 'top 80%',
                        end: 'bottom bottom',
                        onEnter: () => {
                            timelineContainer.style.opacity = '0';
                        },
                        onLeaveBack: () => {
                            if (isTimelineVisible()) {
                                timelineContainer.style.opacity = '1';
                            }
                        }
                    }));
                }

                // Position dots based on actual section positions
                const positionDots = () => {
                    const wrapperRect = wrapper.getBoundingClientRect();
                    const wrapperTop = wrapper.offsetTop;
                    const wrapperHeight = wrapper.offsetHeight;

                    sections.forEach((section, i) => {
                        if (!section || !dots[i]) return;

                        // Get section position relative to wrapper
                        const sectionTop = section.offsetTop - wrapperTop;
                        const sectionHeight = section.offsetHeight;
                        const sectionCenter = sectionTop + (sectionHeight / 2);

                        // Calculate percentage position within the wrapper
                        const dotPosition = (sectionCenter / wrapperHeight) * 100;

                        // Constrain to timeline area (15% to 85%)
                        const constrainedPosition = Math.max(15, Math.min(85, dotPosition));

                        dots[i].style.top = `${constrainedPosition}%`;
                    });
                };

                // Initial positioning
                positionDots();

                // Reposition on window resize
                window.addEventListener('resize', positionDots);

                // Track current active section
                let currentActiveSection = -1;

                // Progressive timeline line animation
                timelineTriggers.push(ScrollTrigger.create({
                    trigger: wrapper,
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: 1,
                    onUpdate: (self) => {
                        if (isTimelineVisible()) {
                            // Calculate progress through the entire content area
                            const progress = self.progress;
                            // Animate line height from 0% to 85% based on scroll progress
                            const lineHeight = Math.min(85, progress * 85);
                            line.style.height = `${lineHeight}%`;
                        }
                    }
                }));

                // Individual section triggers for dot visibility and activation
                keys.forEach((key, i) => {
                    if (!sections[i] || !dots[i]) return;

                    // Dot visibility trigger (wider range)
                    timelineTriggers.push(ScrollTrigger.create({
                        trigger: sections[i],
                        start: 'top 80%',
                        end: 'bottom 20%',
                        onEnter: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.add('visible');
                            }
                        },
                        onEnterBack: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.add('visible');
                            }
                        },
                        onLeave: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.remove('visible', 'active');
                            }
                        },
                        onLeaveBack: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.remove('visible', 'active');
                            }
                        }
                    }));

                    // Dot activation trigger (narrower range for active state)
                    timelineTriggers.push(ScrollTrigger.create({
                        trigger: sections[i],
                        start: 'top 50%',
                        end: 'bottom 50%',
                        onEnter: () => {
                            if (isTimelineVisible()) {
                                currentActiveSection = i;
                                // Remove active from all dots
                                dots.forEach(dot => dot.classList.remove('active'));
                                // Activate current dot
                                dots[i].classList.add('active');
                            }
                        },
                        onEnterBack: () => {
                            if (isTimelineVisible()) {
                                currentActiveSection = i;
                                // Remove active from all dots
                                dots.forEach(dot => dot.classList.remove('active'));
                                // Activate current dot
                                dots[i].classList.add('active');
                            }
                        },
                        onLeave: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.remove('active');
                            }
                        },
                        onLeaveBack: () => {
                            if (isTimelineVisible()) {
                                dots[i].classList.remove('active');
                            }
                        }
                    }));
                });

                // Initial positioning and refresh
                ScrollTrigger.refresh();
            }

            // Initialize with error handling
            try {
                initScrollTriggers();
            } catch (error) {
                console.warn('Timeline initialization error:', error);
                // Fallback: position dots based on section positions
                const wrapperRect = wrapper.getBoundingClientRect();
                const wrapperTop = wrapper.offsetTop;
                const wrapperHeight = wrapper.offsetHeight;

                sections.forEach((section, i) => {
                    if (!section || !dots[i]) return;
                    const sectionTop = section.offsetTop - wrapperTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionCenter = sectionTop + (sectionHeight / 2);
                    const dotPosition = (sectionCenter / wrapperHeight) * 100;
                    const constrainedPosition = Math.max(15, Math.min(85, dotPosition));
                    dots[i].style.top = `${constrainedPosition}%`;
                });
            }

            // Reinitialize on resize with debouncing
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    try {
                        // Reset timeline state before reinitializing
                        if (line) line.style.height = '0%';
                        dots.forEach(dot => dot.classList.remove('active'));

                        // Refresh ScrollTrigger calculations
                        ScrollTrigger.refresh();

                        // Reinitialize
                        initScrollTriggers();
                    } catch (error) {
                        console.warn('Timeline resize error:', error);
                    }
                }, 250);
            });


        });
    </script>
@endsection
