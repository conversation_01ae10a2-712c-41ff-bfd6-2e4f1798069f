
@extends('layouts.app')

@section('background')
    <div class="fixed inset-0 -z-10">
        <!-- Background Image Watermark -->
        @if (!empty($page['acf']['portfolio_header_image']))
            <img src="{{ $page['acf']['portfolio_header_image'] }}"
                 alt="{{ $page['acf']['portfolio_header_image_alt'] ?? 'Portfolio background' }}"
                 class="w-full h-full object-cover opacity-15">
        @endif

        <!-- Elegant Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-cream-95 via-cream-90 to-cream-95"></div>

        <!-- Subtle Blueprint Pattern -->
        <div class="absolute inset-0 blueprint-pattern opacity-20"></div>
    </div>
@endsection

@push('styles')
<style>
    /* Glorious portfolio styling */
    .content-wrapper {
        background:
            linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
            radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
        min-height: 100vh;
        position: relative;
    }

    .pattern-overlay {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
        background-size: 80px 80px, 80px 80px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: pattern-drift 40s linear infinite;
    }

    @keyframes pattern-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
    }

    /* Blueprint pattern for navigation background */
    .blueprint-pattern {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
        background-size: 60px 60px, 60px 60px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: blueprint-drift 30s linear infinite;
    }

    @keyframes blueprint-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 60px 60px, 60px 60px, 200px 200px; }
    }

    /* Cream color definitions */
    .from-cream-95 { background: rgba(250, 248, 243, 0.95); }
    .via-cream-90 { background: rgba(250, 248, 243, 0.9); }
    .to-cream-95 { background: rgba(250, 248, 243, 0.95); }

    .bg-gradient-to-br.from-cream-95.via-cream-90.to-cream-95 {
        background: linear-gradient(to bottom right,
            rgba(250, 248, 243, 0.95) 0%,
            rgba(250, 248, 243, 0.9) 50%,
            rgba(250, 248, 243, 0.95) 100%);
    }
</style>
@endpush

@section('content')
<!-- SPECTACULAR HERO SECTION -->
<section class="relative py-20 min-h-screen flex items-center"
         style="background-image: url('{{ $page['acf']['portfolio_header_image'] ?? '' }}'); background-size: cover; background-position: center; background-attachment: fixed;">
    <!-- Elegant Overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/60"></div>

    <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-6xl mx-auto text-center">
            <!-- Section Header -->
            <div class="mb-16">
                <span class="text-bronze text-sm font-semibold uppercase tracking-[0.2em] mb-4 block">
                    Premium Portfolio
                </span>
                <h1 class="text-6xl sm:text-9xl font-light text-white mb-6 tracking-tight leading-none">
                    Our
                    <span class="block text-bronze font-normal italic">Masterwork</span>
                </h1>

                <!-- Elegant Divider -->
                <div class="flex items-center justify-center mb-12">
                    <div class="w-20 h-px bg-bronze"></div>
                    <div class="w-4 h-4 bg-bronze rounded-full mx-6"></div>
                    <div class="w-40 h-px bg-bronze/50"></div>
                </div>
            </div>

            <!-- Hero Content -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-12 border border-white/20 shadow-2xl">
                <div class="prose prose-xl text-white max-w-none mb-10 leading-relaxed">
                    <p>Discover our portfolio of exceptional custom millwork and metalwork projects. Each piece represents our commitment to unparalleled craftsmanship and innovative design, created in partnership with top architects and discerning clients.</p>
                </div>

                <a href="#portfolio"
                   class="group inline-flex items-center text-bronze hover:text-white transition-all duration-300 text-lg font-medium">
                    <span class="border-b border-bronze group-hover:border-white transition-colors duration-300">
                        Explore Our Work
                    </span>
                    <svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- GLORIOUS CONTENT WRAPPER -->
<div class="content-wrapper relative">
    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 pattern-overlay opacity-30"></div>

    <!-- SOPHISTICATED PORTFOLIO SECTION -->
    <section id="portfolio" class="relative py-24">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Project Categories
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        Explore Our
                        <span class="block text-bronze font-normal italic">Craftsmanship</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>
                </div>

                <!-- Premium Filter Navigation -->
                <div x-data="portfolioFilter()" x-init="init()" class="relative">
                    <div class="mb-16">
                        <div class="bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-bronze/20 shadow-xl">
                            <div class="flex flex-wrap justify-center gap-6">
                                <button @click="setActive(0)"
                                       :class="active === 0 ? 'bg-bronze text-white shadow-lg scale-105' : 'bg-white/60 text-header hover:bg-bronze hover:text-white'"
                                       class="px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-md">
                                    All Projects
                                </button>
                                @foreach($parsedCategories as $cat)
                                    <button @click="setActive({{ $cat['id'] }})"
                                           :class="active === {{ $cat['id'] }} ? 'bg-bronze text-white shadow-lg scale-105' : 'bg-white/60 text-header hover:bg-bronze hover:text-white'"
                                           class="px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-md"
                                           data-id="{{ $cat['id'] }}"
                                           data-slug="{{ $cat['slug'] }}">
                                        {{ $cat['name'] }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <!-- SPECTACULAR PROJECT GRID -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                        @foreach ($projects as $project)
                            @php
                                $media = $project['_embedded']['wp:featuredmedia'][0]['source_url'] ?? null;
                                $categoryIds = $project['portfolio_category'] ?? [];
                                $title = $project['title']['rendered'] ?? '';
                                $excerpt = strip_tags($project['excerpt']['rendered'] ?? '');
                            @endphp
                            <div x-show="active === 0 || {{ Js::from($categoryIds) }}.map(Number).includes(Number(active))"
                                 x-cloak
                                 x-transition:enter="transition ease-out duration-500"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-300"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="group bg-white/80 backdrop-blur-md rounded-3xl border border-bronze/20 overflow-hidden hover:bg-white/90 transition-all duration-500 hover:transform hover:-translate-y-6 hover:shadow-2xl"
                                 style="display: none;">
                                @if ($media)
                                    <a href="{{ url('projects/' . $project['slug']) }}"
                                       @click.prevent="coverTransition('{{ $media }}', '{{ url('projects/' . $project['slug']) }}')">
                                        <!-- Premium Image Container -->
                                        <div class="relative overflow-hidden">
                                            <img src="{{ $media }}"
                                                 alt="{{ $title }}"
                                                 loading="lazy"
                                                 class="w-full h-80 object-cover transition-transform duration-700 group-hover:scale-110" />

                                            <!-- Elegant Overlay -->
                                            <div class="absolute inset-0 bg-gradient-to-t from-bronze/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                                            <!-- Premium Badge -->
                                            <div class="absolute top-4 right-4 bg-bronze/90 backdrop-blur-sm rounded-full px-4 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <span class="text-white text-sm font-bold uppercase tracking-wider">View Project</span>
                                            </div>
                                        </div>

                                        <!-- Sophisticated Content -->
                                        <div class="p-8">
                                            <h3 class="text-2xl font-bold text-header mb-4 group-hover:text-bronze transition-colors duration-300">
                                                {{ $title }}
                                            </h3>

                                            @if($excerpt)
                                                <p class="text-gray-700 leading-relaxed mb-6 line-clamp-3">
                                                    {{ Str::limit($excerpt, 120) }}
                                                </p>
                                            @endif

                                            <!-- Elegant CTA -->
                                            <div class="flex items-center text-bronze opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
                                                <span class="text-sm font-semibold">Explore Project</span>
                                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

    <script>
        function portfolioFilter() {
            return {
                active: 0,
                init() {
                    const hash = window.location.hash.replace('#', '');
                    const button = document.querySelector(`[data-slug="${hash}"]`);
                    this.active = button ? parseInt(button.getAttribute('data-id')) : 0;
                },
                setActive(id) {
                    this.active = id;
                    // Smooth scroll to portfolio section when filter is changed
                    if (id !== 0) {
                        document.getElementById('portfolio').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                },
                coverTransition(imageSrc, url) {
                    const cover = document.createElement('div');
                    cover.style.position = 'fixed';
                    cover.style.bottom = 0;
                    cover.style.left = 0;
                    cover.style.width = '100%';
                    cover.style.height = '0';
                    cover.style.backgroundColor = '#074975';
                    cover.style.zIndex = '9999';
                    cover.style.transition = 'height 0.6s ease-in-out';
                    document.body.appendChild(cover);

                    cover.getBoundingClientRect(); // force reflow
                    cover.style.height = '100%';

                    sessionStorage.setItem('zoomedTransition', 'true');
                    sessionStorage.setItem('transitionImage', imageSrc);

                    setTimeout(() => {
                        window.location = url;
                    }, 600);
                }
            }
        }
    </script>

    <style>
        [x-cloak] { display: none !important; }

        /* Enhanced line clamp */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Smooth animations */
        .transition-all {
            transition-property: all;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }
    </style>
@endsection
