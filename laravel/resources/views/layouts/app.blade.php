<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($page['title']['rendered']) ? strip_tags($page['title']['rendered']) : config('app.name', 'Cache River Mill & Steel Works') }}</title>
    <meta name="description" content="{{ isset($page['excerpt']['rendered']) ? strip_tags($page['excerpt']['rendered']) : 'Premium custom millwork and metalwork' }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Original Site Scripts (Load First) -->
    <script src="{{ asset('js/vendor.js') }}"></script>
    <script src="{{ asset('js/main.js') }}"></script>

    <!-- Alpine.js (Load After) -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>



    <!-- Vite Styles -->
    @vite(['resources/css/app.css'])

    <!-- FIX CLIPPING ISSUES -->
    <style>
        /* Fix clipping for hover transforms */
        section:has(.hover\:-translate-y-2),
        section:has(.hover\:transform),
        .grid:has(.hover\:-translate-y-2),
        .grid:has(.hover\:transform) {
            overflow: visible !important;
            padding-top: 2rem !important;
            padding-bottom: 2rem !important;
        }

        /* Ensure hover elements don't get clipped */
        .hover\:-translate-y-2:hover,
        .hover\:transform:hover {
            z-index: 10 !important;
        }
    </style>

    @stack('styles')
</head>
<body class="font-sans antialiased">
    @yield('background')

    <!-- Header -->
    @include('partials.header', [
        'global' => $global ?? [],
        'nav' => $nav ?? [],
        'colorMenu' => $colorMenu ?? 'header',
        'colorLogo' => $colorLogo ?? 'header'
    ])

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    @include('partials.footer', ['global' => $global ?? []])

    <!-- Custom Scripts -->
    @stack('scripts')
</body>
</html>
