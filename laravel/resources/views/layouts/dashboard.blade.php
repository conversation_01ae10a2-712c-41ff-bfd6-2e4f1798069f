<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($page['title']['rendered']) ? strip_tags($page['title']['rendered']) : config('app.name', 'Cache River Mill & Steel Works') }}</title>
    <meta name="description" content="{{ isset($page['excerpt']['rendered']) ? strip_tags($page['excerpt']['rendered']) : 'Premium custom millwork and metalwork' }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Original Site Scripts (Load First) -->
    <script src="{{ asset('js/vendor.js') }}"></script>
    <script src="{{ asset('js/main.js') }}"></script>

    <!-- Alpine.js (Load After) -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Vite Styles -->
    @vite(['resources/css/app.css'])

    <!-- Dashboard Specific Styles -->
    <style>
        /* Left Sidebar Styles */
        .left-sidebar {
            width: 80px;
            transition: width 0.3s ease-in-out;
        }

        .left-sidebar.expanded {
            width: 280px;
        }

        .sidebar-overlay {
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        }

        .sidebar-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        /* Main content shift */
        .main-content {
            margin-left: 80px;
            transition: margin-left 0.3s ease-in-out;
        }

        .main-content.shifted {
            margin-left: 280px;
        }

        /* Menu text fade in/out */
        .menu-text {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
            white-space: nowrap;
            overflow: hidden;
        }

        .left-sidebar.expanded .menu-text {
            opacity: 1;
            transition-delay: 0.1s;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .left-sidebar {
                width: 0;
                overflow: hidden;
            }

            .left-sidebar.expanded {
                width: 280px;
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.shifted {
                margin-left: 0;
            }
        }
    </style>

    @stack('styles')
</head>
<body class="font-sans antialiased" x-data="{ sidebarOpen: false }">
    @yield('background')

    <!-- Left Sidebar -->
    <div class="left-sidebar fixed inset-y-0 left-0 z-50 bg-white shadow-lg border-r border-gray-200"
         :class="{ 'expanded': sidebarOpen }">

        <!-- Sidebar Header -->
        <div class="flex items-center p-4 border-b border-gray-200">
            <!-- Profile Icon Toggle -->
            <button @click="sidebarOpen = !sidebarOpen"
                    class="flex items-center space-x-3 text-gray-600 hover:text-gray-900 transition-colors duration-200 group w-full">
                @if(Auth::user()->profile_image)
                    <img src="{{ asset('storage/' . Auth::user()->profile_image) }}"
                         alt="Profile"
                         class="w-12 h-12 rounded-full object-cover border border-gray-300 group-hover:border-header transition-colors duration-200 flex-shrink-0">
                @else
                    <div class="w-12 h-12 bg-header/10 group-hover:bg-header/20 rounded-full flex items-center justify-center transition-colors duration-200 flex-shrink-0">
                        <svg class="w-6 h-6 text-header" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                @endif
                <div class="menu-text flex-1 text-left">
                    <div class="font-semibold text-gray-800">{{ Auth::user()->name }}</div>
                    <div class="text-sm text-gray-500">{{ Auth::user()->user_type ?? 'User' }}</div>
                </div>
                <svg class="w-4 h-4 transition-transform duration-200 menu-text" :class="{ 'rotate-180': sidebarOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
        </div>

        <!-- Sidebar Navigation -->
        <nav class="flex-1 px-2 py-4 space-y-1">

            <!-- Dashboard Link -->
            <a href="{{ route('dashboard') }}"
               class="flex items-center px-3 py-3 rounded-lg transition-all duration-200 group {{ request()->routeIs('dashboard') ? 'bg-header/10 text-header font-semibold' : 'text-gray-600 hover:text-header hover:bg-gray-50' }}">
                <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                </svg>
                <span class="menu-text ml-3">User Dashboard</span>
            </a>

            @if(in_array(Auth::user()->user_type, ['admin', 'super_admin']))
                <!-- Admin Dashboard Link -->
                <a href="{{ route('admin.dashboard') }}"
                   class="flex items-center px-3 py-3 rounded-lg transition-all duration-200 group {{ request()->routeIs('admin.dashboard') ? 'bg-header/10 text-header font-semibold' : 'text-gray-600 hover:text-header hover:bg-gray-50' }}">
                    <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="menu-text ml-3">Admin Dashboard</span>
                </a>

                <!-- User Management -->
                <a href="{{ route('admin.users') }}"
                   class="flex items-center px-3 py-3 rounded-lg transition-all duration-200 group {{ request()->routeIs('admin.users') ? 'bg-header/10 text-header font-semibold' : 'text-gray-600 hover:text-header hover:bg-gray-50' }}">
                    <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <span class="menu-text ml-3">User Management</span>
                </a>

                <!-- Form Submissions -->
                <a href="{{ route('admin.form-submissions') }}"
                   class="flex items-center px-3 py-3 rounded-lg transition-all duration-200 group {{ request()->routeIs('admin.form-submissions') ? 'bg-header/10 text-header font-semibold' : 'text-gray-600 hover:text-header hover:bg-gray-50' }}">
                    <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span class="menu-text ml-3">Form Submissions</span>
                </a>
            @endif

            <!-- Divider -->
            <div class="border-t border-gray-200 my-3"></div>

            <!-- Profile Settings -->
            <a href="{{ route('profile.edit') }}"
               class="flex items-center px-3 py-3 rounded-lg transition-all duration-200 group {{ request()->routeIs('profile.edit') ? 'bg-header/10 text-header font-semibold' : 'text-gray-600 hover:text-header hover:bg-gray-50' }}">
                <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="menu-text ml-3">Profile Settings</span>
            </a>

            <!-- Back to Main Site -->
            <a href="{{ route('home') }}"
               class="flex items-center px-3 py-3 text-gray-600 hover:text-header hover:bg-gray-50 rounded-lg transition-all duration-200 group">
                <svg class="w-5 h-5 flex-shrink-0 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <span class="menu-text ml-3">Back to Website</span>
            </a>
        </nav>

        <!-- Sidebar Footer -->
        <div class="p-2 border-t border-gray-200">
            <form method="POST" action="{{ route('logout') }}">
                @csrf
                <button type="submit"
                        class="flex items-center w-full px-3 py-3 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200 group">
                    <svg class="w-5 h-5 flex-shrink-0 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.636 5.636a9 9 0 1012.728 0M12 3v9"></path>
                    </svg>
                    <span class="menu-text ml-3">Logout</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Sidebar Overlay (Mobile) -->
    <div class="sidebar-overlay fixed inset-0 bg-black/50 z-40 md:hidden"
         :class="{ 'open': sidebarOpen }"
         @click="sidebarOpen = false"></div>

    <!-- Page Title Bar (Optional) -->
    <div class="main-content bg-gray-50 border-b border-gray-200 py-4 px-6"
         :class="{ 'shifted': sidebarOpen && window.innerWidth >= 768 }">
        <h1 class="text-2xl font-bold text-gray-800">
            @yield('page-title', 'Dashboard')
        </h1>
        <p class="text-gray-600 text-sm mt-1">{{ date('l, F j, Y') }}</p>
    </div>

    <!-- Main Content -->
    <main class="main-content min-h-screen"
          :class="{ 'shifted': sidebarOpen && window.innerWidth >= 768 }">
        @yield('content')
    </main>

    <!-- Custom Scripts -->
    @stack('scripts')
</body>
</html>
