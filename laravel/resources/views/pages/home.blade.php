@extends('layouts.app')
@section('content')
    <!-- STUNNING HERO SECTION -->
    <section class="relative h-screen w-full overflow-hidden z-50">
        <!-- Hero Image -->
        <img src="{{ $page['acf']['home_main_image'] ?? '' }}"
             alt="{{ $page['acf']['home_main_image_alt'] ?? '' }}"
             class="absolute inset-0 w-full h-full object-cover object-center z-0">

        <!-- Elegant Gradient Overlay -->
        <div class="absolute inset-0 z-10 bg-gradient-to-r from-black/80 via-black/40 to-black/60"></div>

        <!-- Floating Awards -->
        <div class="absolute top-8 right-8 z-30 hidden lg:block">
            <div class="flex space-x-4">
                @for ($i = 0; $i < 3; $i++)
                    <div class="w-16 h-16 relative group animate-float" style="animation-delay: {{ $i * 0.5 }}s">
                        <img class="w-full h-full transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
                             src="{{ asset('img/award_wreath.png') }}"
                             alt="Award">
                        <div class="absolute -bottom-2 -right-2 w-6 h-6 bg-bronze rounded-full flex items-center justify-center">
                            <span class="text-xs font-bold text-header">{{ $i + 1 }}</span>
                        </div>
                    </div>
                @endfor
            </div>
        </div>

        <!-- Main Content -->
        <div class="relative z-20 h-full flex items-center">
            <div class="pl-16 pr-8">
                <div>
                    <!-- Elegant Typography -->
                    <div class="mb-8">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-6 block animate-fade-in-up" style="animation-delay: 0.2s">
                            Arkansas Craftsmanship Since 2018
                        </span>
                        <h1 class="text-6xl sm:text-8xl font-light text-white tracking-tight leading-none mb-8 animate-fade-in-up" style="animation-delay: 0.4s">
                            Custom
                            <span class="block text-bronze font-normal italic">Millwork</span>
                            <span class="block text-white/90 text-5xl sm:text-7xl">& Metalwork</span>
                        </h1>
                    </div>

                    <!-- Elegant Divider -->
                    <div class="flex items-center mb-10 animate-fade-in-up" style="animation-delay: 0.6s">
                        <div class="w-20 h-px bg-bronze"></div>
                        <div class="w-3 h-3 bg-bronze rounded-full mx-6"></div>
                        <div class="w-40 h-px bg-bronze/50"></div>
                    </div>

                    <!-- Sophisticated Description -->
                    <p class="text-xl sm:text-2xl text-white/90 max-w-3xl leading-relaxed mb-12 animate-fade-in-up" style="animation-delay: 0.8s">
                        Exceptional custom solutions across residential, commercial, and manufacturing applications.
                        <span class="text-bronze font-medium">Proudly crafted</span> in the historic KC Baking Powder building.
                    </p>

                    <!-- Elegant CTA -->
                    <div class="flex items-center space-x-8 animate-fade-in-up" style="animation-delay: 1s">
                        <a href="{{ url('contact-us#form') }}"
                           class="group inline-flex items-center text-bronze hover:text-white transition-all duration-500 text-xl font-medium">
                            <span class="border-b-2 border-bronze group-hover:border-white transition-colors duration-500">
                                Start Your Project
                            </span>
                            <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>

                        <a href="{{ url('portfolio') }}"
                           class="group text-white/70 hover:text-white transition-colors duration-300 text-lg">
                            <span class="border-b border-white/30 group-hover:border-white transition-colors duration-300">
                                View Portfolio
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
            <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div class="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
            </div>
        </div>
    </section>
    <!-- ELEGANT CONTENT WRAPPER -->
    <div class="content-wrapper relative">
        <!-- Subtle Pattern Overlay -->
        <div class="absolute inset-0 pattern-overlay opacity-30"></div>

        <!-- CRAFTSMANSHIP SHOWCASE SECTION -->
        <section class="relative py-24">
            <div class="container mx-auto px-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Elegant Section Header -->
                    <div class="text-center mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                            Our Expertise
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                            Craftsmanship
                            <span class="block text-bronze font-normal italic">& Expertise</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="w-16 h-px bg-bronze"></div>
                            <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                            <div class="w-32 h-px bg-bronze/50"></div>
                        </div>
                    </div>

                    <!-- Elegant Content Layout -->
                    <div class="space-y-20">
                        <!-- Premium Service Cards -->
                        <div class="grid md:grid-cols-2 gap-12">
                            <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-12 border border-bronze/20 shadow-xl hover:shadow-2xl transition-all duration-500 group text-center">
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center group-hover:bg-bronze/30 transition-colors duration-300 mx-auto mb-8">
                                    <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 11.172V5l-1-1z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-3xl font-bold text-header mb-6">{{ $page['acf']['home_header_1'] ?? 'Custom Solutions' }}</h3>
                                <p class="text-gray-700 leading-relaxed text-xl">{{ $page['acf']['home_blurb_1'] ?? 'We create bespoke millwork solutions tailored to your exact specifications and vision.' }}</p>
                            </div>

                            <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-12 border border-bronze/20 shadow-xl hover:shadow-2xl transition-all duration-500 group text-center">
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center group-hover:bg-bronze/30 transition-colors duration-300 mx-auto mb-8">
                                    <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-3xl font-bold text-header mb-6">{{ $page['acf']['home_header_2'] ?? 'Master Craftsmanship' }}</h3>
                                <p class="text-gray-700 leading-relaxed text-xl">{{ $page['acf']['home_blurb_2'] ?? 'Our master craftsmen bring generations of expertise to every project we undertake.' }}</p>
                            </div>
                        </div>

                        <!-- Stunning Image Showcase -->
                        <div class="relative">
                            <div class="grid md:grid-cols-2 gap-12 items-center">
                                <!-- Left Image -->
                                <div class="group relative overflow-hidden rounded-3xl shadow-2xl">
                                    <img src="{{ $page['acf']['home_image_1'] ?? '' }}"
                                         alt="Premium Custom Work Example"
                                         class="w-full h-96 object-cover transition-transform duration-700 group-hover:scale-105">
                                    <div class="absolute inset-0 bg-gradient-to-t from-bronze/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute bottom-6 left-6 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <div class="bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-3">
                                            <span class="text-header font-bold text-lg">Premium Millwork</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Image -->
                                <div class="group relative overflow-hidden rounded-3xl shadow-2xl">
                                    <img src="{{ $page['acf']['home_image_2'] ?? '' }}"
                                         alt="Exceptional Metalwork Example"
                                         class="w-full h-96 object-cover transition-transform duration-700 group-hover:scale-105">
                                    <div class="absolute inset-0 bg-gradient-to-t from-bronze/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    <div class="absolute bottom-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                        <div class="bg-white/90 backdrop-blur-sm rounded-2xl px-6 py-3">
                                            <span class="text-header font-bold text-lg">Custom Craftsmanship</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- IMPRESSIVE STATISTICS SECTION -->
        <section class="relative py-24 bg-gradient-to-r from-header via-charcoal to-header">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-7xl mx-auto">
                    <!-- Section Header -->
                    <div class="text-center mb-16">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                            Proven Excellence
                        </span>
                        <h2 class="text-4xl sm:text-6xl font-light text-white mb-6 tracking-tight">
                            By the
                            <span class="text-bronze font-normal italic">Numbers</span>
                        </h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-10 border border-white/20 text-center hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                            <div class="text-6xl sm:text-7xl font-light text-white mb-4 group-hover:text-bronze transition-colors duration-500">100%</div>
                            <div class="text-bronze text-sm font-semibold uppercase tracking-wider">Customer Satisfaction</div>
                            <div class="w-12 h-px bg-bronze/50 mx-auto mt-4"></div>
                        </div>

                        <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-10 border border-white/20 text-center hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                            <div class="text-6xl sm:text-7xl font-light text-white mb-4 group-hover:text-bronze transition-colors duration-500">5000+</div>
                            <div class="text-bronze text-sm font-semibold uppercase tracking-wider">Projects Completed</div>
                            <div class="w-12 h-px bg-bronze/50 mx-auto mt-4"></div>
                        </div>

                        <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-10 border border-white/20 text-center hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                            <div class="text-6xl sm:text-7xl font-light text-white mb-4 group-hover:text-bronze transition-colors duration-500">10/10</div>
                            <div class="text-bronze text-sm font-semibold uppercase tracking-wider">Architect Approved</div>
                            <div class="w-12 h-px bg-bronze/50 mx-auto mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- ELEGANT TESTIMONIALS SECTION -->
        <section class="relative py-24">
            <div class="container mx-auto px-6">
                <div class="max-w-5xl mx-auto text-center">
                    <!-- Sophisticated Section Header -->
                    <div class="mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                            Client Voices
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                            What Our
                            <span class="block text-bronze font-normal italic">Clients Say</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="w-16 h-px bg-bronze"></div>
                            <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                            <div class="w-32 h-px bg-bronze/50"></div>
                        </div>
                    </div>

                    <!-- Stunning Testimonial Slider -->
                    <div x-data="quoteSlider({{ Js::from($quotes) }})" x-init="init()" x-cloak class="bg-white/80 backdrop-blur-md rounded-3xl p-16 border border-bronze/20 shadow-2xl">
                        <template x-if="quotes.length">
                            <div class="h-80 flex flex-col justify-between">
                                <!-- Elegant Quote Icon -->
                                <div class="mb-6">
                                    <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto">
                                        <svg class="w-10 h-10 text-bronze" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Testimonial Text with Dynamic Sizing -->
                                <div class="flex-1 flex items-center justify-center mb-6">
                                    <blockquote
                                        class="text-header leading-relaxed font-light italic text-center"
                                        :class="getTextSizeClass(quotes[active].text)"
                                        x-text="quotes[active].text">
                                    </blockquote>
                                </div>

                                <!-- Author & Rating -->
                                <div class="flex items-center justify-center space-x-6">
                                    <div class="flex space-x-1">
                                        @for($i = 0; $i < 5; $i++)
                                            <svg class="w-5 h-5 text-bronze" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endfor
                                    </div>
                                    <div class="w-px h-6 bg-bronze/30"></div>
                                    <div class="text-bronze font-semibold text-lg" x-text="quotes[active].author"></div>
                                </div>
                            </div>
                        </template>

                        <!-- Elegant Navigation Dots -->
                        <div class="flex justify-center mt-12 space-x-4">
                            <template x-for="(quote, index) in quotes" :key="index">
                                <button @click="goTo(index)"
                                        class="w-4 h-4 rounded-full transition-all duration-500 hover:scale-125"
                                        :class="active === index ? 'bg-bronze shadow-lg' : 'bg-bronze/30 hover:bg-bronze/60'">
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- PREMIUM PORTFOLIO SHOWCASE SECTION -->
        <section class="relative py-32 overflow-hidden"
                 style="background-image: url('{{ $page['acf']['home_image_3'] ?? '' }}'); background-size: cover; background-position: center; background-attachment: fixed;">

            <!-- Sophisticated Background Overlay -->
            <div class="absolute inset-0 bg-gradient-to-br from-black/85 via-black/70 to-black/80"></div>

            <!-- Elegant Pattern Overlay -->
            <div class="absolute inset-0 opacity-20" style="background-image:
                linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
                radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
                background-size: 100px 100px, 100px 100px, 300px 300px;
                animation: pattern-drift 60s linear infinite;">
            </div>

            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-7xl mx-auto">
                    <!-- Sophisticated Section Header -->
                    <div class="text-center mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-6 block">
                            Premium Portfolio
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-white mb-8 tracking-tight leading-none">
                            {{ $page['acf']['home_header_4'] ?? 'Ready To Start' }}
                            <span class="block text-bronze font-normal italic">Your Project?</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-12">
                            <div class="w-20 h-px bg-bronze"></div>
                            <div class="w-4 h-4 bg-bronze rounded-full mx-6"></div>
                            <div class="w-40 h-px bg-bronze/50"></div>
                        </div>

                        <p class="text-xl sm:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-16">
                            {{ $page['acf']['home_blurb_4'] ?? 'Browse our portfolio to see what we\'ve done—and imagine what we could build for you. From luxury residential projects to high-end commercial installations, we deliver excellence.' }}
                        </p>

                        <!-- Premium CTA -->
                        <div class="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8">
                            <a href="{{ url('contact-us#form') }}"
                               class="group inline-flex items-center px-12 py-6 bg-bronze text-header font-bold text-xl rounded-full shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-500">
                                <span>Start Your Project</span>
                                <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>

                            <a href="{{ url('portfolio') }}"
                               class="group text-white hover:text-bronze transition-colors duration-300 text-xl font-medium">
                                <span class="border-b-2 border-white/30 group-hover:border-bronze transition-colors duration-300">
                                    View Full Portfolio
                                </span>
                            </a>
                        </div>
                    </div>

                    <!-- Premium Featured Projects Grid
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
                        @foreach ($featuredProjects as $project)
                            @php
                                $media = $project['_embedded']['wp:featuredmedia'][0]['source_url'] ?? null;
                                $title = $project['title']['rendered'] ?? '';
                                $termSlug = $project['category']['slug'] ?? 'project';
                            @endphp
                            <a href="{{ url('portfolio#' . $termSlug) }}"
                               class="group bg-white/10 backdrop-blur-md rounded-3xl border border-white/20 overflow-hidden hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-4 hover:shadow-2xl">
                                @if ($media)
                                    <div class="relative overflow-hidden">
                                        <img src="{{ $media }}"
                                             alt="{{ $title }}"
                                             class="w-full h-80 object-cover transition-transform duration-700 group-hover:scale-110">
                                        <div class="absolute inset-0 bg-gradient-to-t from-bronze/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                        <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                                            <div class="text-center">
                                                <h3 class="text-white text-2xl font-bold tracking-wide uppercase mb-2">{{ $termSlug }}</h3>
                                                <div class="w-12 h-px bg-white mx-auto"></div>
                                            </div>
                                        </div>
                                        <div class="absolute top-4 right-4 bg-bronze/90 backdrop-blur-sm rounded-full px-4 py-2">
                                            <span class="text-white text-sm font-bold uppercase tracking-wider">Premium</span>
                                        </div>
                                    </div>
                                @endif
                            </a>
                        @endforeach
                    </div>-->

                    <!-- Architect Login Teaser -->
                    <div class="mt-20 text-center">
                        <div class="bg-white/10 backdrop-blur-md rounded-3xl p-12 border border-bronze/30 max-w-4xl mx-auto">
                            <div class="flex items-center justify-center mb-6">
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-user-tie text-bronze text-2xl"></i>
                                </div>
                            </div>
                            <h3 class="text-3xl font-bold text-white mb-4">Architect & Designer Portal</h3>
                            <p class="text-white/80 text-lg mb-8 leading-relaxed">
                                Exclusive access for our professional partners. Track projects, access resources, and collaborate seamlessly.
                            </p>
                            <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                                @auth
                                    <a href="{{ route('dashboard') }}" class="px-8 py-3 bg-bronze/20 text-bronze border border-bronze rounded-full hover:bg-bronze hover:text-white transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                        Your Dashboard
                                    </a>
                                    <span class="text-white/60 text-sm">Welcome back, {{ Auth::user()->name }}!</span>
                                @else
                                    <a href="{{ route('login') }}" class="px-8 py-3 bg-bronze/20 text-bronze border border-bronze rounded-full hover:bg-bronze hover:text-white transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                        Professional Login
                                    </a>
                                    <span class="text-white/60 text-sm">Access your projects & resources</span>
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- STUNNING CONTACT FINALE SECTION -->
        <section class="relative py-32 overflow-hidden"
                 style="background-image: url('{{ $page['acf']['home_image_4'] ?? '' }}'); background-size: cover; background-position: center; background-attachment: fixed;">

            <!-- Sophisticated Background Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/90 via-black/70 to-black/85"></div>

            <!-- Elegant Pattern Overlay -->
            <div class="absolute inset-0 opacity-15" style="background-image:
                linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
                radial-gradient(circle at 75% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
                background-size: 120px 120px, 120px 120px, 400px 400px;
                animation: pattern-drift 80s linear infinite;">
            </div>

            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-7xl mx-auto">
                    <!-- Spectacular Section Header -->
                    <div class="text-center mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-6 block">
                            Ready to Begin?
                        </span>
                        <h2 class="text-5xl sm:text-8xl font-light text-white mb-8 tracking-tight leading-none">
                            Let's Create
                            <span class="block text-bronze font-normal italic">Something Extraordinary</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-12">
                            <div class="w-24 h-px bg-bronze"></div>
                            <div class="w-5 h-5 bg-bronze rounded-full mx-8"></div>
                            <div class="w-48 h-px bg-bronze/50"></div>
                        </div>

                        <p class="text-xl sm:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-16">
                            From concept to completion, we bring your vision to life with unmatched craftsmanship and attention to detail.
                            <span class="text-bronze font-medium">Your extraordinary project starts here.</span>
                        </p>
                    </div>

                    <!-- Premium Contact Grid -->
                    <div class="grid lg:grid-cols-3 gap-12 mb-20">
                        <!-- Main CTA Card -->
                        <div class="lg:col-span-2">
                            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-12 border border-bronze/30 text-center hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                                <div class="mb-8">
                                    <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <i class="fa-solid fa-rocket text-bronze text-3xl"></i>
                                    </div>
                                    <h3 class="text-4xl sm:text-5xl font-light text-white mb-6 leading-tight">
                                        Begin Your <span class="text-bronze font-normal italic">Next Project</span>
                                    </h3>
                                    <p class="text-white/80 text-lg mb-10 leading-relaxed max-w-2xl mx-auto">
                                        Whether you're an architect with a vision, a designer with a dream, or a client with an idea,
                                        we're here to make it reality with our premium craftsmanship.
                                    </p>
                                </div>

                                <!-- Premium CTAs -->
                                <div class="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8">
                                    <a href="{{ url('contact-us#form') }}"
                                       class="group inline-flex items-center px-12 py-6 bg-bronze text-header font-bold text-xl rounded-full shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-500">
                                        <span>Start Your Project</span>
                                        <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                        </svg>
                                    </a>

                                    <a href="{{ url('shop-tour') }}"
                                       class="group text-white hover:text-bronze transition-colors duration-300 text-xl font-medium">
                                        <span class="border-b-2 border-white/30 group-hover:border-bronze transition-colors duration-300">
                                            Tour Our Facility
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Info Card -->
                        <div class="space-y-8">
                            <!-- Visit Us -->
                            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-bronze/20 hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                                <div class="flex items-center mb-6">
                                    <div class="w-12 h-12 bg-bronze/20 rounded-full flex items-center justify-center mr-4">
                                        <i class="fa-solid fa-map-marker-alt text-bronze text-lg"></i>
                                    </div>
                                    <h3 class="text-2xl font-bold text-white">Visit Our Shop</h3>
                                </div>
                                @if (!empty($global['acf']['visit_address']))
                                    <a href="{{ url('contact-us#map') }}" class="block hover:text-bronze transition-colors duration-300">
                                        <p class="text-white/90 whitespace-pre-line leading-relaxed">{{ $global['acf']['visit_address'] }}</p>
                                        <p class="text-bronze text-sm mt-3 font-medium">Get Directions →</p>
                                    </a>
                                @endif
                            </div>

                            <!-- Contact Info -->
                            @if (!empty($global['acf']['contact_phone']) || !empty($global['acf']['contact_email']))
                                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-bronze/20 hover:bg-white/15 transition-all duration-500 hover:transform hover:-translate-y-2">
                                    <div class="flex items-center mb-6">
                                        <div class="w-12 h-12 bg-bronze/20 rounded-full flex items-center justify-center mr-4">
                                            <i class="fa-solid fa-phone text-bronze text-lg"></i>
                                        </div>
                                        <h3 class="text-2xl font-bold text-white">Get In Touch</h3>
                                    </div>
                                    <div class="space-y-4">
                                        @if (!empty($global['acf']['contact_phone']))
                                            <a href="tel:{{ $global['acf']['contact_phone'] }}" class="flex items-center text-white/90 hover:text-bronze transition-colors duration-300">
                                                <i class="fa-solid fa-phone text-bronze mr-3"></i>
                                                <span>{{ $global['acf']['contact_phone'] }}</span>
                                            </a>
                                        @endif
                                        @if (!empty($global['acf']['contact_email']))
                                            <a href="mailto:{{ $global['acf']['contact_email'] }}" class="flex items-center text-white/90 hover:text-bronze transition-colors duration-300">
                                                <i class="fa-solid fa-envelope text-bronze mr-3"></i>
                                                <span>{{ $global['acf']['contact_email'] }}</span>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Premium Footer Message -->
                    <div class="text-center">
                        <div class="bg-white/5 backdrop-blur-md rounded-3xl p-12 border border-bronze/20 max-w-4xl mx-auto">
                            <div class="flex items-center justify-center mb-6">
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center">
                                    <i class="fa-solid fa-award text-bronze text-2xl"></i>
                                </div>
                            </div>
                            <h3 class="text-3xl font-bold text-white mb-4">Premium Craftsmanship Since Day One</h3>
                            <p class="text-white/80 text-lg leading-relaxed">
                                Trusted by top architects and discerning clients who demand excellence.
                                <span class="text-bronze font-medium">Experience the difference quality makes.</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div> <!-- Close content-wrapper -->

    <!-- Add Alpine.js script for testimonials -->
    <script>
        function quoteSlider(quotes) {
            return {
                quotes,
                active: 0,
                interval: null,
                init() {
                    this.startAutoplay();
                },
                startAutoplay() {
                    this.interval = setInterval(() => {
                        this.active = (this.active + 1) % this.quotes.length;
                    }, 5000);
                },
                stopAutoplay() {
                    clearInterval(this.interval);
                },
                goTo(index) {
                    this.active = index;
                    this.stopAutoplay();
                },
                getTextSizeClass(text) {
                    const length = text.length;
                    if (length <= 100) {
                        return 'text-2xl sm:text-3xl lg:text-4xl';
                    } else if (length <= 200) {
                        return 'text-xl sm:text-2xl lg:text-3xl';
                    } else if (length <= 300) {
                        return 'text-lg sm:text-xl lg:text-2xl';
                    } else {
                        return 'text-base sm:text-lg lg:text-xl';
                    }
                }
            };
        }
    </script>

    <style>
        /* Elegant content wrapper with cream background */
        .content-wrapper {
            background:
                linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
                radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
            min-height: 100vh;
            position: relative;
        }

        .pattern-overlay {
            background-image:
                linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
            background-size: 80px 80px, 80px 80px, 200px 200px;
            background-position: 0 0, 0 0, 0 0;
            animation: pattern-drift 40s linear infinite;
        }

        @keyframes pattern-drift {
            0% { background-position: 0 0, 0 0, 0 0; }
            100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
        }

        /* Stunning animations */
        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .animate-fade-in-up {
            animation: fade-in-up 1s ease-out forwards;
            opacity: 0;
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        /* Elegant hover effects */
        .hover-lift {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        /* Sophisticated glass morphism */
        .glass-elegant {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Premium gradient text */
        .gradient-text {
            background: linear-gradient(135deg, #cba015 0%, #e6c547 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Elegant scroll indicator */
        @keyframes scroll-indicator {
            0% { opacity: 1; transform: translateY(0); }
            50% { opacity: 0.5; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .scroll-indicator {
            animation: scroll-indicator 2s ease-in-out infinite;
        }

        /* Responsive typography */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 3rem;
                line-height: 1.1;
            }
        }

        /* Elegant transitions */
        * {
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #cba015;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #b8900f;
        }
    </style>

@endsection
