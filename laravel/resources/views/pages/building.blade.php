@extends('layouts.app')

@push('styles')
<style>
    /* Card stacking effect with proper spacing */
    .card-stack-container {
        position: relative;
    }

    .card-section {
        position: sticky;
        top: 0;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-bottom: 100vh; /* This creates the space needed for stacking */
    }

    .card-section:last-child {
        margin-bottom: 0; /* Remove margin from last card */
    }

    /* Z-index stacking order - higher numbers appear on top */
    .card-section:nth-child(1) { z-index: 1; }
    .card-section:nth-child(2) { z-index: 2; }
    .card-section:nth-child(3) { z-index: 3; }
    .card-section:nth-child(4) { z-index: 4; }
    .card-section:nth-child(5) { z-index: 5; }

    .card-bg {
        position: absolute;
        inset: 0;
        z-index: 0;
    }

    .card-bg-1 { background: linear-gradient(135deg, #f3e8d3 0%, #e8d5b7 100%); }
    .card-bg-2 { background: linear-gradient(135deg, #e8e2d5 0%, #d6cfc2 100%); }
    .card-bg-3 { background: linear-gradient(135deg, #d4e6f1 0%, #aed6f1 100%); }
    .card-bg-4 { background: linear-gradient(135deg, #d5e8d4 0%, #a8d8a8 100%); }
    .card-bg-5 { background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%); }

    .card-content {
        position: relative;
        z-index: 10;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 3rem;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 24px;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.4);
    }

    @media (max-width: 768px) {
        .card-content {
            margin: 1rem;
            padding: 2rem;
        }

        .card-section {
            margin-bottom: 50vh; /* Smaller margin on mobile */
        }
    }
</style>
@endpush

@section('content')
<!-- HERO SECTION (Fixed Background) -->
<section class="relative h-screen overflow-hidden">
    @if (!empty($page['acf']['building_image']))
        <img src="{{ $page['acf']['building_image'] }}"
             class="absolute inset-0 w-full h-full object-cover"
             alt="{{ $page['acf']['building_image_alt'] ?? 'KC Baking Powder Building' }}">
    @endif

    <div class="absolute inset-0 bg-gradient-to-r from-black/40 to-transparent"></div>

    <div class="relative z-10 h-full grid grid-cols-1 md:grid-cols-2 w-full">
        <div class="hidden md:block"></div>
        <div class="flex flex-col items-center justify-center p-6 text-center">
            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <h1 class="text-white text-4xl sm:text-6xl mb-4 font-bold tracking-wide drop-shadow-lg">
                    The KC Baking Powder Building
                </h1>
                <div class="w-24 h-1 bg-bronze mx-auto mb-6"></div>
                <p class="text-white/90 text-lg">Historic renovation meets modern craftsmanship</p>
            </div>
        </div>
    </div>
</section>

<!-- CARD STACK CONTAINER -->
<div class="card-stack-container">
    <!-- Card 1: Welcome -->
    <section class="card-section">
        <div class="card-bg card-bg-1"></div>
        <div class="card-content">
            <div class="text-center">
                <h2 class="text-4xl sm:text-5xl mb-8 text-header font-bold">
                    Welcome to the new and future home of Cache River Mill & MetalWorks
                </h2>
                @if($page['acf']['building_section_1_image'])
                    <div class="mb-8 overflow-hidden rounded-xl">
                        <img src="{{ $page['acf']['building_section_1_image'] }}"
                             alt="Building Section 1"
                             class="w-full h-auto object-cover max-h-96 mx-auto">
                    </div>
                @endif
                <p class="text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto">
                    {{ $page['acf']['building_section_1_text'] }}
                </p>
            </div>
        </div>
    </section>

    <!-- Card 2: Restoring History -->
    <section class="card-section">
        <div class="card-bg card-bg-2"></div>
        <div class="card-content">
            <div class="text-center">
                @if($page['acf']['building_section_2_image'])
                    <div class="mb-8 overflow-hidden rounded-xl">
                        <img src="{{ $page['acf']['building_section_2_image'] }}"
                             alt="Building Section 2"
                             class="w-full h-auto object-cover max-h-96 mx-auto">
                    </div>
                @endif
                <h2 class="text-4xl sm:text-5xl mb-8 text-header font-bold italic">
                    Restoring History
                </h2>
                <p class="text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto">
                    {{ $page['acf']['building_section_2_text'] }}
                </p>
            </div>
        </div>
    </section>

    <!-- Card 3: Revitalizing Rose City -->
    <section class="card-section">
        <div class="card-bg card-bg-3"></div>
        <div class="card-content">
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <!-- Images Column -->
                <div class="space-y-6">
                    @if($page['acf']['building_section_3_image'])
                        <div class="overflow-hidden rounded-xl">
                            <img src="{{ $page['acf']['building_section_3_image'] }}"
                                 alt="Building Section 3 Image 1"
                                 class="w-full h-auto object-cover">
                        </div>
                    @endif
                    @if($page['acf']['building_section_3_image_2'])
                        <div class="overflow-hidden rounded-xl">
                            <img src="{{ $page['acf']['building_section_3_image_2'] }}"
                                 alt="Building Section 3 Image 2"
                                 class="w-full h-auto object-cover">
                        </div>
                    @endif
                </div>

                <!-- Text Column -->
                <div class="text-center md:text-left">
                    <h2 class="text-4xl sm:text-5xl mb-8 text-header font-bold italic">
                        Revitalizing Rose City
                    </h2>
                    <p class="text-lg text-gray-700 leading-relaxed">
                        {{ $page['acf']['building_section_3_text'] }}
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Card 4: Next 75 Years -->
    <section class="card-section">
        <div class="card-bg card-bg-4"></div>
        <div class="card-content">
            <div class="text-center">
                <h2 class="text-4xl sm:text-5xl mb-8 text-header font-bold italic">
                    Preparing for the Next 75 Years
                </h2>
                @if($page['acf']['building_section_4_image'])
                    <div class="mb-8 overflow-hidden rounded-xl">
                        <img src="{{ $page['acf']['building_section_4_image'] }}"
                             alt="Building Section 4"
                             class="w-full h-auto object-cover max-h-96 mx-auto">
                    </div>
                @endif
                <p class="text-lg text-gray-700 leading-relaxed max-w-4xl mx-auto italic">
                    {{ $page['acf']['building_section_4_text'] }}
                </p>
            </div>
        </div>
    </section>

    <!-- Card 5: Renovation Highlights -->
    <section class="card-section">
        <div class="card-bg card-bg-5"></div>
        <div class="card-content">
            <div class="text-center mb-12">
                <h2 class="text-4xl sm:text-5xl text-header font-bold italic mb-4">
                    Planned Renovation Highlights
                </h2>
                <div class="w-24 h-1 bg-bronze mx-auto"></div>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                @for ($i = 1; $i <= 3; $i++)
                    <div class="bg-white/30 backdrop-blur-sm rounded-2xl p-6 border border-white/40 text-center">
                        <!-- Image -->
                        <div class="overflow-hidden rounded-xl mb-6">
                            <img class="w-full h-48 object-cover"
                                 src="{{ $page['acf']['building_projection_group']['building_projection_image_' . $i] ?? '/placeholder.jpg' }}"
                                 alt="Building Projection Image {{ $i }}">
                        </div>

                        <!-- Content -->
                        <div class="space-y-4">
                            <div class="w-16 h-1 bg-bronze mx-auto"></div>

                            <h3 class="text-xl text-header font-semibold">
                                {{ $page['acf']['building_projection_group']['building_projection_tag_' . $i] ?? 'Renovation Feature ' . $i }}
                            </h3>

                            <p class="text-sm text-gray-700 font-medium">
                                {{ $page['acf']['building_projection_group']['building_projection_tag_sub_' . $i] ?? 'Feature description ' . $i }}
                            </p>
                        </div>
                    </div>
                @endfor
            </div>
        </div>
    </section>
</div> <!-- Close card-stack-container -->



<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Card stacking effect loaded');
    // The card stacking effect is achieved purely through CSS
    // Each section uses position: sticky and z-index layering
    // As you scroll, sections naturally stack on top of each other
});
</script>

@endsection