@extends('layouts.app')
@section('content')
    <!-- HERO SECTION -->
    <section class="relative h-screen w-full overflow-hidden z-50">
        <!-- Hero Image -->
        <img src="{{ $page['acf']['main_image'] }}"
             alt="About Us"
             class="absolute inset-0 w-full h-full object-cover object-center z-0">

        <!-- Elegant Gradient Overlay -->
        <div class="absolute inset-0 z-10 bg-gradient-to-r from-black/80 via-black/40 to-transparent"></div>

        <!-- Text Content -->
        <div class="relative z-20 h-full flex items-center">
            <div class="pl-16 pr-8">
                <div>
                    <!-- Elegant Typography -->
                    <div class="mb-6">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.2em] mb-4 block">
                            Craftsmanship & Heritage
                        </span>
                        <h1 class="text-6xl sm:text-8xl font-light text-white tracking-tight leading-none mb-6">
                            About
                            <span class="block text-bronze font-normal italic">Our Story</span>
                        </h1>
                    </div>

                    <!-- Elegant Divider -->
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>

                    <!-- Content -->
                    <div class="prose prose-xl prose-white text-white max-w-none mb-10 leading-relaxed">
                        {!! $page['acf']['blurb_1'] ?? '<p>Discover the heritage and craftsmanship that defines our legacy in custom millwork and metalwork.</p>' !!}
                    </div>

                    <!-- Elegant CTA -->
                    <a href="{{ url('contact-us#contact') }}"
                       class="group inline-flex items-center text-bronze hover:text-white transition-all duration-300 text-lg font-medium">
                        <span class="border-b border-bronze group-hover:border-white transition-colors duration-300">
                            Contact Us
                        </span>
                        <svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <!-- Subtle Texture Overlay for Rustic Feel -->
        <div class="absolute inset-0 z-5 opacity-10 mix-blend-overlay"
             style="background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%23000"/><circle cx="80" cy="40" r="1" fill="%23000"/><circle cx="40" cy="80" r="1" fill="%23000"/><circle cx="90" cy="90" r="1" fill="%23000"/><circle cx="10" cy="60" r="1" fill="%23000"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');">
        </div>
    </section>
    <!-- MAIN CONTENT WRAPPER -->
    <div class="content-wrapper relative">
        <!-- Blueprint Background Overlay -->
        <div class="absolute inset-0 blueprint-overlay opacity-60"></div>

        <!-- OUR STORY SECTION -->
        <section class="relative py-20">
            <div class="container mx-auto px-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Section Header -->
                    <div class="text-center mb-16">
                        <h2 class="text-4xl text-header mb-4 tracking-wide">OUR STORY</h2>
                        <div class="w-16 h-px mx-auto bg-bronze"></div>
                    </div>

                    <!-- Content Grid -->
                    <div class="grid lg:grid-cols-2 gap-12 mb-16">
                        <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-bronze/20 shadow-lg">
                            <div class="prose prose-lg text-header max-w-none">
                                {!! $page['acf']['blurb_2'] ?? '<p class="text-gray-700">Our story begins...</p>' !!}
                            </div>
                        </div>

                        <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-bronze/20 shadow-lg">
                            <div class="prose prose-lg text-header max-w-none">
                                {!! $page['acf']['blurb_3'] ?? '<p class="text-gray-700">Our commitment to excellence...</p>' !!}
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image -->
                    <div class="group relative overflow-hidden rounded-2xl bg-white/60 backdrop-blur-sm border border-bronze/20 shadow-lg">
                        <img src="{{ $page['acf']['body_image_1'] }}"
                             alt="About Us"
                             class="w-full h-96 object-cover transition-transform duration-500 group-hover:scale-105">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                </div>
            </div>
        </section>
        <!-- CAPABILITIES SECTION -->
        <section class="relative py-20">
            <div class="container mx-auto px-6">
                <div class="max-w-6xl mx-auto text-center">
                    <!-- Section Header -->
                    <div class="mb-16">
                        <h2 class="text-4xl text-header mb-4 tracking-wide">OUR CAPABILITIES</h2>
                        <div class="w-16 h-px mx-auto bg-bronze"></div>
                    </div>

                    <!-- Table Content -->
                    <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-12 border border-bronze/20 shadow-lg">
                        <div class="prose prose-lg text-header max-w-none">
                            {!! $page['acf']['table_1'] ?? '<p class="text-gray-700">Our capabilities and services...</p>' !!}
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- FAQ SECTION -->
        <section class="relative py-20">
            <div class="container mx-auto px-6">
                <div class="max-w-7xl mx-auto">
                    <!-- Section Header -->
                    <div class="text-center mb-16">
                        <h2 class="text-4xl text-header mb-4 tracking-wide">FREQUENTLY ASKED QUESTIONS</h2>
                        <div class="w-16 h-px mx-auto bg-bronze"></div>
                    </div>

                    <div class="grid lg:grid-cols-2 gap-12 items-start">
                        <!-- FAQ Content -->
                        <div class="w-full">
                            @php
                                $faqItems = [];
                                foreach ($page['acf'] ?? [] as $key => $value) {
                                    if (preg_match('/^faq_(\d+)_question$/', $key, $matches)) {
                                        $index = $matches[1];
                                        $faqItems[] = [
                                            'question' => $value,
                                            'answer' => $page['acf']["faq_{$index}_answer"] ?? '',
                                        ];
                                    }
                                }
                            @endphp
                            @if (!empty($faqItems))
                            <div x-data="{ openIndex: null }" class="w-full">
                                <div class="space-y-4">
                                    @foreach ($faqItems as $i => $item)
                                        <div class="bg-white/60 backdrop-blur-sm rounded-2xl border border-bronze/20 overflow-hidden shadow-lg">
                                            <button @click="openIndex === {{ $i }} ? openIndex = null : openIndex = {{ $i }}"
                                                    class="w-full flex items-center justify-between px-6 py-6 text-left hover:bg-white/80 transition-colors duration-300">
                                                <div class="flex items-center gap-4">
                                                    <span class="text-bronze leading-none">
                                                        <span class="text-2xl font-bold" x-show="openIndex !== {{ $i }}">+</span>
                                                        <span class="text-2xl font-bold" x-show="openIndex === {{ $i }}" x-cloak>−</span>
                                                    </span>
                                                    <span class="text-header font-semibold text-lg">{{ $item['question'] }}</span>
                                                </div>
                                            </button>
                                            <div x-show="openIndex === {{ $i }}"
                                                 x-collapse
                                                 x-cloak
                                                 class="px-6 py-4 border-t border-bronze/20">
                                                <div class="prose prose-lg text-gray-700 max-w-none">
                                                    {!! $item['answer'] !!}
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- FAQ Image -->
                        <div class="w-full">
                            <div class="group relative overflow-hidden rounded-2xl bg-white/60 backdrop-blur-sm border border-bronze/20 shadow-lg">
                                <img src="{{ $page['acf']['body_image_2'] }}"
                                     class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                                     alt="FAQ image">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- OUR BUILDING SECTION -->
        <section class="relative py-20 min-h-screen flex items-center"
                 style="background-image: url('{{ $page['acf']['body_image_3'] }}'); background-size: cover; background-position: center; background-attachment: fixed;">
            <!-- Elegant Overlay -->
            <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/60"></div>

            <div class="container mx-auto px-6 relative z-10">
                <div class="max-w-4xl mx-auto text-center">
                    <!-- Section Header -->
                    <div class="mb-16">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.2em] mb-4 block">
                            Historic Heritage
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-white mb-6 tracking-tight leading-none">
                            Our
                            <span class="block text-bronze font-normal italic">Building</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-12">
                            <div class="w-16 h-px bg-bronze"></div>
                            <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                            <div class="w-32 h-px bg-bronze/50"></div>
                        </div>
                    </div>

                    <!-- Building Content -->
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-12 border border-white/20 shadow-2xl">
                        <div class="prose prose-xl prose-white text-white max-w-none mb-10 leading-relaxed">
                            {!! $page['acf']['blurb_4'] ?? '<p>Discover the rich history of our home in the iconic KC Baking Powder building, where tradition meets modern craftsmanship.</p>' !!}
                        </div>

                        <a href="{{ url('kc-baking-powder-building') }}"
                           class="group inline-flex items-center text-bronze hover:text-white transition-all duration-300 text-lg font-medium">
                            <span class="border-b border-bronze group-hover:border-white transition-colors duration-300">
                                Learn More About Our Building
                            </span>
                            <svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div> <!-- Close content-wrapper -->
    <!-- FINAL CTA SECTION -->
    <section class="relative z-50 bg-gradient-to-r from-header to-charcoal py-24">
        <div class="container mx-auto px-6 text-center">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center justify-center mb-12">
                    <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                </div>

                <!-- Heading -->
                <h1 class="text-5xl font-bold text-white mb-6 tracking-wide">
                    Want to Discuss Your Project?
                </h1>
                <p class="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
                    Let's bring your vision to life with our expert craftsmanship and attention to detail.
                </p>

                <!-- Interactive Button -->
                <div class="flex items-center justify-center">
                    <a href="{{ url('contact-us#form') }}" class="group relative">
                        <div class="circle-enhanced">
                            <div class="circle-content">
                                <img class="w-[80%] transition-transform duration-300 group-hover:scale-110"
                                     src="{{ asset('img/start.png') }}"
                                     alt="Start a Project">
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Content wrapper with elegant cream background */
        .content-wrapper {
            background:
                linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
                radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.02) 0%, transparent 50%);
            min-height: 100vh;
            position: relative;
        }

        .content-wrapper::before {
            content: '';
            position: absolute;
            inset: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
            pointer-events: none;
        }

        .blueprint-overlay {
            background-image:
                linear-gradient(rgba(203, 160, 21, 0.08) 1px, transparent 1px),
                linear-gradient(90deg, rgba(203, 160, 21, 0.08) 1px, transparent 1px),
                linear-gradient(45deg, rgba(139, 69, 19, 0.03) 1px, transparent 1px);
            background-size: 60px 60px, 60px 60px, 120px 120px;
            background-position: 0 0, 0 0, 0 0;
            animation: blueprint-move 30s linear infinite;
        }

        @keyframes blueprint-move {
            0% { background-position: 0 0, 0 0; }
            100% { background-position: 50px 50px, 50px 50px; }
        }

        /* Enhanced glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
        }

        /* Custom prose styling for cream background */
        .prose h1, .prose h2, .prose h3, .prose h4 {
            color: #753307;
            font-weight: 700;
        }

        .prose p {
            color: #2d3748;
            line-height: 1.8;
        }

        .prose strong {
            color: #FFCC00;
        }

        .prose ul li, .prose ol li {
            color: #2d3748;
        }

        .prose table {
            color: #2d3748;
        }

        .prose th {
            color: #FFCC00;
            border-bottom-color: rgba(203, 160, 21, 0.3);
        }

        .prose td {
            border-bottom-color: rgba(45, 55, 72, 0.1);
        }

        /* White text prose for dark backgrounds (hero and building sections) */
        .prose-white p {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .prose-white ul li, .prose-white ol li {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .prose-white h1, .prose-white h2, .prose-white h3, .prose-white h4, .prose-white h5, .prose-white h6 {
            color: #FFCC00 !important;
        }

        .prose-white strong {
            color: #FFCC00 !important;
        }

        .prose-white em {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .prose-white a {
            color: #FFCC00 !important;
        }

        /* Enhanced circle button */
        .circle-enhanced {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FFCC00, #e6c547);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 10px 30px rgba(203, 160, 21, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .circle-enhanced:hover {
            transform: scale(1.05);
            box-shadow:
                0 15px 40px rgba(203, 160, 21, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        .circle-enhanced::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .circle-enhanced:hover::before {
            opacity: 1;
        }

        .circle-content {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
    </style>

@endsection