@extends('layouts.app')

@section('background')
    <div class="fixed inset-0 -z-10">
        <!-- Background Image Watermark -->
        @if (!empty($page['acf']['main_image']))
            <img src="{{ $page['acf']['main_image'] }}"
                 alt="{{ $page['acf']['main_image_alt'] ?? 'Tour background' }}"
                 class="w-full h-full object-cover opacity-15">
        @endif

        <!-- Elegant Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-cream-95 via-cream-90 to-cream-95"></div>

        <!-- Subtle Blueprint Pattern -->
        <div class="absolute inset-0 blueprint-pattern opacity-20"></div>
    </div>
@endsection

@push('styles')
<style>
    /* Elegant cream content background */
    .content-wrapper {
        background:
            linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
            radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
        min-height: 100vh;
        position: relative;
    }

    .pattern-overlay {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
        background-size: 80px 80px, 80px 80px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: pattern-drift 40s linear infinite;
    }

    @keyframes pattern-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
    }

    /* Blueprint pattern for navigation background */
    .blueprint-pattern {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
        background-size: 60px 60px, 60px 60px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: blueprint-drift 30s linear infinite;
    }

    @keyframes blueprint-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 60px 60px, 60px 60px, 200px 200px; }
    }

    /* Cream color definitions */
    .from-cream-95 { background: rgba(250, 248, 243, 0.95); }
    .via-cream-90 { background: rgba(250, 248, 243, 0.9); }
    .to-cream-95 { background: rgba(250, 248, 243, 0.95); }

    .bg-gradient-to-br.from-cream-95.via-cream-90.to-cream-95 {
        background: linear-gradient(to bottom right,
            rgba(250, 248, 243, 0.95) 0%,
            rgba(250, 248, 243, 0.9) 50%,
            rgba(250, 248, 243, 0.95) 100%);
    }
</style>
@endpush

@section('content')
<!-- STUNNING HERO SECTION -->
<section class="relative py-20 min-h-screen flex items-center"
         style="background-image: url('{{ $page['acf']['main_image'] ?? '' }}'); background-size: cover; background-position: center; background-attachment: fixed;">
    <!-- Elegant Overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/60"></div>

    <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Section Header -->
            <div class="mb-16">
                <span class="text-bronze text-sm font-semibold uppercase tracking-[0.2em] mb-4 block">
                    Behind the Scenes
                </span>
                <h1 class="text-5xl sm:text-7xl font-light text-white mb-6 tracking-tight leading-none">
                    Shop
                    <span class="block text-bronze font-normal italic">Tour</span>
                </h1>

                <!-- Elegant Divider -->
                <div class="flex items-center justify-center mb-12">
                    <div class="w-16 h-px bg-bronze"></div>
                    <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                    <div class="w-32 h-px bg-bronze/50"></div>
                </div>
            </div>

            <!-- Tour Content -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-12 border border-white/20 shadow-2xl">
                <div class="prose prose-xl text-white max-w-none mb-10 leading-relaxed">
                    {!! $page['acf']['tour_blurb'] ?? '<p>Experience our state-of-the-art facility and see where craftsmanship meets innovation in every project we create.</p>' !!}
                </div>

                <a href="#form"
                   class="group inline-flex items-center text-bronze hover:text-white transition-all duration-300 text-lg font-medium">
                    <span class="border-b border-bronze group-hover:border-white transition-colors duration-300">
                        Request Your Tour
                    </span>
                    <svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>
<!-- ELEGANT CONTENT WRAPPER -->
<div class="content-wrapper relative">
    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 pattern-overlay opacity-30"></div>
    <!-- ELEGANT GALLERY SECTION -->
    <section class="relative py-24">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Visual Journey
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        Gallery
                        <span class="block text-bronze font-normal italic">Highlights</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>
                </div>

                <!-- Sophisticated Gallery -->
                <div class="bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-bronze/20 shadow-2xl">
                    <div id="custom-controls-gallery" class="relative w-full" data-carousel="slide">
                        <!-- Carousel wrapper -->
                        <div class="relative h-96 md:h-[500px] overflow-hidden rounded-2xl bg-gray-200">
                            @php $hasImages = false; $imageCount = 0; @endphp
                            @for ($i = 1; $i <= 9; $i++)
                                @php
                                    $image = $page['acf']["image_$i"] ?? null;
                                    if ($image) {
                                        $imageCount++;
                                        if (!$hasImages) $hasImages = true;
                                    }
                                @endphp
                                @if ($image)
                                    <div class="{{ $imageCount === 1 ? 'block' : 'hidden' }} duration-700 ease-in-out" data-carousel-item="{{ $imageCount - 1 }}">
                                        <img src="{{ $image }}"
                                             alt="Gallery image {{ $i }}"
                                             class="absolute block w-full h-full object-cover -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" />
                                    </div>
                                @endif
                            @endfor

                            @if (!$hasImages)
                                <div class="flex items-center justify-center h-full">
                                    <p class="text-gray-500">No gallery images available</p>
                                </div>
                            @endif
                        </div>
                        <!-- Elegant Navigation buttons -->
                        <div class="flex justify-center items-center pt-8">
                            <button type="button"
                                    class="flex justify-center items-center me-6 w-12 h-12 bg-bronze/20 hover:bg-bronze/40 rounded-full cursor-pointer group focus:outline-none transition-all duration-300"
                                    data-carousel-prev>
                                <svg class="rtl:rotate-180 w-5 h-5 text-bronze" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                          stroke-width="2" d="M13 5H1m0 0 4 4M1 5l4-4"/>
                                </svg>
                                <span class="sr-only">Previous</span>
                            </button>
                            <button type="button"
                                    class="flex justify-center items-center w-12 h-12 bg-bronze/20 hover:bg-bronze/40 rounded-full cursor-pointer group focus:outline-none transition-all duration-300"
                                    data-carousel-next>
                                <svg class="rtl:rotate-180 w-5 h-5 text-bronze" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                          stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                                </svg>
                                <span class="sr-only">Next</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- ELEGANT FORM SECTION -->
    <section id="form" class="relative py-24">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Schedule Your Visit
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        Request a
                        <span class="block text-bronze font-normal italic">Tour</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>

                    <p class="text-xl text-header/80 max-w-3xl mx-auto leading-relaxed">
                        Want to see our operation in person? Drop us a line below; we would love to show you around our state-of-the-art facility.
                    </p>
                </div>

                <!-- Sophisticated Form Container -->
                <div class="bg-white/80 backdrop-blur-md rounded-3xl p-12 border border-bronze/20 shadow-2xl">
                <form method="POST" action="{{ route('tour.form.submit') }}" class="space-y-6">
                    @csrf
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-header font-semibold mb-2">{{ $page['acf']['form_name_label']}}</label>
                            <input type="text" name="name" placeholder="{{ $page['acf']['form_name'] ?? 'Your Name' }}"
                                   required class="w-full px-4 py-3 bg-white/60 border border-bronze/20 rounded-xl text-header placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300">
                        </div>
                        <div>
                            <label class="block text-header font-semibold mb-2">{{ $page['acf']['form_company_label']}}</label>
                            <input type="text" name="company" placeholder="{{ $page['acf']['form_company'] ?? 'Company Name' }}"
                                   class="w-full px-4 py-3 bg-white/60 border border-bronze/20 rounded-xl text-header placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-header mb-1">
                                {{ $page['acf']['form_industry_label'] ?? 'Industry' }}
                            </label>
                            <select name="industry" required class="w-full px-4 py-2 border border-lightgray rounded focus:outline-none focus:ring-2 focus:ring-bronze">
                                <option value="" disabled selected>Select an industry</option>
                                <option value="architecture">Architecture</option>
                                <option value="general-construction">General Construction</option>
                                <option value="custom-home-builder">Custom Home Builder</option>
                                <option value="interior-design">Interior Design</option>
                                <option value="real-estate">Real Estate</option>
                                <option value="restoration-or-remodeling">Restoration or Remodeling</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-header mb-1">{{ $page['acf']['form_email_label']}}</label>
                            <input type="email" name="date" placeholder="{{ $page['acf']['form_email_label'] ?? 'Your Email' }}"
                                   required class="w-full px-4 py-2 border border-lightgray rounded focus:outline-none focus:ring-2 focus:ring-bronze">
                        </div>
                    </div>
                    <div>
                        <label class="block text-header mb-1">{{ $page['acf']['form_date_label']}}</label>
                        <input type="date" name="date" placeholder="{{ $page['acf']['form_date_label'] ?? 'Desired Tour Date' }}"
                               required class="w-full px-4 py-2 border border-lightgray rounded focus:outline-none focus:ring-2 focus:ring-bronze">
                    </div>
                    <div>
                        <label class="block text-header mb-1">{{ $page['acf']['form_phone_label']}}</label>
                        <input type="tel" name="phone" placeholder="{{ $page['acf']['form_phone_label'] ?? 'Phone Number' }}"
                               required pattern="[0-9()#&+*=.]+" class="w-full px-4 py-2 border border-lightgray rounded focus:outline-none focus:ring-2 focus:ring-bronze">
                    </div>

                    <div>
                        <label class="block text-header mb-1">{{ $page['acf']['form_message_label']}}</label>
                        <textarea name="message" rows="4" placeholder="{{ $page['acf']['form_message_label'] ?? 'Message' }}"
                                  class="w-full px-4 py-2 border border-lightgray rounded focus:outline-none focus:ring-2 focus:ring-bronze"></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="bg-header text-white hover:bg-cream hover:text-header px-6 py-3 rounded tracking-wider">
                            Send
                        </button>
                    </div>
                </form>
                </div>
            </div>
        </div>
    </section>
</div>

    <section id="map" class="py-12">
        <div class="max-w-6xl mx-auto p-6">
            @if (!empty($page['acf']['tour_map']))
                <div class="flex items-center justify-center">
                    <iframe src="{{ $page['acf']['tour_map'] }}"
                            class="w-full h-96 lg:h-[600px] rounded"
                            style="border:0;"
                            allowfullscreen="" loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            @endif
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const carousel = document.getElementById('custom-controls-gallery');
            const items = carousel.querySelectorAll('[data-carousel-item]');
            const prevButton = carousel.querySelector('[data-carousel-prev]');
            const nextButton = carousel.querySelector('[data-carousel-next]');

            let currentIndex = 0;
            const totalItems = items.length;

            if (totalItems === 0) return; // No images to show

            // Function to show specific slide
            function showSlide(index) {
                // Hide all items
                items.forEach(item => {
                    item.classList.add('hidden');
                    item.classList.remove('block');
                });

                // Show current item
                if (items[index]) {
                    items[index].classList.remove('hidden');
                    items[index].classList.add('block');
                }
            }

            // Previous button
            if (prevButton) {
                prevButton.addEventListener('click', function() {
                    currentIndex = (currentIndex - 1 + totalItems) % totalItems;
                    showSlide(currentIndex);
                });
            }

            // Next button
            if (nextButton) {
                nextButton.addEventListener('click', function() {
                    currentIndex = (currentIndex + 1) % totalItems;
                    showSlide(currentIndex);
                });
            }

            // Initialize first slide
            showSlide(0);

            // Optional: Auto-play carousel
            // setInterval(function() {
            //     currentIndex = (currentIndex + 1) % totalItems;
            //     showSlide(currentIndex);
            // }, 5000);
        });
    </script>

@endsection
