@extends('layouts.app')

@section('background')
    <div class="fixed inset-0 -z-10">


        <!-- Subtle Blueprint Pattern -->
        <div class="absolute inset-0 blueprint-pattern opacity-20"></div>
    </div>
@endsection

@push('styles')
<style>
    /* Disable custom cursor on contact page */
    .cursor {
        display: none !important;
    }

    /* Restore normal cursor behavior */
    body, * {
        cursor: auto !important;
    }
    /* Elegant cream content background */
    .contact-hero {
        background:
            linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
            radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
        position: relative;
    }

    .pattern-overlay {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
        background-size: 80px 80px, 80px 80px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: pattern-drift 40s linear infinite;
    }

    @keyframes pattern-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
    }

    /* Stunning hero animations */
    @keyframes hero-float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(2deg); }
    }

    @keyframes pulse-glow {
        0%, 100% { box-shadow: 0 0 20px rgba(203, 160, 21, 0.3); }
        50% { box-shadow: 0 0 40px rgba(203, 160, 21, 0.6); }
    }

    @keyframes text-shimmer {
        0% { background-position: -200% center; }
        100% { background-position: 200% center; }
    }

    .hero-float {
        animation: hero-float 6s ease-in-out infinite;
    }

    .pulse-glow {
        animation: pulse-glow 3s ease-in-out infinite;
    }

    .text-shimmer {
        color: #cba015;
        text-shadow: 0 0 20px rgba(203, 160, 21, 0.5);
    }

    /* Floating elements */
    .floating-element {
        position: absolute;
        animation: hero-float 4s ease-in-out infinite;
    }

    .floating-element:nth-child(2) { animation-delay: -1s; }
    .floating-element:nth-child(3) { animation-delay: -2s; }
    .floating-element:nth-child(4) { animation-delay: -3s; }

    /* Cream color definitions */
    .from-cream-95 {
        background: rgba(250, 248, 243, 0.95);
    }

    .via-cream-90 {
        background: rgba(250, 248, 243, 0.9);
    }

    .to-cream-95 {
        background: rgba(250, 248, 243, 0.95);
    }

    .bg-gradient-to-br.from-cream-95.via-cream-90.to-cream-95 {
        background: linear-gradient(to bottom right,
            rgba(250, 248, 243, 0.95) 0%,
            rgba(250, 248, 243, 0.9) 50%,
            rgba(250, 248, 243, 0.95) 100%);
    }

    /* Fade in up animation */
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fade-in-up 1s ease-out forwards;
        opacity: 0;
    }

    /* Blueprint pattern for navigation background */
    .blueprint-pattern {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
        background-size: 60px 60px, 60px 60px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: blueprint-drift 30s linear infinite;
    }

    @keyframes blueprint-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 60px 60px, 60px 60px, 200px 200px; }
    }

    /* Smooth section transitions */
    .section-transition {
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Gentle fade in for sections */
    .fade-in-section {
        opacity: 0;
        transform: translateY(20px);
        animation: fade-in-section 1.2s ease-out forwards;
    }

    @keyframes fade-in-section {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Smooth gradient transitions */
    .gradient-transition {
        background: linear-gradient(180deg,
            transparent 0%,
            rgba(203, 160, 21, 0.02) 25%,
            rgba(203, 160, 21, 0.05) 50%,
            rgba(203, 160, 21, 0.02) 75%,
            transparent 100%);
    }
</style>
@endpush

@section('content')
<!-- STUNNING HERO SECTION -->
<section id="hero-section" class="relative h-screen overflow-hidden">
    @if (!empty($page['acf']['contact_image']))
        <!-- Background Image with Watermark Effect -->
        <img src="{{ $page['acf']['contact_image'] }}"
             class="absolute inset-0 w-full h-full object-cover object-center z-20"
             alt="{{ $page['acf']['contact_image_alt'] ?? 'Contact image' }}">
    @endif

    <!-- Strong Readability Overlay -->
    <div class="absolute inset-0 z-10 bg-gradient-to-r from-black/85 via-black/60 to-black/75"></div>

    <!-- Additional Text Background -->
    <div class="absolute inset-0 z-15 bg-gradient-to-b from-transparent via-transparent to-black/40"></div>

    <!-- Floating Decorative Elements -->
    <div class="absolute inset-0 z-15 pointer-events-none">
        <div class="floating-element top-20 left-20 w-16 h-16 bg-bronze/20 rounded-full blur-sm"></div>
        <div class="floating-element top-40 right-32 w-12 h-12 bg-bronze/15 rounded-full blur-sm"></div>
        <div class="floating-element bottom-32 left-32 w-20 h-20 bg-bronze/10 rounded-full blur-sm"></div>
        <div class="floating-element bottom-20 right-20 w-14 h-14 bg-bronze/25 rounded-full blur-sm"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-20 h-full flex items-center">
        <div class="container mx-auto px-8">
            <div class="max-w-6xl mx-auto">
                <!-- Enhanced Content Container -->
                <div class="bg-black/40 backdrop-blur-md rounded-3xl p-12 border border-white/20 text-center">
                    <!-- Stunning Typography -->
                    <div class="mb-12">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-6 block animate-fade-in-up">
                            Let's Create Something Amazing
                        </span>
                        <h1 class="text-6xl sm:text-8xl font-light text-white tracking-tight leading-none mb-8 animate-fade-in-up" style="animation-delay: 0.2s">
                            Get In
                            <span class="block text-bronze font-normal italic">Touch</span>
                        </h1>
                    </div>

                    <!-- Elegant Divider with Animation -->
                    <div class="flex items-center justify-center mb-12 animate-fade-in-up" style="animation-delay: 0.4s">
                        <div class="w-20 h-px bg-bronze pulse-glow"></div>
                        <div class="w-4 h-4 bg-bronze rounded-full mx-6 pulse-glow"></div>
                        <div class="w-40 h-px bg-bronze/50"></div>
                    </div>

                    <!-- Compelling Description -->
                    <p class="text-xl sm:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-16 animate-fade-in-up" style="animation-delay: 0.6s">
                        Ready to bring your vision to life? Let's discuss your custom millwork and metalwork project.
                        <span class="text-bronze font-medium block mt-2">We're here to make it extraordinary.</span>
                    </p>

                    <!-- Dynamic CTA Buttons -->
                    <div class="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-12 animate-fade-in-up" style="animation-delay: 0.8s">
                        <a href="#form"
                           class="group inline-flex items-center px-12 py-6 bg-bronze text-header font-bold text-xl rounded-full shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-500 pulse-glow">
                            <span>Start Your Project</span>
                            <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>

                        <a href="tel:{{ $global['acf']['contact_phone'] ?? '' }}"
                           class="group text-white hover:text-bronze transition-colors duration-300 text-xl font-medium">
                            <span class="border-b-2 border-white/30 group-hover:border-bronze transition-colors duration-300">
                                Call Us Now
                            </span>
                        </a>
                    </div>
                </div>

            </div>

            <!-- Contact Info Preview - Outside the main container -->
            <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-fade-in-up" style="animation-delay: 1s">
                <div class="text-center hero-float">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                        <i class="fa-solid fa-phone text-bronze text-xl"></i>
                    </div>
                    <p class="text-white font-semibold">{{ $global['acf']['contact_phone'] ?? 'Call Us' }}</p>
                </div>
                <div class="text-center hero-float" style="animation-delay: -2s">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                        <i class="fa-solid fa-envelope text-bronze text-xl"></i>
                    </div>
                    <p class="text-white font-semibold">{{ $global['acf']['contact_email'] ?? 'Email Us' }}</p>
                </div>
                <div class="text-center hero-float" style="animation-delay: -4s">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/30">
                        <i class="fa-solid fa-map-marker-alt text-bronze text-xl"></i>
                    </div>
                    <p class="text-white font-semibold">North Little Rock, AR</p>
                </div>
            </div>
            </div>
        </div>
</section>

<!-- ELEGANT CONTENT WRAPPER -->
<div class="contact-hero relative">
    <!-- Subtle Pattern Overlay -->
    <!-- CONTACT FORM SECTION -->
    <section id="form" class="relative py-32 fade-in-section section-transition">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Ready to Start?
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        Let's
                        <span class="block text-bronze font-normal italic">Connect</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>
                </div>

                <!-- PREMIUM CONTACT SHOWCASE -->
                <div class="grid lg:grid-cols-3 gap-12">
                    <!-- Left: Enhanced Contact Info -->
                    <div class="lg:col-span-1 space-y-8">
                        <!-- Premium Hours Card -->
                        @if (!empty($page['acf']['hours_mon_thurs']) || !empty($page['acf']['hours_friday']))
                            <div class="group relative bg-gradient-to-br from-white/90 to-white/80 backdrop-blur-md rounded-3xl p-12 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-700 hover:transform hover:-translate-y-2">

                                <div class="relative z-10">
                                    <div class="flex items-center mb-8">
                                        <i class="fa-regular fa-clock text-bronze text-3xl mr-6"></i>
                                        <h3 class="text-3xl font-bold text-header">Workshop Hours</h3>
                                    </div>
                                    <div class="space-y-4">
                                        @if (!empty($page['acf']['hours_mon_thurs']))
                                            <div class="flex items-center justify-between p-4 bg-bronze/10 rounded-2xl">
                                                <span class="text-header font-semibold">Mon - Thu</span>
                                                <span class="text-bronze font-bold">{{ $page['acf']['hours_mon_thurs'] }}</span>
                                            </div>
                                        @endif
                                        @if (!empty($page['acf']['hours_friday']))
                                            <div class="flex items-center justify-between p-4 bg-bronze/10 rounded-2xl">
                                                <span class="text-header font-semibold">Friday</span>
                                                <span class="text-bronze font-bold">{{ $page['acf']['hours_friday'] }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Premium Contact Info Card -->
                        @if (!empty($global['acf']['contact_phone']) || !empty($global['acf']['contact_email']))
                            <div class="group relative bg-gradient-to-br from-white/90 to-white/80 backdrop-blur-md rounded-3xl p-12 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-700 hover:transform hover:-translate-y-2">

                                <div class="relative z-10">
                                    <div class="flex items-center mb-8">
                                        <i class="fa-solid fa-phone text-bronze text-3xl mr-6"></i>
                                        <h3 class="text-3xl font-bold text-header">Get In Touch</h3>
                                    </div>
                                    <div class="space-y-6">
                                        @if (!empty($global['acf']['contact_phone']))
                                            <a href="tel:{{ $global['acf']['contact_phone'] }}" class="group/link flex items-center p-4 bg-bronze/10 rounded-2xl hover:bg-bronze/20 transition-colors duration-300">
                                                <i class="fa-solid fa-phone text-bronze text-xl mr-4"></i>
                                                <div>
                                                    <span class="text-header/60 text-sm font-medium block">Call Us</span>
                                                    <span class="text-header font-bold text-lg group-hover/link:text-bronze transition-colors duration-300">{{ $global['acf']['contact_phone'] }}</span>
                                                </div>
                                            </a>
                                        @endif
                                        @if (!empty($global['acf']['contact_email']))
                                            <a href="mailto:{{ $global['acf']['contact_email'] }}" class="group/link flex items-center p-4 bg-bronze/10 rounded-2xl hover:bg-bronze/20 transition-colors duration-300">
                                                <i class="fa-solid fa-envelope text-bronze text-xl mr-4"></i>
                                                <div>
                                                    <span class="text-header/60 text-sm font-medium block">Email Us</span>
                                                    <span class="text-header font-bold text-lg group-hover/link:text-bronze transition-colors duration-300">{{ $global['acf']['contact_email'] }}</span>
                                                </div>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Right: Spectacular Contact Form -->
                    <div class="lg:col-span-2">
                        <div class="group relative bg-gradient-to-br from-white/95 to-white/85 backdrop-blur-md rounded-3xl p-16 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-700">

                            <div class="relative z-10">
                                <!-- Premium Form Header -->
                                <div class="text-center mb-12">
                                    <i class="fa-solid fa-paper-plane text-bronze text-4xl mb-6 block"></i>
                                    <h3 class="text-4xl font-bold text-header mb-4">Start Your Project</h3>
                                    <p class="text-header/70 text-xl leading-relaxed max-w-2xl mx-auto">
                                        Ready to bring your vision to life? Share your project details and we'll craft a custom solution that exceeds your expectations.
                                    </p>
                                </div>

                                <!-- Success Message -->
                                @if(session('success'))
                                    <div id="success-message" class="mb-8 p-6 bg-green-50 border-2 border-green-200 rounded-2xl shadow-lg animate-pulse">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 class="text-green-800 font-bold text-lg">Message Sent Successfully!</h4>
                                                <p class="text-green-700">{{ session('success') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Error Messages -->
                                @if($errors->any())
                                    <div class="mb-8 p-6 bg-red-50 border-2 border-red-200 rounded-2xl shadow-lg">
                                        <div class="flex items-center space-x-3 mb-3">
                                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </div>
                                            <h4 class="text-red-800 font-bold text-lg">Please fix the following errors:</h4>
                                        </div>
                                        <ul class="list-disc list-inside text-red-700 space-y-1">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <!-- Premium Contact Form -->
                                <form method="POST" action="{{ route('contact.form.submit') }}" class="space-y-8">
                                    @csrf

                                    <!-- Name & Email Row -->
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-8">
                                        <div class="group">
                                            <label class="block text-lg font-bold text-header mb-4 group-focus-within:text-bronze transition-colors duration-300">
                                                <i class="fa-solid fa-user text-bronze mr-3"></i>
                                                Full Name
                                            </label>
                                            <input type="text" name="name"
                                                   placeholder="{{ $page['acf']['form_name_placeholder'] ?? 'Enter your full name' }}"
                                                   required
                                                   class="w-full px-6 py-5 bg-white/90 border-2 border-bronze/30 rounded-2xl focus:ring-4 focus:ring-bronze/30 focus:border-bronze transition-all duration-500 text-lg placeholder-gray-400 shadow-lg hover:shadow-xl">
                                        </div>
                                        <div class="group">
                                            <label class="block text-lg font-bold text-header mb-4 group-focus-within:text-bronze transition-colors duration-300">
                                                <i class="fa-solid fa-envelope text-bronze mr-3"></i>
                                                Email Address
                                            </label>
                                            <input type="email" name="email"
                                                   placeholder="{{ $page['acf']['form_email_placeholder'] ?? '<EMAIL>' }}"
                                                   required
                                                   class="w-full px-6 py-5 bg-white/90 border-2 border-bronze/30 rounded-2xl focus:ring-4 focus:ring-bronze/30 focus:border-bronze transition-all duration-500 text-lg placeholder-gray-400 shadow-lg hover:shadow-xl">
                                        </div>
                                    </div>

                                    <!-- Phone -->
                                    <div class="group">
                                        <label class="block text-lg font-bold text-header mb-4 group-focus-within:text-bronze transition-colors duration-300">
                                            <i class="fa-solid fa-phone text-bronze mr-3"></i>
                                            Phone Number
                                        </label>
                                        <input type="tel" name="phone"
                                               placeholder="{{ $page['acf']['form_phone_placeholder'] ?? '(*************' }}"
                                               required pattern="[0-9()#&+*=.-]+"
                                               class="w-full px-6 py-5 bg-white/90 border-2 border-bronze/30 rounded-2xl focus:ring-4 focus:ring-bronze/30 focus:border-bronze transition-all duration-500 text-lg placeholder-gray-400 shadow-lg hover:shadow-xl">
                                    </div>

                                    <!-- Message -->
                                    <div class="group">
                                        <label class="block text-lg font-bold text-header mb-4 group-focus-within:text-bronze transition-colors duration-300">
                                            <i class="fa-solid fa-comment-dots text-bronze mr-3"></i>
                                            Project Details
                                        </label>
                                        <textarea name="message" rows="6"
                                                  placeholder="{{ $page['acf']['form_message_placeholder'] ?? 'Tell us about your vision, timeline, budget, and any specific requirements...' }}"
                                                  class="w-full px-6 py-5 bg-white/90 border-2 border-bronze/30 rounded-2xl focus:ring-4 focus:ring-bronze/30 focus:border-bronze transition-all duration-500 text-lg placeholder-gray-400 resize-none shadow-lg hover:shadow-xl"></textarea>
                                    </div>

                                    <!-- Premium Submit Button -->
                                    <div class="text-center pt-8">
                                        <button type="submit"
                                                class="group inline-flex items-center px-16 py-6 bg-gradient-to-r from-bronze to-bronze/90 text-white font-bold text-xl rounded-2xl hover:from-bronze/90 hover:to-bronze transition-all duration-500 transform hover:-translate-y-3 shadow-2xl hover:shadow-3xl">
                                            <i class="fa-solid fa-paper-plane mr-4 group-hover:translate-x-1 transition-transform duration-300"></i>
                                            <span>{{ $page['acf']['form_submit_text'] ?? 'Send Project Inquiry' }}</span>
                                            <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                            </svg>
                                        </button>

                                        <p class="text-header/60 text-lg mt-8 max-w-md mx-auto leading-relaxed">
                                            We typically respond within 24 hours. For urgent inquiries, please call us directly.
                                        </p>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                </div>
            </div>
    </section>

    <!-- SMOOTH SECTION TRANSITION -->
    <div class="relative">
        <!-- Elegant Gradient Bridge -->
        <div class="h-32 gradient-transition"></div>

        <!-- Decorative Divider -->
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="flex items-center opacity-60">
                <div class="w-24 h-px bg-bronze/40"></div>
                <div class="w-4 h-4 bg-bronze/50 rounded-full mx-8 animate-pulse"></div>
                <div class="w-24 h-px bg-bronze/40"></div>
            </div>
        </div>
    </div>
<div style="position:relative;">
    <img src="{{ asset('img/bg.jpg') }}"
         style="position:absolute; inset:0; width:100%; height:100%; object-fit:cover; opacity:0.1; z-index:-1;"
         alt="" />
    <div style="position:relative; z-index:1;">
    <!-- MAP & LOCATION SECTION -->
    <section id="map" class="relative py-32 fade-in-section section-transition" style="animation-delay: 0.3s">

        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                @if (!empty($page['acf']['visit_heading']) || !empty($global['acf']['visit_address']) || !empty($page['acf']['google_map_embed_url']))
                    <!-- Elegant Section Header -->
                    <div class="text-center mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                            Visit Our Shop
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                            Find
                            <span class="block text-bronze font-normal italic">Us</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="w-16 h-px bg-bronze"></div>
                            <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                            <div class="w-32 h-px bg-bronze/50"></div>
                        </div>
                    </div>

                    <div class="grid lg:grid-cols-2 gap-12 items-center">
                        <!-- Map -->
                        @if (!empty($page['acf']['google_map_embed_url']))
                            <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-6 border border-bronze/20 shadow-xl overflow-hidden">
                                <iframe src="{{ $page['acf']['google_map_embed_url'] }}"
                                        class="w-full h-96 lg:h-[500px] rounded-xl"
                                        style="border:0;"
                                        allowfullscreen="" loading="lazy"
                                        referrerpolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                        @endif

                        <!-- Address Info -->
                        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-10 border border-bronze/20 shadow-xl text-center lg:text-left">
                            <div class="flex items-center justify-center lg:justify-start mb-6">
                                <i class="fa-regular fa-eye text-bronze text-3xl mr-4"></i>
                                <h3 class="text-3xl font-bold text-header">Find Us</h3>
                            </div>

                            @if (!empty($global['acf']['visit_address']))
                                <div class="mb-6">
                                    <h4 class="text-xl font-semibold text-bronze mb-3 flex items-center justify-center lg:justify-start">
                                        <i class="fa-solid fa-map-marker-alt mr-2"></i>
                                        Address
                                    </h4>
                                    <p class="text-gray-700 text-lg leading-relaxed whitespace-pre-line">{{ $global['acf']['visit_address'] }}</p>
                                </div>
                            @endif

                            <div class="pt-6 border-t border-bronze/20">
                                <p class="text-gray-600 text-sm">
                                    Located in the historic KC Baking Powder building in North Little Rock, AR
                                </p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </section>
    </div>
</div>

    <!-- ELEGANT CLOSING TRANSITION -->
    <div class="relative">
        <!-- Smooth Fade Out -->
        <div class="h-20 bg-gradient-to-b from-transparent to-bronze/10"></div>
    </div>
</div> <!-- Close contact-hero wrapper -->

@endsection

@push('scripts')
<script>
    // Completely disable and remove custom cursor on contact page
    document.addEventListener('DOMContentLoaded', function() {
        // Function to remove all cursor elements
        function removeCursorElements() {
            // Remove all possible cursor elements
            const cursorSelectors = [
                '#js-cursor',
                '.cursor',
                '.cursor__follower',
                '.cursor__wrapper',
                '[data-cursor]',
                '[class*="cursor"]'
            ];

            cursorSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (el) {
                        el.remove();
                    }
                });
            });
        }

        // Remove cursor elements immediately
        removeCursorElements();

        // Disable cursor functionality
        if (window.theme && window.theme.cursorFollower) {
            window.theme.cursorFollower.enabled = false;
        }

        // Remove cursor classes from body and restore normal cursor
        document.body.classList.remove('cursor-none', 'cursor-progress');
        document.body.style.cursor = 'auto';
        document.documentElement.style.cursor = 'auto';

        // Override any cursor styles
        const style = document.createElement('style');
        style.textContent = `
            body, body *, html, html * {
                cursor: auto !important;
            }
            .cursor, #js-cursor, [class*="cursor"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                pointer-events: none !important;
            }
        `;
        document.head.appendChild(style);

        // Remove cursor elements again after a delay (in case they're added dynamically)
        setTimeout(removeCursorElements, 100);
        setTimeout(removeCursorElements, 500);
        setTimeout(removeCursorElements, 1000);
    });

    // Also disable on window load
    window.addEventListener('load', function() {
        document.querySelectorAll('.cursor, #js-cursor, [class*="cursor"]').forEach(el => {
            if (el) el.remove();
        });

        // Scroll to success message if present
        @if(session('success'))
            const successMessage = document.getElementById('success-message');
            if (successMessage) {
                successMessage.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                // Remove pulse animation after 3 seconds
                setTimeout(() => {
                    successMessage.classList.remove('animate-pulse');
                }, 3000);
            }
        @endif
    });
</script>
@endpush
