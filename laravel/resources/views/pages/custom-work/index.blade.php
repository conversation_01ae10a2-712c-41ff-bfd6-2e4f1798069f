@extends('layouts.app')
@section('content')
    <section class="relative w-full h-screen overflow-hidden border-b border-header">
        {{-- Fullscreen Background Image --}}
        <img rel="preload" loading="eager" src="{{ $page['acf']['main_image'] }}"
             class="absolute inset-0 w-full h-full object-cover z-0" alt="Hero"/>
        {{-- Gradient Overlay --}}
        <div class="absolute inset-0 z-10 bg-gradient-to-b from-header to-bronze opacity-40"></div>
        {{-- Text overlay --}}
        <div class="absolute inset-0 z-20 flex flex-col items-start justify-center p-4 sm:p-6 md:p-8 space-y-1 sm:space-y-2">
            <div class=" font-sans text-white">
                <h2 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl drop-shadow-lg pb-6">
                    CUSTOM WORK
                </h2>
                <p class="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-light drop-shadow-lg">
                    YOUR VISION.
                </p>
                <p class="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-light drop-shadow-lg">
                    OUR CRAFTSMANSHIP.
                </p>
            </div>
        </div>
    </section>

    <!-- ELEGANT CONTENT WRAPPER -->
    <div class="content-wrapper relative">
        <!-- Subtle Pattern Overlay -->
        <div class="absolute inset-0 pattern-overlay opacity-30"></div>

        <section class="relative py-24">
            <div class="container mx-auto px-6">
                <div class="max-w-5xl mx-auto text-center">
                    <!-- Elegant Section Header -->
                    <div class="mb-20">
                        <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                            Our Philosophy
                        </span>
                        <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                            Our
                            <span class="block text-bronze font-normal italic">Approach</span>
                        </h2>

                        <!-- Elegant Divider -->
                        <div class="flex items-center justify-center mb-8">
                            <div class="w-16 h-px bg-bronze"></div>
                            <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                            <div class="w-32 h-px bg-bronze/50"></div>
                        </div>
                    </div>

                    <!-- Sophisticated Content Card -->
                    <div class="bg-white/80 backdrop-blur-md rounded-3xl p-16 border border-bronze/20 shadow-2xl min-h-[400px] flex items-center justify-center">
                        <div class="text-center">
                            <div class="prose prose-xl text-header max-w-none text-center leading-relaxed mb-12">
                                {!! $page['acf']['blurb_1'] ?? '<p class="text-gray-700">Our approach to custom work combines traditional craftsmanship with modern innovation...</p>' !!}
                            </div>

                            <!-- Elegant CTA -->
                            <div>
                                <a href="{{ url('contact-us#form') }}"
                                   class="group inline-flex items-center text-bronze hover:text-header transition-all duration-500 text-xl font-medium">
                                    <span class="border-b-2 border-bronze group-hover:border-header transition-colors duration-500">
                                        Start Your Project
                                    </span>
                                    <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <section class="relative py-24 bg-gradient-to-br from-cream-light via-cream to-cream-dark" id="services">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Our Services
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        What We
                        <span class="block text-bronze font-normal italic">Can Do</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>
                </div>

                <!-- Sophisticated Services Grid -->
                <div class="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                    @foreach($customPages as $pageItem)
                        <a href="{{ url('/' . $pageItem['slug']) }}"
                           class="group bg-white/80 backdrop-blur-md rounded-3xl border border-bronze/20 overflow-hidden hover:bg-white/90 transition-all duration-500 hover:transform hover:-translate-y-4 hover:shadow-2xl">

                            {{-- Image Section --}}
                            <div class="relative overflow-hidden">
                                <img src="{{ $pageItem['_embedded']['wp:featuredmedia'][0]['source_url'] ?? '/placeholder.jpg' }}"
                                     loading="lazy"
                                     alt="{{ $pageItem['title']['rendered'] }}"
                                     class="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110" />
                                <div class="absolute inset-0 bg-gradient-to-t from-bronze/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            </div>

                            {{-- Content Section --}}
                            <div class="p-8">
                                <h5 class="text-2xl font-bold text-header mb-4 group-hover:text-bronze transition-colors duration-300">
                                    {!! $pageItem['title']['rendered'] !!}
                                </h5>
                                <p class="text-gray-700 leading-relaxed line-clamp-3 mb-6">
                                    {!! Str::limit(strip_tags($pageItem['excerpt']['rendered'] ?? ''), 120) !!}
                                </p>

                                <!-- Learn More Arrow -->
                                <div class="flex items-center text-bronze opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
                                    <span class="text-sm font-semibold">Learn More</span>
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    <section class="relative py-24">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Elegant Section Header -->
                <div class="text-center mb-20">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-4 block">
                        Getting Started
                    </span>
                    <h2 class="text-5xl sm:text-7xl font-light text-header mb-6 tracking-tight leading-none">
                        How To
                        <span class="block text-bronze font-normal italic">Get Started</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-8">
                        <div class="w-16 h-px bg-bronze"></div>
                        <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                        <div class="w-32 h-px bg-bronze/50"></div>
                    </div>
                </div>

                @if (!empty($posts) && is_array($posts))
                    <!-- Sophisticated Blog Posts Grid -->
                    <div class="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        @foreach ($posts as $post)
                            @php
                                $title = $post['title']['rendered'] ?? 'Untitled';
                                $excerpt = Str::limit(strip_tags($post['excerpt']['rendered'] ?? ''), 100);
                                $thumbnail = $post['_embedded']['wp:featuredmedia'][0]['source_url'] ?? null;
                                $slug = $post['slug'] ?? '#';
                                $date = !empty($post['date']) ? \Carbon\Carbon::parse($post['date'])->format('F j, Y') : '';
                            @endphp

                            <a href="{{ url('/blog/' . $slug) }}"
                               class="group bg-white/80 backdrop-blur-md rounded-3xl border border-bronze/20 overflow-hidden hover:bg-white/90 transition-all duration-500 hover:transform hover:-translate-y-4 hover:shadow-2xl">

                                @if ($thumbnail)
                                    <div class="relative overflow-hidden">
                                        <img loading="lazy"
                                             src="{{ $thumbnail }}"
                                             alt="{{ strip_tags($title) }}"
                                             class="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-110">
                                        <div class="absolute inset-0 bg-gradient-to-t from-bronze/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                    </div>
                                @endif

                                <div class="p-8">
                                    <!-- Date -->
                                    <div class="mb-4">
                                        <span class="text-bronze text-sm font-semibold uppercase tracking-wider">{!! $date !!}</span>
                                    </div>

                                    <!-- Title -->
                                    <h3 class="text-xl font-bold text-header mb-4 group-hover:text-bronze transition-colors duration-300">
                                        {!! $title !!}
                                    </h3>

                                    <!-- Excerpt -->
                                    <p class="text-gray-700 leading-relaxed mb-6">
                                        {!! $excerpt !!}
                                    </p>

                                    <!-- Read More -->
                                    <div class="flex items-center text-bronze opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
                                        <span class="text-sm font-semibold">Read More</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                        </svg>
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </section>
    </div> <!-- Close content-wrapper -->

    <!-- FINAL CTA SECTION -->
    <section class="relative z-50 bg-gradient-to-r from-header to-charcoal py-24">
        <div class="container mx-auto px-6 text-center">
            <div class="max-w-4xl mx-auto">
                <!-- Divider Line -->
                <div class="w-24 h-1 bg-bronze mx-auto mb-8"></div>

                <!-- Heading -->
                <h1 class="text-5xl font-bold text-white mb-6 tracking-wide">
                    Want to Discuss Your Project?
                </h1>
                <p class="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
                    Let's bring your vision to life with our expert craftsmanship and attention to detail.
                </p>

                <!-- Interactive Button -->
                <div class="flex items-center justify-center">
                    <a href="{{ url('contact-us#form') }}" class="group relative">
                        <div class="circle-enhanced">
                            <div class="circle-content">
                                <img class="w-[80%] transition-transform duration-300 group-hover:scale-110"
                                     src="{{ asset('img/start.png') }}"
                                     alt="Start a Project">
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Elegant content wrapper with cream background */
        .content-wrapper {
            background:
                linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
                radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
            min-height: 100vh;
            position: relative;
        }

        .pattern-overlay {
            background-image:
                linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
                radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
            background-size: 80px 80px, 80px 80px, 200px 200px;
            background-position: 0 0, 0 0, 0 0;
            animation: pattern-drift 40s linear infinite;
        }

        @keyframes pattern-drift {
            0% { background-position: 0 0, 0 0, 0 0; }
            100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
        }

        /* Cream color variations */
        .from-cream-light { background: #fcfaf7; }
        .via-cream { background: #faf8f3; }
        .to-cream-dark { background: #f0ede4; }

        .bg-gradient-to-br.from-cream-light.via-cream.to-cream-dark {
            background: linear-gradient(to bottom right, #fcfaf7 0%, #faf8f3 50%, #f0ede4 100%);
        }

        /* Enhanced glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
        }

        /* Custom prose styling for cream background */
        .prose h1, .prose h2, .prose h3, .prose h4 {
            color: #cba015;
            font-weight: 700;
        }

        .prose p {
            color: #2d3748;
            line-height: 1.8;
        }

        .prose strong {
            color: #cba015;
        }

        .prose ul li, .prose ol li {
            color: #2d3748;
        }

        /* Enhanced circle button */
        .circle-enhanced {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: linear-gradient(45deg, #FFCC00, #e6c547);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 10px 30px rgba(203, 160, 21, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .circle-enhanced:hover {
            transform: scale(1.05);
            box-shadow:
                0 15px 40px rgba(203, 160, 21, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        .circle-enhanced::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .circle-enhanced:hover::before {
            opacity: 1;
        }

        .circle-content {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
    </style>

@endsection
