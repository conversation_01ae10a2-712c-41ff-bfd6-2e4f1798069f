@extends('layouts.app')

@section('background')
    <div class="fixed inset-0 -z-10">
        <!-- Background Image Watermark -->
        @if (!empty($page['acf']['main_image']))
            <img src="{{ $page['acf']['main_image'] }}"
                 alt="{{ $page['acf']['main_image_alt'] ?? 'Professionals background' }}"
                 class="w-full h-full object-cover opacity-15">
        @endif

        <!-- Elegant Overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-cream-95 via-cream-90 to-cream-95"></div>

        <!-- Subtle Blueprint Pattern -->
        <div class="absolute inset-0 blueprint-pattern opacity-20"></div>
    </div>
@endsection

@push('styles')
<style>
    /* Elegant cream content background */
    .content-wrapper {
        background:
            linear-gradient(135deg, #faf8f3 0%, #f5f2eb 50%, #f0ede4 100%),
            radial-gradient(circle at 30% 70%, rgba(203, 160, 21, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(139, 69, 19, 0.015) 0%, transparent 50%);
        min-height: 100vh;
        position: relative;
    }

    .pattern-overlay {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.05) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.02) 0%, transparent 50%);
        background-size: 80px 80px, 80px 80px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: pattern-drift 40s linear infinite;
    }

    @keyframes pattern-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 80px 80px, 80px 80px, 200px 200px; }
    }

    /* Blueprint pattern for navigation background */
    .blueprint-pattern {
        background-image:
            linear-gradient(rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(203, 160, 21, 0.1) 1px, transparent 1px),
            radial-gradient(circle at 25% 25%, rgba(203, 160, 21, 0.05) 0%, transparent 50%);
        background-size: 60px 60px, 60px 60px, 200px 200px;
        background-position: 0 0, 0 0, 0 0;
        animation: blueprint-drift 30s linear infinite;
    }

    @keyframes blueprint-drift {
        0% { background-position: 0 0, 0 0, 0 0; }
        100% { background-position: 60px 60px, 60px 60px, 200px 200px; }
    }

    /* Cream color definitions */
    .from-cream-95 { background: rgba(250, 248, 243, 0.95); }
    .via-cream-90 { background: rgba(250, 248, 243, 0.9); }
    .to-cream-95 { background: rgba(250, 248, 243, 0.95); }

    .bg-gradient-to-br.from-cream-95.via-cream-90.to-cream-95 {
        background: linear-gradient(to bottom right,
            rgba(250, 248, 243, 0.95) 0%,
            rgba(250, 248, 243, 0.9) 50%,
            rgba(250, 248, 243, 0.95) 100%);
    }

    /* Custom prose styling for cream background */
    .prose h1, .prose h2, .prose h3, .prose h4 {
        color: #cba015;
        font-weight: 700;
    }

    .prose p {
        color: #2d3748;
        line-height: 1.8;
    }

    .prose strong {
        color: #cba015;
    }

    .prose ul li, .prose ol li {
        color: #2d3748;
    }
</style>
@endpush

@section('content')
<!-- STUNNING HERO SECTION -->
<section class="relative py-20 min-h-screen flex items-center"
         style="background-image: url('{{ $page['acf']['main_image'] ?? '' }}'); background-size: cover; background-position: center; background-attachment: fixed;">
    <!-- Elegant Overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-black/60"></div>

    <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Section Header -->
            <div class="mb-16">
                <span class="text-bronze text-sm font-semibold uppercase tracking-[0.2em] mb-4 block">
                    Industry Partners
                </span>
                <h1 class="text-5xl sm:text-7xl font-light text-white mb-6 tracking-tight leading-none">
                    For
                    <span class="block text-bronze font-normal italic">Professionals</span>
                </h1>

                <!-- Elegant Divider -->
                <div class="flex items-center justify-center mb-12">
                    <div class="w-16 h-px bg-bronze"></div>
                    <div class="w-2 h-2 bg-bronze rounded-full mx-4"></div>
                    <div class="w-32 h-px bg-bronze/50"></div>
                </div>
            </div>

            <!-- Professional Content -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-12 border border-white/20 shadow-2xl">
                <div class="prose prose-xl max-w-none mb-10 leading-relaxed">
                    <p class="!text-white">Partner with us for exceptional custom millwork and metalwork solutions. We work with architects, designers, contractors, and industry professionals to bring extraordinary visions to life.</p>
                </div>

                <a href="#content"
                   class="group inline-flex items-center text-bronze hover:text-white transition-all duration-300 text-lg font-medium">
                    <span class="border-b border-bronze group-hover:border-white transition-colors duration-300">
                        Learn More About Our Services
                    </span>
                    <svg class="w-5 h-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- ELEGANT CONTENT WRAPPER -->
<div class="content-wrapper relative">
    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 pattern-overlay opacity-30"></div>

    <!-- LUXURY PROFESSIONAL SHOWCASE -->
    <section id="content" class="relative py-32">
        <div class="container mx-auto px-6">
            <div class="max-w-7xl mx-auto">
                <!-- Spectacular Section Header -->
                <div class="text-center mb-24">
                    <span class="text-bronze text-sm font-semibold uppercase tracking-[0.3em] mb-6 block">
                        Elite Professional Services
                    </span>
                    <h2 class="text-6xl sm:text-8xl font-light text-header mb-8 tracking-tight leading-none">
                        Unparalleled
                        <span class="block text-bronze font-normal italic">Craftsmanship</span>
                    </h2>

                    <!-- Elegant Divider -->
                    <div class="flex items-center justify-center mb-12">
                        <div class="w-24 h-px bg-bronze"></div>
                        <div class="w-4 h-4 bg-bronze rounded-full mx-6"></div>
                        <div class="w-48 h-px bg-bronze/50"></div>
                    </div>

                    <p class="text-2xl text-header/80 max-w-4xl mx-auto leading-relaxed">
                        Trusted by the world's most discerning architects and designers for projects that demand absolute perfection.
                    </p>
                </div>

                <!-- Premium Services Grid -->
                <div class="grid md:grid-cols-3 gap-12 mb-24">
                    <!-- Architectural Millwork -->
                    <div class="group bg-white/90 backdrop-blur-md rounded-3xl p-10 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:transform hover:-translate-y-4">
                        <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-8 group-hover:bg-bronze/30 transition-colors duration-300">
                            <i class="fa-solid fa-drafting-compass text-bronze text-3xl"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-header mb-6 text-center">Architectural Millwork</h3>
                        <p class="text-gray-700 text-lg leading-relaxed text-center">
                            Precision-crafted millwork that transforms architectural visions into stunning reality. From intricate moldings to custom cabinetry.
                        </p>
                    </div>

                    <!-- Custom Metalwork -->
                    <div class="group bg-white/90 backdrop-blur-md rounded-3xl p-10 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:transform hover:-translate-y-4">
                        <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-8 group-hover:bg-bronze/30 transition-colors duration-300">
                            <i class="fa-solid fa-hammer text-bronze text-3xl"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-header mb-6 text-center">Custom Metalwork</h3>
                        <p class="text-gray-700 text-lg leading-relaxed text-center">
                            Artisan metalwork that combines traditional techniques with modern innovation. Every piece is a masterwork of form and function.
                        </p>
                    </div>

                    <!-- Design Collaboration -->
                    <div class="group bg-white/90 backdrop-blur-md rounded-3xl p-10 border border-bronze/30 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:transform hover:-translate-y-4">
                        <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-8 group-hover:bg-bronze/30 transition-colors duration-300">
                            <i class="fa-solid fa-users text-bronze text-3xl"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-header mb-6 text-center">Design Collaboration</h3>
                        <p class="text-gray-700 text-lg leading-relaxed text-center">
                            Seamless partnership from concept to completion. We work hand-in-hand with your team to exceed expectations.
                        </p>
                    </div>
                </div>

                <!-- Luxury Content Showcase -->
                <div class="grid lg:grid-cols-2 gap-16 items-center mb-24">
                    <!-- Premium Content -->
                    <div class="space-y-8">
                        <div class="bg-white/90 backdrop-blur-md rounded-3xl p-12 border border-bronze/30 shadow-2xl">
                            <div class="flex items-start space-x-6 mb-8">
                                <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fa-solid fa-award text-bronze text-2xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-3xl font-bold text-header mb-4">Elite Partnerships</h3>
                                    <div class="prose prose-xl text-header max-w-none leading-relaxed">
                                        {!! $page['acf']['pro_content'] ?? '<p>We provide comprehensive professional services for architects, designers, contractors, and industry professionals seeking exceptional custom millwork and metalwork solutions that define luxury and precision.</p>' !!}
                                    </div>
                                </div>
                            </div>

                            <!-- Premium Features -->
                            <div class="grid md:grid-cols-2 gap-6 mt-8">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-bronze rounded-full"></div>
                                    <span class="text-header font-semibold">White-Glove Service</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-bronze rounded-full"></div>
                                    <span class="text-header font-semibold">Precision Engineering</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-bronze rounded-full"></div>
                                    <span class="text-header font-semibold">Luxury Materials</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-bronze rounded-full"></div>
                                    <span class="text-header font-semibold">Timeless Craftsmanship</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stunning Image Showcase -->
                    @if (!empty($page['acf']['pro_image_1']))
                        <div class="relative">
                            <div class="group relative overflow-hidden rounded-3xl shadow-2xl">
                                <img src="{{ $page['acf']['pro_image_1'] }}"
                                     alt="Premium Professional Work"
                                     class="w-full h-[600px] object-cover transition-transform duration-700 group-hover:scale-105" />
                                <div class="absolute inset-0 bg-gradient-to-t from-bronze/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                <div class="absolute bottom-8 left-8 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                                    <div class="bg-white/90 backdrop-blur-sm rounded-2xl px-8 py-4">
                                        <span class="text-header font-bold text-xl">Premium Craftsmanship</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Floating Stats
                            <div class="absolute -top-8 -right-8 bg-bronze/90 backdrop-blur-sm rounded-3xl p-8 text-center shadow-2xl">
                                <div class="text-white text-3xl font-bold">5+</div>
                                <div class="text-white/80 text-sm font-semibold">Years Experience</div>
                            </div>-->

                            <div class="absolute -top-8 -right-8 bg-bronze/90 backdrop-blur-sm rounded-3xl p-8 text-center shadow-2xl">
                                <div class="text-white text-3xl font-bold">500+</div>
                                <div class="text-white/80 text-sm font-semibold">Elite Projects</div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Premium CTA Section -->
                <div class="text-center">
                    <div class="bg-white/90 backdrop-blur-md rounded-3xl p-16 border border-bronze/30 shadow-2xl max-w-4xl mx-auto">
                        <div class="flex items-center justify-center mb-8">
                            <div class="w-20 h-20 bg-bronze/20 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-handshake text-bronze text-3xl"></i>
                            </div>
                        </div>
                        <h3 class="text-4xl font-bold text-header mb-6">Ready to Create Something Extraordinary?</h3>
                        <p class="text-xl text-header/80 mb-10 leading-relaxed">
                            Join the elite architects and designers who trust us with their most prestigious projects.
                        </p>
                        <div class="flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8">
                            <a href="{{ url('contact-us#form') }}"
                               class="group inline-flex items-center px-12 py-6 bg-bronze text-header font-bold text-xl rounded-full shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-500">
                                <span>Start Your Project</span>
                                <svg class="w-6 h-6 ml-4 transform group-hover:translate-x-2 transition-transform duration-500"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>

                            <a href="{{ url('portfolio') }}"
                               class="group text-header hover:text-bronze transition-colors duration-300 text-xl font-medium">
                                <span class="border-b-2 border-header/30 group-hover:border-bronze transition-colors duration-300">
                                    View Our Portfolio
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
