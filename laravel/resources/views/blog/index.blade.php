@extends('layouts.app')

@section('content')
    <div class="max-w-5xl mx-auto px-6 py-12 font-serif pt-30"
         x-data="{
         activeCategory: 'all',
         posts: {{ Js::from($posts) }},
         get filteredPosts() {
             if (this.activeCategory === 'all') return this.posts;
             return this.posts.filter(p => (p.categories || []).includes(parseInt(this.activeCategory)));
         }}">
        <div class="mb-8 pb-4">
            <div class="mx-auto text-center">
                <h3 class="text-7xl mb-3 italic text-header">Blog - Cache River Mill & MetalWorks</h3>
                <hr class="w-24 mx-auto mb-6">{{-- CATEGORY FILTER MENU --}}
                <div class="mb-8 pb-4">
                    <div class="flex flex-wrap gap-3 justify-center">
                        <button class="px-3 py-1"
                                :class="activeCategory === 'all' ? 'text-header underline font-bold' : ''"
                                @click="activeCategory = 'all'">All</button>
                        @foreach ($categories as $category)
                            <button class="px-3 py-1"
                                    :class="activeCategory == '{!! $category['id'] !!}' ? 'text-header underline font-bold' : ''"
                                    @click="activeCategory = '{!!  $category['id'] !!}'">
                                {!!  $category['name'] !!}
                            </button>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="flex flex-col lg:flex-row gap-12">
                {{-- MAIN CONTENT --}}
                <div class="w-full lg:w-2/3">
                    {{-- BLOG POSTS --}}
                    <template x-for="post in filteredPosts" :key="post.id">
                        <div class="mb-10 border-b pb-6">
                            <img :src="post._embedded['wp:featuredmedia'][0].source_url"
                                 class="mb-4 w-full object-cover"
                                 :alt="">
                            <h2 class="text-4xl italic mb-2">
                                <a :href="`/blog/${post.slug}`" class="hover:underline" x-html="post.title.rendered"></a>
                            </h2>
                            <p class="text-gray text-sm mb-4" x-text="new Date(post.date).toDateString()"></p>
                            <div class="text-base text-gray text-md" x-html="post.excerpt.rendered"></div>
                        </div>
                    </template>
                </div>
                {{-- SIDEBAR --}}
                <div class="w-full lg:w-1/3">
                    @include('partials.blog-sidebar', [
                        'categories' => $categories,
                        'recentPosts' => $recentPosts,
                        'tags' => $tags
                        ])
                </div>
            </div>
        </div>
    </div>
@endsection
