<aside class="w-full px-4">
    <!-- Categories -->
    <div class="mb-8">
        <h3 class="text-lg font-bold mb-2 border-b pb-1">Categories</h3>
        <ul class="text-lg">
            @foreach ($categories as $cat)
                <li class="mb-1 text-gray-800">
                    <a href="{{ url('/blog/category/' . $cat['slug']) }}" class="hover:underline">
                        {!! $cat['name'] !!} ({{ $cat['count'] }})
                    </a>
                </li>
            @endforeach
        </ul>
    </div>

    <!-- Recent Posts -->
    <div class="w-full mb-8">
        <h3 class="text-lg font-semibold mb-2 border-b pb-1">Recent Posts</h3>
        <ul class="text-lg">
            @foreach ($recentPosts as $post)
                <li class="mb-2">
                    <a class="text-gray-800 font-bold" href="{{ route('blog.show', $post['slug']) }}" class="hover:underline">
                        {!! $post['title']['rendered'] !!}
                    </a>
                    <p class="text-sm text-gray-500">
                        {{ \Carbon\Carbon::parse($post['date'])->toFormattedDateString() }}
                    </p>
                </li>
            @endforeach
        </ul>
    </div>

    <!-- Tags
    <div>
        <h3 class="text-lg font-semibold mb-2 border-b pb-1 underline">Tags</h3>
        <div class="flex flex-wrap gap-2 text-sm">
            @foreach ($tags as $tag)
                <a href="{{ url('/blog/tag/' . $tag['slug']) }}" class="px-2 py-1 bg-gray-100 rounded hover:bg-gray-200">
                    {{ $tag['name'] }}
                </a>
            @endforeach
        </div>
    </div>  -->
</aside>
