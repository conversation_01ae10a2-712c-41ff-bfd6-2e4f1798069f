<header x-data="{ scrolled: false, mobileOpen: false }"
        x-init="window.addEventListener('scroll', () => scrolled = window.pageYOffset > 50)"
        class="fixed inset-x-0 top-0 z-50 transition-all duration-300"
        :class="scrolled ? 'bg-header shadow-md' : 'bg-transparent'"
        id="main-header">
    <div class="w-full flex items-center justify-between px-6 py-4">

        {{-- LEFT: Logo --}}
        <div class="flex-shrink-0">
            <a href="/" class="flex space-x-3 items-center backdrop-blur-lg">
                <!-- Use Alpine binding: ensure x-data scope exists on header -->
                <img
                        :src="scrolled
                     ? '{{ $global['acf']['company_logo_white'] }}'
                     : '{{ $global['acf']['company_logo_' . $colorLogo] }}'"
                        class="h-20"
                        alt="Company Logo"
                />
            </a>
        </div>

        {{-- RIGHT: Desktop Navigation --}}
        <nav class="hidden md:flex items-center space-x-8">
            @include('partials.navigation', [
              'colorMenu' => 'text-' . ($colorMenu ?? 'bronze'),
              'nav'       => $nav ?? []
            ])
        </nav>

        {{-- RIGHT: Mobile Toggle --}}
        <div class="md:hidden">
            <button @click="mobileOpen = !mobileOpen"
                    class="inline-flex items-center justify-center w-10 h-10 text-gray-600 hover:text-bronze focus:outline-none">
                <svg x-show="!mobileOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
                <svg x-show="mobileOpen" x-cloak class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
    </div>

    {{-- Mobile Dropdown Menu --}}
    <nav x-show="mobileOpen"
         x-transition
         x-cloak
         class="md:hidden px-6 pt-2 pb-4 bg-white shadow-md space-y-2">
        @include('partials.navigation', ['mobile' => true, 'nav' => $nav ?? []])
    </nav>
</header>