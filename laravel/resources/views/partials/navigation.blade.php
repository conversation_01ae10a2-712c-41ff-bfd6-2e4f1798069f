@php $isMobile = isset($mobile) && $mobile; @endphp

<div class="{{ $isMobile ? 'block space-y-2' : 'flex space-x-8' }}">
    @foreach($nav as $item)
        @if(!empty($item['label']) && !empty($item['url']))
            <div class="relative group {{ $isMobile ? 'w-full' : '' }} backdrop-blur-sm">
                {{-- Parent Item --}}
                <a href="{{ $item['url'] }}"
                   class="text-sm transition-colors duration-300"
                   :class="scrolled ? 'text-white' : '{{ $colorMenu }} font-bold'">
                    {{ $item['label'] }}
                </a>

                {{-- Child Items --}}
                @if(!empty($item['children']) && is_array($item['children']))
                    <div class="{{ $isMobile
                        ? 'mt-1 w-full bg-white bg-opacity-75 rounded'
                        : 'absolute left-0 mt-2 w-48 bg-white rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50' }}">

                        <ul class="{{ $isMobile ? 'p-2 space-y-1' : 'py-2' }}">
                            @foreach($item['children'] as $child)
                                @if(!empty($child['label']) && !empty($child['url']))
                                    <li>
                                        <a href="{{ $child['url'] }}"
                                           @click="mobileOpen = false"
                                           class="block px-4 py-2 text-sm text-charcoal hover:bg-lightgray">
                                            {{ $child['label'] }}
                                        </a>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        @endif
    @endforeach

    {{-- Authentication Links --}}
    <div class="relative {{ $isMobile ? 'w-full border-t pt-2 mt-2' : 'ml-4' }}">
        @auth
            {{-- Desktop: Profile Dropdown --}}
            @if(!$isMobile)
                <div class="relative group">
                    {{-- Profile Icon --}}
                    <div class="w-10 h-10 bg-bronze/20 backdrop-blur-md rounded-full flex items-center justify-center cursor-pointer border-2 border-bronze/30 hover:border-bronze/60 transition-all duration-300 group-hover:scale-105">
                        <span class="text-bronze font-bold text-sm">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>

                    {{-- Dropdown Menu --}}
                    <div class="absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50" style="right: -50px; min-width: 280px;">
                        {{-- User Info Header --}}
                        <div class="px-6 py-4 border-b border-gray-200/50">
                            <p class="text-gray-800 font-semibold">{{ Auth::user()->name }}</p>
                            <p class="text-gray-600 text-sm">{{ Auth::user()->email }}</p>
                            <p class="text-bronze text-xs font-medium mt-1">{{ ucfirst(str_replace('_', ' ', Auth::user()->user_type)) }}</p>
                        </div>

                        {{-- Menu Items --}}
                        <div class="py-2">
                            {{-- Dashboard --}}
                            <a href="{{ route('dashboard') }}"
                               class="flex items-center px-6 py-3 text-gray-700 hover:bg-bronze/10 hover:text-bronze transition-colors duration-200">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                                </svg>
                                Dashboard
                            </a>

                            {{-- Admin Link (if admin/super_admin) --}}
                            @if(in_array(Auth::user()->user_type, ['admin', 'super_admin']))
                                <a href="{{ route('admin.dashboard') }}"
                                   class="flex items-center px-6 py-3 text-purple-700 hover:bg-purple-50 hover:text-purple-800 transition-colors duration-200">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Admin Panel
                                </a>
                            @endif

                            {{-- Divider --}}
                            <div class="border-t border-gray-200/50 my-2"></div>

                            {{-- Logout --}}
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit"
                                        class="flex items-center w-full px-6 py-3 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            @else
                {{-- Mobile: Simple List --}}
                <div class="space-y-2">
                    <div class="text-sm font-bold text-bronze mb-2">{{ Auth::user()->name }}</div>

                    <a href="{{ route('dashboard') }}"
                       class="block text-sm transition-colors duration-300"
                       :class="scrolled ? 'text-white hover:text-bronze' : '{{ $colorMenu }} font-bold hover:text-bronze'">
                        Dashboard
                    </a>

                    @if(in_array(Auth::user()->user_type, ['admin', 'super_admin']))
                        <a href="{{ route('admin.dashboard') }}"
                           class="block text-sm transition-colors duration-300 text-purple-600 hover:text-purple-800">
                            Admin Panel
                        </a>
                    @endif

                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit"
                                class="block text-sm transition-colors duration-300 text-red-600 hover:text-red-800">
                            Logout
                        </button>
                    </form>
                </div>
            @endif
        @else
            {{-- Not Logged In --}}
            <div class="{{ $isMobile ? 'space-y-2' : 'flex items-center space-x-4' }}">
                <a href="{{ route('login') }}"
                   class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                   :class="scrolled ? 'text-white hover:text-bronze' : '{{ $colorMenu }} font-bold hover:text-bronze'">
                    Login
                </a>

                <a href="{{ route('register') }}"
                   class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                   :class="scrolled ? 'text-bronze hover:text-bronze/80' : 'text-bronze font-bold hover:text-bronze/80'">
                    Register
                </a>
            </div>
        @endauth
    </div>
</div>