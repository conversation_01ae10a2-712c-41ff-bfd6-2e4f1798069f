@php $isMobile = isset($mobile) && $mobile; @endphp

<div class="{{ $isMobile ? 'block space-y-2' : 'flex space-x-8' }}">
    @foreach($nav as $item)
        @if(!empty($item['label']) && !empty($item['url']))
            <div class="relative group {{ $isMobile ? 'w-full' : '' }} backdrop-blur-sm">
                {{-- Parent Item --}}
                <a href="{{ $item['url'] }}"
                   class="text-sm transition-colors duration-300"
                   :class="scrolled ? 'text-white' : '{{ $colorMenu }} font-bold'">
                    {{ $item['label'] }}
                </a>

                {{-- Child Items --}}
                @if(!empty($item['children']) && is_array($item['children']))
                    <div class="{{ $isMobile
                        ? 'mt-1 w-full bg-white bg-opacity-75 rounded'
                        : 'absolute left-0 mt-2 w-48 bg-white rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50' }}">

                        <ul class="{{ $isMobile ? 'p-2 space-y-1' : 'py-2' }}">
                            @foreach($item['children'] as $child)
                                @if(!empty($child['label']) && !empty($child['url']))
                                    <li>
                                        <a href="{{ $child['url'] }}"
                                           @click="mobileOpen = false"
                                           class="block px-4 py-2 text-sm text-charcoal hover:bg-lightgray">
                                            {{ $child['label'] }}
                                        </a>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        @endif
    @endforeach

    {{-- Authentication Links --}}
    <div class="relative group {{ $isMobile ? 'w-full border-t pt-2 mt-2' : 'ml-4' }} backdrop-blur-sm">
        @auth
            {{-- Logged In User --}}
            <div class="{{ $isMobile ? 'space-y-2' : 'flex items-center space-x-4' }}">
                {{-- User Name --}}
                <span class="text-sm {{ $isMobile ? 'block' : '' }}"
                      :class="scrolled ? 'text-white' : '{{ $colorMenu }} font-bold'">
                    {{ Auth::user()->name }}
                </span>

                {{-- Dashboard Link --}}
                <a href="{{ route('dashboard') }}"
                   class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                   :class="scrolled ? 'text-white hover:text-bronze' : '{{ $colorMenu }} font-bold hover:text-bronze'">
                    Dashboard
                </a>

                {{-- Admin Link (if admin/super_admin) --}}
                @if(in_array(Auth::user()->user_type, ['admin', 'super_admin']))
                    <a href="{{ route('admin.dashboard') }}"
                       class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                       :class="scrolled ? 'text-purple-300 hover:text-purple-100' : 'text-purple-600 font-bold hover:text-purple-800'">
                        Admin
                    </a>
                @endif

                {{-- Logout --}}
                <form method="POST" action="{{ route('logout') }}" class="{{ $isMobile ? 'block' : 'inline' }}">
                    @csrf
                    <button type="submit"
                            class="text-sm transition-colors duration-300 {{ $isMobile ? 'block w-full text-left' : '' }}"
                            :class="scrolled ? 'text-red-300 hover:text-red-100' : 'text-red-600 font-bold hover:text-red-800'">
                        Logout
                    </button>
                </form>
            </div>
        @else
            {{-- Not Logged In --}}
            <div class="{{ $isMobile ? 'space-y-2' : 'flex items-center space-x-4' }}">
                <a href="{{ route('login') }}"
                   class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                   :class="scrolled ? 'text-white hover:text-bronze' : '{{ $colorMenu }} font-bold hover:text-bronze'">
                    Login
                </a>

                <a href="{{ route('register') }}"
                   class="text-sm transition-colors duration-300 {{ $isMobile ? 'block' : '' }}"
                   :class="scrolled ? 'text-bronze hover:text-bronze/80' : 'text-bronze font-bold hover:text-bronze/80'">
                    Register
                </a>
            </div>
        @endauth
    </div>
</div>