@php $isMobile = isset($mobile) && $mobile; @endphp

<div class="{{ $isMobile ? 'block space-y-2' : 'flex space-x-8' }}">
    @foreach($nav as $item)
        @if(!empty($item['label']) && !empty($item['url']))
            <div class="relative group {{ $isMobile ? 'w-full' : '' }} backdrop-blur-sm">
                {{-- Parent Item --}}
                <a href="{{ $item['url'] }}"
                   class="text-sm transition-colors duration-300"
                   :class="scrolled ? 'text-white' : '{{ $colorMenu }} font-bold'">
                    {{ $item['label'] }}
                </a>

                {{-- Child Items --}}
                @if(!empty($item['children']) && is_array($item['children']))
                    <div class="{{ $isMobile
                        ? 'mt-1 w-full bg-white bg-opacity-75 rounded'
                        : 'absolute left-0 mt-2 w-48 bg-white rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50' }}">

                        <ul class="{{ $isMobile ? 'p-2 space-y-1' : 'py-2' }}">
                            @foreach($item['children'] as $child)
                                @if(!empty($child['label']) && !empty($child['url']))
                                    <li>
                                        <a href="{{ $child['url'] }}"
                                           @click="mobileOpen = false"
                                           class="block px-4 py-2 text-sm text-charcoal hover:bg-lightgray">
                                            {{ $child['label'] }}
                                        </a>
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        @endif
    @endforeach
</div>