<footer class="bg-white py-2 border-t border-lightgray bg-no-repeat bg-cover bg-center relative">
    <div class="absolute inset-0 bg-no-repeat bg-cover bg-center"
         style="opacity: 0.12;background-image: url('{{ $global['acf']['footer_background'] ?? '/images/default-footer-bg.png' }}');">
    </div>
    <div class="container mx-auto relative z-10 py-2 px-4">
        @if (!empty($global['acf']['footer_links_json']))
            @php
                $footerSections = json_decode($global['acf']['footer_links_json'], true);
                $lastIndex = count($footerSections) - 1;
            @endphp

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-left text-sm mb-4 p-8">
                @foreach ($footerSections as $index => $section)
                    @if ($index === $lastIndex)
                        @continue
                    @endif
                    <div>
                        <h5 class="font-semibold text-header mb-2">{{ $section['heading'] ?? '' }}</h5>
                        <ul class="space-y-1">
                            @foreach ($section['links'] as $link)
                                <li>
                                    <a href="{{ $link['url'] }}" class="hover:underline text-gray">
                                        {{ $link['label'] }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endforeach
                <!-- Social Media Icons -->
                <div class="flex flex-col justify-end items-end h-full">
                    <div class="flex space-x-4">
                        <a href="https://facebook.com" target="_blank" class="text-header hover:text-bronze transition">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com" target="_blank" class="text-header hover:text-bronze transition">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://instagram.com" target="_blank" class="text-header hover:text-bronze transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="https://linkedin.com" target="_blank" class="text-header hover:text-bronze transition">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>
            <hr class="border-t border-lightgray">
            {{-- Last group in a single horizontal line --}}
            <div class="flex flex-wrap justify-center space-x-4 text-xs text-gray p-2">
                @foreach ($footerSections[$lastIndex]['links'] as $link)
                    <a href="{{ $link['url'] }}" class="hover:underline">
                        {{ $link['label'] }}
                    </a>
                @endforeach
            </div>
        @endif

        <p class="text-xs text-center">
            &copy; {{ now()->year }} Cache River Mill & Steelworks
        </p>

    </div>
</footer>
