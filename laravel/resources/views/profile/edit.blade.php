@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">

        <!-- Profile Header -->
        <div class="max-w-4xl mx-auto">

            <!-- Back Button & Title -->
            <div class="mb-8">
                <a href="{{ route('dashboard') }}" class="inline-flex items-center text-bronze hover:text-bronze/80 transition-colors duration-300 mb-4">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Back to Dashboard
                </a>
                <h1 class="text-4xl font-bold text-bronze mb-2">Account Settings</h1>
                <p class="text-white/80 text-lg">Manage your profile information and preferences</p>
            </div>

            <!-- Success Messages -->
            @if(session('success'))
                <div class="mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-white font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Profile Settings Grid -->
            <div class="grid lg:grid-cols-2 gap-8">

                <!-- Profile Image Section -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl">
                    <h3 class="text-2xl font-bold text-bronze mb-6">Profile Image</h3>

                    <div class="text-center">
                        <!-- Current Profile Image -->
                        <div class="mb-6">
                            @if(Auth::user()->profile_image)
                                <img src="{{ asset('storage/' . Auth::user()->profile_image) }}"
                                     alt="Profile Image"
                                     class="w-32 h-32 rounded-full mx-auto object-cover border-4 border-bronze/30">
                            @else
                                <div class="w-32 h-32 bg-bronze/20 rounded-full flex items-center justify-center mx-auto border-4 border-bronze/30">
                                    <svg class="w-16 h-16 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>

                        <!-- Image Upload Form -->
                        <form action="{{ route('profile.image.upload') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                            @csrf
                            <div>
                                <input type="file"
                                       name="profile_image"
                                       accept="image/*"
                                       class="hidden"
                                       id="profile-image-input"
                                       onchange="this.form.submit()">
                                <label for="profile-image-input"
                                       class="cursor-pointer bg-bronze hover:bg-bronze/90 text-header font-medium py-3 px-6 rounded-xl transition-all duration-300 inline-block">
                                    {{ Auth::user()->profile_image ? 'Change Photo' : 'Upload Photo' }}
                                </label>
                            </div>
                            @error('profile_image')
                                <p class="text-red-400 text-sm">{{ $message }}</p>
                            @enderror
                        </form>

                        @if(Auth::user()->profile_image)
                            <form action="{{ route('profile.image.remove') }}" method="POST" class="mt-4">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="bg-red-500/20 hover:bg-red-500/30 text-red-400 font-medium py-2 px-4 rounded-xl transition-all duration-300"
                                        onclick="return confirm('Are you sure you want to remove your profile image?')">
                                    Remove Photo
                                </button>
                            </form>
                        @endif
                    </div>
                </div>

                <!-- Profile Information Section -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl">
                    <h3 class="text-2xl font-bold text-bronze mb-6">Profile Information</h3>

                    <form method="POST" action="{{ route('profile.update') }}" class="space-y-6">
                        @csrf
                        @method('PATCH')

                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-white font-medium mb-2">Full Name</label>
                            <input type="text"
                                   id="name"
                                   name="name"
                                   value="{{ old('name', $user->name) }}"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300"
                                   required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-white font-medium mb-2">Email Address</label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   value="{{ old('email', $user->email) }}"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300"
                                   required>
                            @error('email')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                            @if($user->email_verified_at === null)
                                <p class="text-yellow-400 text-sm mt-1">Your email address is unverified.</p>
                            @endif
                        </div>

                        <!-- Company -->
                        <div>
                            <label for="company" class="block text-white font-medium mb-2">Company (Optional)</label>
                            <input type="text"
                                   id="company"
                                   name="company"
                                   value="{{ old('company', $user->company) }}"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300">
                            @error('company')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button type="submit"
                                    class="w-full bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Password Change Section -->
            <div class="mt-8">
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl">
                    <h3 class="text-2xl font-bold text-bronze mb-6">Change Password</h3>

                    <form method="POST" action="{{ route('profile.password.update') }}" class="space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- Current Password -->
                        <div>
                            <label for="current_password" class="block text-white font-medium mb-2">Current Password</label>
                            <input type="password"
                                   id="current_password"
                                   name="current_password"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300"
                                   required>
                            @error('current_password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="password" class="block text-white font-medium mb-2">New Password</label>
                            <input type="password"
                                   id="password"
                                   name="password"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300"
                                   required>
                            @error('password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="block text-white font-medium mb-2">Confirm New Password</label>
                            <input type="password"
                                   id="password_confirmation"
                                   name="password_confirmation"
                                   class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-bronze focus:border-transparent transition-all duration-300"
                                   required>
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button type="submit"
                                    class="bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
