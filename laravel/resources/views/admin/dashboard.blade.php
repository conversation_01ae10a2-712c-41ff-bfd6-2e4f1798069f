@extends('layouts.app')

@section('background')
    <!-- Textured Background -->
    <div class="fixed inset-0 bg-gray-100" style="background-image: url('{{ asset('img/photo-wall-texture-pattern.webp') }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"></div>
    <!-- Overlay for readability -->
    <div class="fixed inset-0 bg-white/80"></div>
@endsection

@section('content')
    <div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Admin Dashboard Header -->
        <div class="max-w-7xl mx-auto">
            
            <!-- Welcome Section -->
            <div class="text-center mb-12">
                <h1 class="text-5xl font-bold text-header mb-4">Admin Dashboard</h1>
                <p class="text-gray-700 text-xl">Welcome, {{ Auth::user()->name }}</p>
                <p class="text-gray-600 text-lg">System Administration Panel</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="max-w-md mx-auto mb-8">
                    <div class="bg-green-50 border border-green-200 rounded-2xl p-6 shadow-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-green-800 font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="max-w-md mx-auto mb-8">
                    <div class="bg-red-50 border border-red-200 rounded-2xl p-6 shadow-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                            <p class="text-red-800 font-medium">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">

                <!-- Combined Users Card -->
                <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg p-6 border-2 border-gray-300 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-1" style="box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-200 to-blue-300 rounded-lg flex items-center justify-center mx-auto mb-4 border border-blue-400 shadow-inner">
                            <svg class="w-6 h-6 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3 drop-shadow-sm">User Management</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Total:</span>
                                <span class="text-blue-700 font-bold">{{ $totalUsers }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Active:</span>
                                <span class="text-green-700 font-bold">{{ $activeUsers }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Pending:</span>
                                <span class="text-yellow-700 font-bold">{{ $pendingUsers }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Website Performance Card -->
                <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg p-6 border-2 border-gray-300 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-1" style="box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-200 to-green-300 rounded-lg flex items-center justify-center mx-auto mb-4 border border-green-400 shadow-inner">
                            <svg class="w-6 h-6 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3 drop-shadow-sm">Site Performance</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Uptime:</span>
                                <span class="text-green-700 font-bold">99.9%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Load Time:</span>
                                <span class="text-blue-700 font-bold">1.2s</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700 font-medium">Status:</span>
                                <span class="text-green-700 font-bold">Healthy</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calendar Card -->
                <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg p-6 border-2 border-gray-300 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-1" style="box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-200 to-orange-300 rounded-lg flex items-center justify-center mx-auto mb-4 border border-orange-400 shadow-inner">
                            <svg class="w-6 h-6 text-orange-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-3 drop-shadow-sm">Today</h3>
                        <div class="space-y-2">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-orange-700 drop-shadow-sm">{{ date('j') }}</div>
                                <div class="text-gray-700 font-medium">{{ date('M Y') }}</div>
                            </div>
                            <div class="text-gray-700 font-medium text-sm">
                                {{ date('l') }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Submissions Card -->
                <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg p-6 border-2 border-gray-300 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-1" style="box-shadow: inset 0 1px 0 rgba(255,255,255,0.6), 0 8px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-200 to-purple-300 rounded-lg flex items-center justify-center mx-auto mb-4 border border-purple-400 shadow-inner">
                            <svg class="w-6 h-6 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-purple-700 mb-2 drop-shadow-sm">{{ $totalSubmissions }}</h3>
                        <p class="text-gray-700 font-semibold">Form Submissions</p>
                        @if($unreadSubmissions > 0)
                            <p class="text-red-700 text-sm font-bold mt-1 drop-shadow-sm">{{ $unreadSubmissions }} unread</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Management Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">

                <!-- User Management Card -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-150 rounded-xl p-8 border-2 border-gray-400 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-header/20 to-header/30 rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-header/40 shadow-inner">
                            <svg class="w-8 h-8 text-header" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4 drop-shadow-sm">User Management</h3>
                        <p class="text-gray-700 mb-4 font-medium">Manage user accounts, permissions, and access</p>
                        <a href="{{ route('admin.users') }}" class="bg-header hover:bg-header/90 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:scale-105">
                            Manage Users
                        </a>
                    </div>
                </div>

                <!-- Form Submissions Card -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-150 rounded-xl p-8 border-2 border-gray-400 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-200 to-purple-300 rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-purple-400 shadow-inner">
                            <svg class="w-8 h-8 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-purple-700 mb-4 drop-shadow-sm">Form Submissions</h3>
                        <p class="text-gray-700 mb-4 font-medium">View and manage contact form submissions</p>
                        <a href="{{ route('admin.form-submissions') }}" class="bg-header hover:bg-header/90 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 inline-block shadow-lg hover:shadow-xl transform hover:scale-105">
                            View Submissions
                        </a>
                        @if($unreadSubmissions > 0)
                            <span class="block mt-3 text-red-700 text-sm font-bold drop-shadow-sm bg-red-800 px-3 py-1 rounded-full inline-block">{{ $unreadSubmissions }} unread</span>
                        @endif
                    </div>
                </div>

                <!-- System Settings Card -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-150 rounded-xl p-8 border-2 border-gray-400 shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl flex items-center justify-center mx-auto mb-6 border-2 border-gray-400 shadow-inner">
                            <svg class="w-8 h-8 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-4 drop-shadow-sm">System Settings</h3>
                        <p class="text-gray-700 mb-4 font-medium">Configure system preferences and settings</p>
                        <button class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg opacity-60 cursor-not-allowed" disabled>
                            Coming Soon
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Users Section -->
            @if($recentUsers->count() > 0)
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8 border-2 border-gray-400 shadow-2xl mb-12" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                <h3 class="text-2xl font-bold text-gray-800 mb-6 drop-shadow-sm">Recent Users</h3>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b-2 border-gray-400">
                                <th class="text-left py-4 px-4 font-bold text-gray-800 drop-shadow-sm">Name</th>
                                <th class="text-left py-4 px-4 font-bold text-gray-800 drop-shadow-sm">Email</th>
                                <th class="text-left py-4 px-4 font-bold text-gray-800 drop-shadow-sm">Type</th>
                                <th class="text-left py-4 px-4 font-bold text-gray-800 drop-shadow-sm">Status</th>
                                <th class="text-left py-4 px-4 font-bold text-gray-800 drop-shadow-sm">Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentUsers as $user)
                            <tr class="border-b border-gray-300 hover:bg-gradient-to-r hover:from-gray-100 hover:to-gray-200 transition-all duration-300">
                                <td class="py-4 px-4 text-gray-800 font-semibold">{{ $user->name }}</td>
                                <td class="py-4 px-4 text-gray-700">{{ $user->email }}</td>
                                <td class="py-4 px-4">
                                    <span class="px-3 py-1 text-xs font-bold rounded-lg shadow-sm border
                                        {{ $user->user_type === 'admin' ? 'bg-gradient-to-r from-purple-200 to-purple-300 text-purple-800 border-purple-400' :
                                           ($user->user_type === 'super_admin' ? 'bg-gradient-to-r from-red-200 to-red-300 text-red-800 border-red-400' : 'bg-gradient-to-r from-blue-200 to-blue-300 text-blue-800 border-blue-400') }}">
                                        {{ ucfirst(str_replace('_', ' ', $user->user_type ?? 'user')) }}
                                    </span>
                                </td>
                                <td class="py-4 px-4">
                                    <span class="px-3 py-1 text-xs font-bold rounded-lg shadow-sm border
                                        {{ $user->is_active ? 'bg-gradient-to-r from-green-200 to-green-300 text-green-800 border-green-400' : 'bg-gradient-to-r from-yellow-200 to-yellow-300 text-yellow-800 border-yellow-400' }}">
                                        {{ $user->is_active ? 'Active' : 'Pending' }}
                                    </span>
                                </td>
                                <td class="py-4 px-4 text-gray-700 font-medium">{{ $user->created_at->format('M j, Y') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="text-center">
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8 border-2 border-gray-400 shadow-2xl inline-block" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6 drop-shadow-sm">Quick Actions</h3>
                    <div class="flex flex-wrap justify-center items-center gap-4">
                        <a href="{{ route('dashboard') }}" class="bg-header hover:bg-header/90 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            User Dashboard
                        </a>
                        <a href="{{ route('home') }}" class="bg-header hover:bg-header/90 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            Back to Home
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
