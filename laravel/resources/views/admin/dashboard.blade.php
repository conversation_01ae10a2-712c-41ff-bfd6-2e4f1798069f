@extends('layouts.app')

@section('background')
    <!-- Clean Professional Background -->
    <div class="fixed inset-0 bg-gray-50"></div>
@endsection

@section('content')
    <div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Admin Dashboard Header -->
        <div class="max-w-7xl mx-auto">
            
            <!-- Welcome Section -->
            <div class="text-center mb-12">
                <h1 class="text-5xl font-bold text-header mb-4">Admin Dashboard</h1>
                <p class="text-gray-700 text-xl">Welcome, {{ Auth::user()->name }}</p>
                <p class="text-gray-600 text-lg">System Administration Panel</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="max-w-md mx-auto mb-8">
                    <div class="bg-green-50 border border-green-200 rounded-2xl p-6 shadow-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-green-800 font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="max-w-md mx-auto mb-8">
                    <div class="bg-red-50 border border-red-200 rounded-2xl p-6 shadow-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                            <p class="text-red-800 font-medium">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                
                <!-- Total Users Card -->
                <div class="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-header mb-2">{{ $totalUsers }}</h3>
                        <p class="text-gray-600 font-medium">Total Users</p>
                    </div>
                </div>

                <!-- Active Users Card -->
                <div class="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-green-600 mb-2">{{ $activeUsers }}</h3>
                        <p class="text-gray-600 font-medium">Active Users</p>
                    </div>
                </div>

                <!-- Pending Users Card -->
                <div class="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-yellow-600 mb-2">{{ $pendingUsers }}</h3>
                        <p class="text-gray-600 font-medium">Pending Users</p>
                    </div>
                </div>

                <!-- Form Submissions Card -->
                <div class="bg-white rounded-3xl p-6 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-purple-600 mb-2">{{ $totalSubmissions }}</h3>
                        <p class="text-gray-600 font-medium">Form Submissions</p>
                        @if($unreadSubmissions > 0)
                            <p class="text-red-600 text-sm font-medium mt-1">{{ $unreadSubmissions }} unread</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Management Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                
                <!-- User Management Card -->
                <div class="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-header/10 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-header" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-header mb-4">User Management</h3>
                        <p class="text-gray-600 mb-4">Manage user accounts, permissions, and access</p>
                        <a href="{{ route('admin.users') }}" class="bg-header hover:bg-header/90 text-white font-medium py-2 px-4 rounded-xl transition-all duration-300 inline-block">
                            Manage Users
                        </a>
                    </div>
                </div>

                <!-- Form Submissions Card -->
                <div class="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-purple-600 mb-4">Form Submissions</h3>
                        <p class="text-gray-600 mb-4">View and manage contact form submissions</p>
                        <a href="{{ route('admin.form-submissions') }}" class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-xl transition-all duration-300 inline-block">
                            View Submissions
                        </a>
                        @if($unreadSubmissions > 0)
                            <span class="block mt-2 text-red-600 text-sm font-medium">{{ $unreadSubmissions }} unread</span>
                        @endif
                    </div>
                </div>

                <!-- System Settings Card -->
                <div class="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-700 mb-4">System Settings</h3>
                        <p class="text-gray-600 mb-4">Configure system preferences and settings</p>
                        <button class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-xl transition-all duration-300" disabled>
                            Coming Soon
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Users Section -->
            @if($recentUsers->count() > 0)
            <div class="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg mb-12">
                <h3 class="text-2xl font-bold text-header mb-6">Recent Users</h3>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-left py-3 px-4 font-semibold text-gray-700">Name</th>
                                <th class="text-left py-3 px-4 font-semibold text-gray-700">Email</th>
                                <th class="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                                <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                <th class="text-left py-3 px-4 font-semibold text-gray-700">Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentUsers as $user)
                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                <td class="py-3 px-4 text-gray-800">{{ $user->name }}</td>
                                <td class="py-3 px-4 text-gray-600">{{ $user->email }}</td>
                                <td class="py-3 px-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full 
                                        {{ $user->user_type === 'admin' ? 'bg-purple-100 text-purple-800' : 
                                           ($user->user_type === 'super_admin' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800') }}">
                                        {{ ucfirst(str_replace('_', ' ', $user->user_type ?? 'user')) }}
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full 
                                        {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $user->is_active ? 'Active' : 'Pending' }}
                                    </span>
                                </td>
                                <td class="py-3 px-4 text-gray-600">{{ $user->created_at->format('M j, Y') }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="text-center">
                <div class="bg-white rounded-3xl p-8 border border-gray-200 shadow-lg inline-block">
                    <h3 class="text-2xl font-bold text-header mb-6">Quick Actions</h3>
                    <div class="flex flex-wrap justify-center items-center gap-4">
                        <a href="{{ route('dashboard') }}" class="bg-header hover:bg-header/90 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                            User Dashboard
                        </a>
                        <a href="{{ route('home') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                            Back to Home
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="bg-red-100 hover:bg-red-200 text-red-700 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
