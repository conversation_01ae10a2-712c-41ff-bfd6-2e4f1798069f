@extends('layouts.app')

@section('background')
    <!-- Blueprint Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-header via-header to-blue-900 opacity-95"></div>
    <div class="fixed inset-0 bg-blueprint-pattern opacity-20"></div>
@endsection

@section('content')
    <div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        
        <!-- Admin Dashboard -->
        <div class="max-w-7xl mx-auto">
            
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-5xl font-bold text-bronze mb-4">Admin Dashboard</h1>
                <p class="text-white/80 text-xl">Manage users and system settings</p>
                <p class="text-white/60 text-lg">Welcome, {{ Auth::user()->name }} ({{ ucfirst(Auth::user()->user_type) }})</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-bronze/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-bronze/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-white font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="max-w-4xl mx-auto mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-red-400/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-400/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-white font-medium">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Statistics Cards -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                
                <!-- Total Users -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-bronze mb-2">{{ $totalUsers }}</h3>
                        <p class="text-white/80">Total Users</p>
                    </div>
                </div>

                <!-- Pending Users -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-yellow-400 mb-2">{{ $pendingUsers }}</h3>
                        <p class="text-white/80">Pending Approval</p>
                    </div>
                </div>

                <!-- Active Users -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-green-400 mb-2">{{ $activeUsers }}</h3>
                        <p class="text-white/80">Active Users</p>
                    </div>
                </div>

                <!-- Manage Users -->
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-bronze/20 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-bronze" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-bronze mb-4">User Management</h3>
                        <a href="{{ route('admin.users') }}" class="bg-bronze hover:bg-bronze/90 text-header font-bold py-2 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                            Manage Users
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl mb-12">
                <h3 class="text-2xl font-bold text-bronze mb-6">Recent Registrations</h3>
                
                @if($recentUsers->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentUsers as $user)
                            <div class="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-bronze/20 rounded-full flex items-center justify-center">
                                        <span class="text-bronze font-bold">{{ substr($user->name, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <p class="text-white font-medium">{{ $user->name }}</p>
                                        <p class="text-white/60 text-sm">{{ $user->email }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <span class="px-3 py-1 rounded-full text-xs font-medium
                                        @if($user->is_active) bg-green-500/20 text-green-400 @else bg-yellow-500/20 text-yellow-400 @endif">
                                        {{ $user->is_active ? 'Active' : 'Pending' }}
                                    </span>
                                    <span class="text-white/60 text-sm">{{ $user->created_at->diffForHumans() }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-white/60 text-center py-8">No users registered yet.</p>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="text-center">
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-xl inline-block">
                    <h3 class="text-2xl font-bold text-bronze mb-6">Quick Actions</h3>
                    <div class="space-x-4">
                        <a href="{{ route('admin.users') }}" class="bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                            Manage All Users
                        </a>
                        <a href="{{ route('dashboard') }}" class="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                            Client Dashboard
                        </a>
                        <a href="{{ route('home') }}" class="bg-gray-500/20 hover:bg-gray-500/30 text-gray-400 font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl inline-block">
                            Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
