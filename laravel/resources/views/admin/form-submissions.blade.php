@extends('layouts.app')

@section('background')
    <!-- Textured Background -->
    <div class="fixed inset-0 bg-gray-100" style="background-image: url('{{ asset('img/photo-wall-texture-pattern.webp') }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"></div>
    <!-- Overlay for readability -->
    <div class="fixed inset-0 bg-white/80"></div>
@endsection

@section('head')
<style>
    .submission-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .submission-card:nth-child(even) {
        animation-delay: 0.1s;
    }

    .submission-card:nth-child(odd) {
        animation-delay: 0.2s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .pulse-dot {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }
</style>
@endsection

@section('content')
<div class="relative min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold text-bronze mb-4">Form Submissions</h1>
            <p class="text-white/80 text-xl">Manage contact and tour requests</p>
            <div class="flex justify-center items-center space-x-6 mt-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">{{ $submissions->count() }}</div>
                    <div class="text-white/60">Total Submissions</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-400">{{ $submissions->where('is_read', false)->count() }}</div>
                    <div class="text-white/60">Unread</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-400">{{ $submissions->where('is_read', true)->count() }}</div>
                    <div class="text-white/60">Read</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20 shadow-xl mb-8">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <select id="formTypeFilter" class="bg-white/20 border border-white/30 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-bronze">
                        <option value="">All Form Types</option>
                        <option value="contact">Contact Forms</option>
                        <option value="tour">Tour Requests</option>
                    </select>
                    <select id="statusFilter" class="bg-white/20 border border-white/30 rounded-xl px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-bronze">
                        <option value="">All Status</option>
                        <option value="unread">Unread</option>
                        <option value="read">Read</option>
                    </select>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="markAllAsRead()" class="bg-bronze hover:bg-bronze/90 text-header font-bold py-2 px-4 rounded-xl transition-all duration-300">
                        Mark All as Read
                    </button>
                    <a href="{{ route('admin.dashboard') }}" class="bg-white/20 hover:bg-white/30 text-white font-bold py-2 px-4 rounded-xl transition-all duration-300">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Submissions Grid -->
        <div class="grid gap-6">
            @forelse($submissions as $submission)
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 submission-card" 
                     data-type="{{ $submission->form_type }}" 
                     data-status="{{ $submission->is_read ? 'read' : 'unread' }}">
                    
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <!-- Header Row -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <!-- Status Indicator -->
                                    @if(!$submission->is_read)
                                        <div class="w-3 h-3 bg-red-500 rounded-full pulse-dot"></div>
                                    @else
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    @endif
                                    
                                    <!-- Form Type Badge -->
                                    <span class="px-3 py-1 text-sm font-semibold rounded-full 
                                        {{ $submission->form_type === 'contact' ? 'bg-blue-500/20 text-blue-300' : 'bg-purple-500/20 text-purple-300' }}">
                                        {{ $submission->form_type === 'contact' ? 'Contact Form' : 'Tour Request' }}
                                    </span>
                                    
                                    <!-- Submission ID -->
                                    <span class="text-white/60 text-sm">#{{ $submission->id }}</span>
                                </div>
                                
                                <div class="text-white/60 text-sm">
                                    {{ $submission->created_at->format('M j, Y g:i A') }}
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                                <div>
                                    <div class="text-bronze font-semibold text-lg">{{ $submission->name }}</div>
                                    <div class="text-white/80 text-sm">{{ $submission->email }}</div>
                                </div>
                                
                                @if($submission->company)
                                <div>
                                    <div class="text-white/60 text-xs uppercase tracking-wider">Company</div>
                                    <div class="text-white text-sm">{{ $submission->company }}</div>
                                </div>
                                @endif
                                
                                @if($submission->phone)
                                <div>
                                    <div class="text-white/60 text-xs uppercase tracking-wider">Phone</div>
                                    <div class="text-white text-sm">{{ $submission->phone }}</div>
                                </div>
                                @endif
                                
                                @if($submission->preferred_date)
                                <div>
                                    <div class="text-white/60 text-xs uppercase tracking-wider">Preferred Date</div>
                                    <div class="text-white text-sm">{{ $submission->preferred_date->format('M j, Y') }}</div>
                                </div>
                                @endif
                            </div>

                            <!-- Message Preview -->
                            @if($submission->message)
                            <div class="mb-4">
                                <div class="text-white/60 text-xs uppercase tracking-wider mb-2">Message</div>
                                <div class="bg-white/5 rounded-xl p-4 text-white/80 text-sm">
                                    {{ Str::limit($submission->message, 200) }}
                                    @if(strlen($submission->message) > 200)
                                        <span class="text-bronze cursor-pointer" onclick="showDetails({{ $submission->id }})">... read more</span>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col space-y-2 ml-6">
                            <button onclick="showDetails({{ $submission->id }})" 
                                    class="bg-bronze hover:bg-bronze/90 text-header font-bold py-2 px-4 rounded-xl transition-all duration-300 text-sm">
                                View Details
                            </button>
                            
                            @if(!$submission->is_read)
                            <button onclick="markAsRead({{ $submission->id }})" 
                                    class="bg-green-500/20 hover:bg-green-500/30 text-green-400 font-bold py-2 px-4 rounded-xl transition-all duration-300 text-sm">
                                Mark as Read
                            </button>
                            @else
                            <button onclick="markAsUnread({{ $submission->id }})" 
                                    class="bg-gray-500/20 hover:bg-gray-500/30 text-gray-400 font-bold py-2 px-4 rounded-xl transition-all duration-300 text-sm">
                                Mark as Unread
                            </button>
                            @endif
                            
                            <a href="mailto:{{ $submission->email }}" 
                               class="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 font-bold py-2 px-4 rounded-xl transition-all duration-300 text-sm text-center">
                                Reply
                            </a>
                        </div>
                    </div>
                </div>
            @empty
                <div class="text-center py-12">
                    <div class="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-white mb-2">No Form Submissions Yet</h3>
                    <p class="text-white/60">When visitors submit contact or tour request forms, they'll appear here.</p>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Enhanced Details Modal -->
    <div id="detailsModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border-0 w-11/12 md:w-3/4 lg:w-1/2 xl:w-2/5 shadow-2xl rounded-3xl bg-gradient-to-br from-header via-header to-blue-900">
            <div class="relative">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-bronze">Submission Details</h3>
                    <button onclick="closeModal()" class="text-white/60 hover:text-white transition-colors">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Modal Content -->
                <div id="modalContent" class="space-y-6">
                    <!-- Content will be loaded here -->
                </div>

                <!-- Modal Actions -->
                <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-white/20">
                    <button onclick="closeModal()" class="bg-white/20 hover:bg-white/30 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300">
                        Close
                    </button>
                    <button id="modalReplyBtn" class="bg-bronze hover:bg-bronze/90 text-header font-bold py-3 px-6 rounded-xl transition-all duration-300">
                        Reply via Email
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Filter functionality
document.getElementById('formTypeFilter').addEventListener('change', filterSubmissions);
document.getElementById('statusFilter').addEventListener('change', filterSubmissions);

function filterSubmissions() {
    const typeFilter = document.getElementById('formTypeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const cards = document.querySelectorAll('.submission-card');

    cards.forEach(card => {
        const cardType = card.dataset.type;
        const cardStatus = card.dataset.status;

        let showCard = true;

        if (typeFilter && cardType !== typeFilter) {
            showCard = false;
        }

        if (statusFilter && cardStatus !== statusFilter) {
            showCard = false;
        }

        if (showCard) {
            card.style.display = 'block';
            card.classList.remove('hidden');
        } else {
            card.style.display = 'none';
            card.classList.add('hidden');
        }
    });
}

// Modal functionality
function showDetails(id) {
    fetch(`/admin/form-submissions/${id}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('modalContent').innerHTML = `
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Name</div>
                                <div class="text-white text-lg">${data.name}</div>
                            </div>
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Email</div>
                                <div class="text-white text-lg">${data.email}</div>
                            </div>
                            ${data.company ? `
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Company</div>
                                <div class="text-white text-lg">${data.company}</div>
                            </div>
                            ` : ''}
                            ${data.industry ? `
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Industry</div>
                                <div class="text-white text-lg">${data.industry}</div>
                            </div>
                            ` : ''}
                        </div>
                        <div class="space-y-4">
                            ${data.phone ? `
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Phone</div>
                                <div class="text-white text-lg">${data.phone}</div>
                            </div>
                            ` : ''}
                            ${data.preferred_date ? `
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Preferred Date</div>
                                <div class="text-white text-lg">${data.preferred_date}</div>
                            </div>
                            ` : ''}
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Form Type</div>
                                <div class="text-white text-lg">${data.form_type === 'contact' ? 'Contact Form' : 'Tour Request'}</div>
                            </div>
                            <div>
                                <div class="text-bronze font-semibold text-sm uppercase tracking-wider">Submitted</div>
                                <div class="text-white text-lg">${data.created_at}</div>
                            </div>
                        </div>
                    </div>
                </div>
                ${data.message ? `
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                    <div class="text-bronze font-semibold text-sm uppercase tracking-wider mb-4">Message</div>
                    <div class="text-white/90 leading-relaxed whitespace-pre-wrap">${data.message}</div>
                </div>
                ` : ''}
                <div class="bg-white/5 backdrop-blur-md rounded-2xl p-4 border border-white/10">
                    <div class="text-white/60 text-sm">
                        <strong>IP Address:</strong> ${data.ip_address} | <strong>Submission ID:</strong> #${data.id}
                    </div>
                </div>
            `;

            // Set up reply button
            document.getElementById('modalReplyBtn').onclick = () => {
                window.location.href = `mailto:${data.email}?subject=Re: ${data.form_type === 'contact' ? 'Contact Form' : 'Tour Request'} Submission`;
            };

            document.getElementById('detailsModal').classList.remove('hidden');

            // Mark as read when viewing details
            if (!data.is_read) {
                markAsRead(data.id, false);
            }
        })
        .catch(error => {
            console.error('Error fetching submission details:', error);
        });
}

function closeModal() {
    document.getElementById('detailsModal').classList.add('hidden');
}

// Mark as read/unread functionality
function markAsRead(id, reload = true) {
    fetch(`/admin/form-submissions/${id}/mark-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && reload) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking as read:', error);
    });
}

function markAsUnread(id) {
    fetch(`/admin/form-submissions/${id}/mark-unread`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking as unread:', error);
    });
}

function markAllAsRead() {
    if (confirm('Mark all submissions as read?')) {
        fetch('/admin/form-submissions/mark-all-read', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error marking all as read:', error);
        });
    }
}

// Close modal when clicking outside
document.getElementById('detailsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
@endsection
