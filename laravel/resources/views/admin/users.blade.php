@extends('layouts.dashboard')

@section('page-title', 'User Management')

@section('background')
    <!-- Textured Background -->
    <div class="fixed inset-0 bg-gray-100" style="background-image: url('{{ asset('img/photo-wall-texture-pattern.webp') }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"></div>
    <!-- Overlay for readability -->
    <div class="fixed inset-0 bg-white/80"></div>
@endsection

@section('content')
    <div class="h-full p-6 overflow-hidden flex flex-col">
        <div class="max-w-7xl mx-auto w-full h-full flex flex-col">

            <!-- Header -->
            <div class="text-center mb-6 flex-shrink-0">
                <h2 class="text-3xl font-bold text-header mb-2">User Management</h2>
                <p class="text-gray-700 text-lg">Manage all user accounts and permissions</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-4 flex-shrink-0">
                    <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-green-800 font-medium text-sm">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-4 flex-shrink-0">
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-red-800 font-medium text-sm">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Users Table -->
            <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-gray-400 shadow-2xl overflow-hidden flex-1 min-h-0 flex flex-col" style="box-shadow: inset 0 2px 0 rgba(255,255,255,0.7), 0 12px 30px rgba(0,0,0,0.2), 0 6px 15px rgba(0,0,0,0.15);">
                <div class="p-4 border-b border-gray-300 flex-shrink-0">
                    <h3 class="text-xl font-bold text-gray-800">All Users ({{ $users->total() }})</h3>
                </div>



                @if($users->count() > 0)
                    <div class="overflow-auto flex-1">
                        <table class="w-full">
                            <thead class="bg-gray-100 sticky top-0">
                                <tr>
                                    <th class="px-4 py-3 text-left text-gray-800 font-bold text-sm">User</th>
                                    <th class="px-4 py-3 text-left text-gray-800 font-bold text-sm">Role</th>
                                    <th class="px-4 py-3 text-left text-gray-800 font-bold text-sm">Status</th>
                                    <th class="px-4 py-3 text-left text-gray-800 font-bold text-sm">Registered</th>
                                    <th class="px-4 py-3 text-left text-gray-800 font-bold text-sm">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach($users as $user)
                                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                                        <!-- User Info -->
                                        <td class="px-4 py-3">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-8 h-8 bg-header/10 rounded-full flex items-center justify-center">
                                                    <span class="text-header font-bold text-sm">{{ substr($user->name, 0, 1) }}</span>
                                                </div>
                                                <div>
                                                    <p class="text-gray-800 font-medium text-sm">{{ $user->name }}</p>
                                                    <p class="text-gray-600 text-xs">{{ $user->email }}</p>
                                                    @if($user->company)
                                                        <p class="text-gray-500 text-xs">{{ $user->company }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Role -->
                                        <td class="px-4 py-3">
                                            @if(Auth::user()->user_type === 'super_admin' && $user->id !== Auth::id())
                                                <form method="POST" action="{{ route('admin.users.role', $user) }}" class="inline-block">
                                                    @csrf
                                                    <select name="user_type" onchange="this.form.submit()"
                                                            class="bg-gray-100 border border-gray-300 rounded-lg px-2 py-1 text-gray-800 text-xs focus:outline-none focus:ring-2 focus:ring-header">
                                                        <option value="client" {{ $user->user_type === 'client' ? 'selected' : '' }}>Client</option>
                                                        <option value="admin" {{ $user->user_type === 'admin' ? 'selected' : '' }}>Admin</option>
                                                        <option value="super_admin" {{ $user->user_type === 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                                                    </select>
                                                </form>
                                            @else
                                                <span class="px-2 py-1 rounded-lg text-xs font-bold bg-header/10 text-header">
                                                    {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}
                                                </span>
                                            @endif
                                        </td>

                                        <!-- Status -->
                                        <td class="px-4 py-3">
                                            <div class="space-y-1">
                                                <span class="px-2 py-1 rounded-lg text-xs font-bold
                                                    @if($user->is_active) bg-green-100 text-green-700 @else bg-yellow-100 text-yellow-700 @endif">
                                                    {{ $user->is_active ? 'Active' : 'Pending' }}
                                                </span>
                                                @if($user->email_verified_at)
                                                    <span class="block px-2 py-1 rounded-lg text-xs font-bold bg-blue-100 text-blue-700">
                                                        Verified
                                                    </span>
                                                @else
                                                    <span class="block px-2 py-1 rounded-lg text-xs font-bold bg-red-100 text-red-700">
                                                        Unverified
                                                    </span>
                                                @endif
                                            </div>
                                        </td>

                                        <!-- Registered -->
                                        <td class="px-4 py-3">
                                            <p class="text-gray-700 text-xs">{{ $user->created_at->format('M j, Y') }}</p>
                                            <p class="text-gray-500 text-xs">{{ $user->created_at->diffForHumans() }}</p>
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-4 py-3">
                                            <div class="flex items-center space-x-1">
                                                @if(!$user->is_active)
                                                    <form method="POST" action="{{ route('admin.users.approve', $user) }}" class="inline-block">
                                                        @csrf
                                                        <button type="submit" class="bg-green-100 hover:bg-green-200 text-green-700 text-xs font-bold py-1 px-2 rounded transition-all duration-300">
                                                            Approve
                                                        </button>
                                                    </form>
                                                @else
                                                    @if($user->id !== Auth::id())
                                                        <form method="POST" action="{{ route('admin.users.deactivate', $user) }}" class="inline-block">
                                                            @csrf
                                                            <button type="submit" class="bg-yellow-100 hover:bg-yellow-200 text-yellow-700 text-xs font-bold py-1 px-2 rounded transition-all duration-300"
                                                                    onclick="return confirm('Are you sure you want to deactivate this user?')">
                                                                Deactivate
                                                            </button>
                                                        </form>
                                                    @endif
                                                @endif

                                                @if(Auth::user()->user_type === 'super_admin' && $user->id !== Auth::id())
                                                    <form method="POST" action="{{ route('admin.users.delete', $user) }}" class="inline-block">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="bg-red-100 hover:bg-red-200 text-red-700 text-xs font-bold py-1 px-2 rounded transition-all duration-300"
                                                                onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                            Delete
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($users->hasPages())
                        <div class="p-4 border-t border-gray-300 flex-shrink-0">
                            {{ $users->links() }}
                        </div>
                    @endif
                @else
                    <div class="p-8 text-center flex-1 flex items-center justify-center">
                        <p class="text-gray-500 text-lg">No users found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
