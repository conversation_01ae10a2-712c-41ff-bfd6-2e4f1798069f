@extends('layouts.dashboard')

@section('page-title', 'User Management')

@section('background')
    <!-- Textured Background -->
    <div class="fixed inset-0 bg-gray-100" style="background-image: url('{{ asset('img/photo-wall-texture-pattern.webp') }}'); background-size: cover; background-position: center; background-repeat: no-repeat;"></div>
    <!-- Overlay for readability -->
    <div class="fixed inset-0 bg-white/80"></div>
@endsection

@section('content')
    <div class="h-full p-6 overflow-hidden flex flex-col">
        <div class="max-w-7xl mx-auto w-full h-full flex flex-col">

            <!-- Header -->
            <div class="text-center mb-6 flex-shrink-0">
                <h2 class="text-3xl font-bold text-header mb-2">User Management</h2>
                <p class="text-gray-700 text-lg">Manage all user accounts and permissions</p>
            </div>

            <!-- Success/Error Messages -->
            @if(session('success'))
                <div class="mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-header/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-header/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-header" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <p class="text-black font-medium">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-red-400/30 shadow-xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-400/20 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <p class="text-black font-medium">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Users Table -->
            <div class="bg-white/10 backdrop-blur-md rounded-3xl border border-white/20 shadow-xl overflow-hidden">
                <div class="p-6 border-b border-white/20">
                    <h3 class="text-2xl font-bold text-header">All Users ({{ $users->total() }})</h3>
                </div>
                
                @if($users->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-white/5">
                                <tr>
                                    <th class="px-6 py-4 text-left text-header font-semibold">User</th>
                                    <th class="px-6 py-4 text-left text-header font-semibold">Role</th>
                                    <th class="px-6 py-4 text-left text-header font-semibold">Status</th>
                                    <th class="px-6 py-4 text-left text-header font-semibold">Registered</th>
                                    <th class="px-6 py-4 text-left text-header font-semibold">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-white/10">
                                @foreach($users as $user)
                                    <tr class="hover:bg-white/5 transition-colors duration-200">
                                        <!-- User Info -->
                                        <td class="px-6 py-4">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-10 h-10 bg-bronze/20 rounded-full flex items-center justify-center">
                                                    <span class="text-header font-bold">{{ substr($user->name, 0, 1) }}</span>
                                                </div>
                                                <div>
                                                    <p class="text-black font-medium">{{ $user->name }}</p>
                                                    <p class="text-black/60 text-sm">{{ $user->email }}</p>
                                                    @if($user->company)
                                                        <p class="text-black/50 text-xs">{{ $user->company }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>

                                        <!-- Role -->
                                        <td class="px-6 py-4">
                                            @if(Auth::user()->user_type === 'super_admin' && $user->id !== Auth::id())
                                                <form method="POST" action="{{ route('admin.users.role', $user) }}" class="inline-block">
                                                    @csrf
                                                    <select name="user_type" onchange="this.form.submit()" 
                                                            class="bg-white/20 border border-white/30 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-bronze">
                                                        <option value="client" {{ $user->user_type === 'client' ? 'selected' : '' }}>Client</option>
                                                        <option value="admin" {{ $user->user_type === 'admin' ? 'selected' : '' }}>Admin</option>
                                                        <option value="super_admin" {{ $user->user_type === 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                                                    </select>
                                                </form>
                                            @else
                                                <span class="px-3 py-1 rounded-full text-xs font-medium bg-bronze/20 text-bronze">
                                                    {{ ucfirst(str_replace('_', ' ', $user->user_type)) }}
                                                </span>
                                            @endif
                                        </td>

                                        <!-- Status -->
                                        <td class="px-6 py-4">
                                            <div class="space-y-1">
                                                <span class="px-3 py-1 rounded-full text-xs font-medium
                                                    @if($user->is_active) bg-green-500/20 text-green-400 @else bg-yellow-500/20 text-yellow-400 @endif">
                                                    {{ $user->is_active ? 'Active' : 'Pending' }}
                                                </span>
                                                @if($user->email_verified_at)
                                                    <span class="block px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                                                        Email Verified
                                                    </span>
                                                @else
                                                    <span class="block px-3 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-400">
                                                        Email Unverified
                                                    </span>
                                                @endif
                                            </div>
                                        </td>

                                        <!-- Registered -->
                                        <td class="px-6 py-4">
                                            <p class="text-black/80 text-sm">{{ $user->created_at->format('M j, Y') }}</p>
                                            <p class="text-black/60 text-xs">{{ $user->created_at->diffForHumans() }}</p>
                                        </td>

                                        <!-- Actions -->
                                        <td class="px-6 py-4">
                                            <div class="flex items-center space-x-2">
                                                @if(!$user->is_active)
                                                    <form method="POST" action="{{ route('admin.users.approve', $user) }}" class="inline-block">
                                                        @csrf
                                                        <button type="submit" class="bg-green-500/20 hover:bg-green-500/30 text-green-400 text-xs font-medium py-1 px-3 rounded-lg transition-all duration-300">
                                                            Approve
                                                        </button>
                                                    </form>
                                                @else
                                                    @if($user->id !== Auth::id())
                                                        <form method="POST" action="{{ route('admin.users.deactivate', $user) }}" class="inline-block">
                                                            @csrf
                                                            <button type="submit" class="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 text-xs font-medium py-1 px-3 rounded-lg transition-all duration-300"
                                                                    onclick="return confirm('Are you sure you want to deactivate this user?')">
                                                                Deactivate
                                                            </button>
                                                        </form>
                                                    @endif
                                                @endif

                                                @if(Auth::user()->user_type === 'super_admin' && $user->id !== Auth::id())
                                                    <form method="POST" action="{{ route('admin.users.delete', $user) }}" class="inline-block">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="bg-red-500/20 hover:bg-red-500/30 text-red-400 text-xs font-medium py-1 px-3 rounded-lg transition-all duration-300"
                                                                onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                            Delete
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($users->hasPages())
                        <div class="p-6 border-t border-white/20">
                            {{ $users->links() }}
                        </div>
                    @endif
                @else
                    <div class="p-12 text-center">
                        <p class="text-white/60 text-lg">No users found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
