<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $subject }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #cba015 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f8f9fa;
            padding: 30px;
            border: 1px solid #e9ecef;
        }
        .field {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #cba015;
        }
        .field-label {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }
        .field-value {
            color: #555;
        }
        .footer {
            background: #2d3748;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ $subject }}</h1>
        <p>New {{ $submission->form_type === 'contact' ? 'Contact' : 'Tour' }} Form Submission</p>
    </div>

    <div class="content">
        <div class="field">
            <div class="field-label">Name:</div>
            <div class="field-value">{{ $submission->name }}</div>
        </div>

        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">{{ $submission->email }}</div>
        </div>

        @if($submission->company)
        <div class="field">
            <div class="field-label">Company:</div>
            <div class="field-value">{{ $submission->company }}</div>
        </div>
        @endif

        @if($submission->industry)
        <div class="field">
            <div class="field-label">Industry:</div>
            <div class="field-value">{{ $submission->industry }}</div>
        </div>
        @endif

        @if($submission->phone)
        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{ $submission->phone }}</div>
        </div>
        @endif

        @if($submission->preferred_date)
        <div class="field">
            <div class="field-label">Preferred Tour Date:</div>
            <div class="field-value">{{ $submission->preferred_date->format('F j, Y') }}</div>
        </div>
        @endif

        @if($submission->message)
        <div class="field">
            <div class="field-label">Message:</div>
            <div class="field-value">{{ $submission->message }}</div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Submitted:</div>
            <div class="field-value">{{ $submission->created_at->format('F j, Y \a\t g:i A') }}</div>
        </div>

        <div class="field">
            <div class="field-label">IP Address:</div>
            <div class="field-value">{{ $submission->ip_address }}</div>
        </div>
    </div>

    <div class="footer">
        <p>Cache River Mill & Steel Works</p>
        <p>This is an automated notification from your website contact form.</p>
    </div>
</body>
</html>
