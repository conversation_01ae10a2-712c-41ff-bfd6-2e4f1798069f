@import url('https://fonts.googleapis.com/css2?family=Jost:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body.loading {
    overflow: hidden;
}
.gradient {
    height: 10%;
    background: linear-gradient(0deg, transparent, #e0e0e0 80%) no-repeat;
}
.fade-in-bg {
    --fade-color: #e0e0e0;
    background: radial-gradient(
            circle,
            var(--fade-color) 0%,
            var(--fade-color) 60%,
            transparent 100%
    );
}
h1, .h1 {
    font-family: serif;
    font-size: 3.5rem;
    font-weight: 100;
    letter-spacing: 0;
    line-height: 1.13;
    text-transform: none;
    color: #074975;
}
.circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #074975;
    border: 3px solid #e8e8e8;
    text-align: center;
    vertical-align: middle;
    display: table-cell;
}
.bar {
    width: 100%;
    height: 100px;
    background-color: black;
    opacity: 0.8;
    align-items: center;
}
#page-loader {
    will-change: opacity;
    transition-property: opacity;
    transition-duration: 700ms;
    transition-timing-function: ease-in-out;
}
#page-loader .scale-90 {
    transform: scale(0.9);
    transition: transform 0.7s ease-in-out;
}
[x-cloak] {
    display: none !important;
}
.perspective {
    perspective: 1000px;
}
.card-flip {
    transform-style: preserve-3d;
    transition: transform 0.6s ease-in-out;
}
.card-hover:hover .card-flip {
    transform: rotateX(180deg);
}
.card-face {
    backface-visibility: hidden;
}
.card-back {
    transform: rotateX(180deg);
}
.no-table-borders td,
.no-table-borders th,
.no-table-borders tr {
    border: none !important;
}
.about-gradient {
    background-image: linear-gradient(to bottom, #042c48, #14283f, #1b2436, #1d202d, #1d1d25, #242329, #2a2a2d, #313131, #444444, #575757, #6b6b6b, #808080);
}
.prose table {
    width: 100%;
    border-collapse: collapse;
    margin-left: auto;
    margin-right: auto;
}
.prose table tr td:nth-child(2) {
    padding-left: 2rem;
}
.prose td h1 {
    margin-top: 0.5rem;
    margin-bottom: 0.25rem;
    font-weight: 600;
    font-size: 1.25rem;
}
.prose td i {
    font-size: 1.25rem;
    color: #074975;
}
.prose h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}
.clip-hexagon {
    clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}
.clip-dome {
    clip-path: ellipse(100% 60% at 50% 0%);
}
.clip-gable {
    clip-path: polygon(0 100%, 50% 0, 100% 100%);
}
.clip-angle {
    clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}
.clip-roman-arch {
    clip-path: ellipse(100% 80% at 50% 20%);
}

/* Pin the section scroll container for stable animation */
[data-section-scroll] {
    position: sticky;
    top: 0;
    z-index: 10;
}
