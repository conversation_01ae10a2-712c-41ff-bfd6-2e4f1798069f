<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('company')->nullable()->after('email');
            $table->string('license_number')->nullable()->after('company');
            $table->enum('user_type', ['architect', 'designer', 'contractor', 'admin'])->default('architect')->after('license_number');
            $table->text('bio')->nullable()->after('user_type');
            $table->string('phone')->nullable()->after('bio');
            $table->timestamp('approved_at')->nullable()->after('phone');
            $table->boolean('is_active')->default(false)->after('approved_at');
            $table->string('avatar')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'company',
                'license_number',
                'user_type',
                'bio',
                'phone',
                'approved_at',
                'is_active',
                'avatar'
            ]);
        });
    }
};
