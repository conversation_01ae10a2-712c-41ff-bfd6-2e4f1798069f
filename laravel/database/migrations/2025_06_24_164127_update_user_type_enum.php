<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('user_type');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->enum('user_type', ['client', 'architect', 'designer', 'contractor', 'admin', 'super_admin'])->default('client')->after('license_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('user_type');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->enum('user_type', ['architect', 'designer', 'contractor', 'admin'])->default('architect')->after('license_number');
        });
    }
};
