<?php

use App\Http\Controllers\PageController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;
use Illuminate\Support\Facades\Route;

// Main Site Routes
Route::get('/', [PageController::class, 'home'])->name('home');
Route::get('/about-us', [PageController::class, 'about'])->name('about');
Route::get('/contact-us', [PageController::class, 'contact'])->name('contact-us');
Route::get('/for-professionals', [PageController::class, 'professionals'])->name('professionals');
Route::get('/shop-tour', [PageController::class, 'tour'])->name('tour');
Route::get('/kc-baking-powder-building', [PageController::class, 'building'])->name('building');

// Portfolio Routes
Route::get('/portfolio', [ProjectController::class, 'index'])->name('portfolio');
Route::get('/projects/{slug}', [ProjectController::class, 'show'])->name('projects.show');

// Custom Work Routes
Route::get('/custom-work', [PageController::class, 'customWork'])->name('custom-work');
Route::get('/cabinets-casework', [PageController::class, 'customWorkCabinets'])->name('custom-work.cabinets');
Route::get('/commercial', [PageController::class, 'customWorkCommercial'])->name('custom-work.commercial');
Route::get('/doors-windows', [PageController::class, 'customWorkDoors'])->name('custom-work.doors');
Route::get('/furniture-fabrication', [PageController::class, 'customWorkFurniture'])->name('custom-work.furniture');
Route::get('/manufacturing', [PageController::class, 'customWorkManufacturing'])->name('custom-work.manufacturing');
Route::get('/trim-moulding', [PageController::class, 'customWorkTrim'])->name('custom-work.trim');
Route::get('/design-visualization', [PageController::class, 'customWorkDesign'])->name('custom-work.design');
Route::get('/metalworking', [PageController::class, 'customWorkMetalworking'])->name('custom-work.metal');
Route::get('/woodworking', [PageController::class, 'customWorkWoodworking'])->name('custom-work.wood');

// Blog Routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Contact Form
Route::post('/contact-us', [ContactController::class, 'submit'])->name('contact.submit');

// User