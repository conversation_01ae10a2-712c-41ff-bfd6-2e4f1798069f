<?php

use App\Http\Controllers\PageController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

// Main Site Routes
Route::get('/', [PageController::class, 'home'])->name('home');
Route::get('/about-us', [PageController::class, 'about'])->name('about');
Route::get('/contact-us', [PageController::class, 'contact'])->name('contact-us');
Route::get('/for-professionals', [PageController::class, 'professionals'])->name('professionals');
Route::get('/shop-tour', [PageController::class, 'tour'])->name('tour');
Route::get('/kc-baking-powder-building', [PageController::class, 'building'])->name('building');

// Portfolio Routes
Route::get('/portfolio', [ProjectController::class, 'index'])->name('portfolio');
Route::get('/projects/{slug}', [ProjectController::class, 'show'])->name('projects.show');

// Custom Work Routes
Route::get('/custom-work', [PageController::class, 'customWork'])->name('custom-work');
Route::get('/cabinets-casework', [PageController::class, 'customWorkCabinets'])->name('custom-work.cabinets');
Route::get('/commercial', [PageController::class, 'customWorkCommercial'])->name('custom-work.commercial');
Route::get('/doors-windows', [PageController::class, 'customWorkDoors'])->name('custom-work.doors');
Route::get('/furniture-fabrication', [PageController::class, 'customWorkFurniture'])->name('custom-work.furniture');
Route::get('/manufacturing', [PageController::class, 'customWorkManufacturing'])->name('custom-work.manufacturing');
Route::get('/trim-moulding', [PageController::class, 'customWorkTrim'])->name('custom-work.trim');
Route::get('/design-visualization', [PageController::class, 'customWorkDesign'])->name('custom-work.design');
Route::get('/metalworking', [PageController::class, 'customWorkMetalworking'])->name('custom-work.metal');
Route::get('/woodworking', [PageController::class, 'customWorkWoodworking'])->name('custom-work.wood');

// Blog Routes
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Contact Form
Route::post('/contact-us', [ContactController::class, 'submit'])->name('contact.submit');
Route::post('/contact-form', [PageController::class, 'submitContactForm'])->name('contact.form.submit');
Route::post('/tour-form', [PageController::class, 'submitTourForm'])->name('tour.form.submit');

// Authentication Routes
Route::get('/login', [PageController::class, 'login'])->name('login');
Route::post('/login', [PageController::class, 'loginSubmit'])->name('login.submit');
Route::post('/logout', [PageController::class, 'logout'])->name('logout');
Route::get('/register', [PageController::class, 'register'])->name('register');
Route::post('/register', [PageController::class, 'registerSubmit'])->name('register.submit');

// Email Verification Routes
Route::get('/email/verify', [PageController::class, 'verifyNotice'])->name('verification.notice');
Route::get('/email/verify/{id}/{hash}', [PageController::class, 'verifyEmail'])->name('verification.verify');
Route::post('/email/verification-notification', [PageController::class, 'resendVerification'])->name('verification.send');

// Password Reset Routes
Route::get('/forgot-password', [PageController::class, 'forgotPassword'])->name('password.request');
Route::post('/forgot-password', [PageController::class, 'sendResetLink'])->name('password.email');
Route::get('/reset-password/{token}', [PageController::class, 'resetPassword'])->name('password.reset');
Route::post('/reset-password', [PageController::class, 'updatePassword'])->name('password.update');

// Protected Routes (require login and email verification)
Route::get('/dashboard', [PageController::class, 'dashboard'])->name('dashboard')->middleware(['auth', 'verified']);

// Profile Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::post('/profile/image', [ProfileController::class, 'uploadImage'])->name('profile.image.upload');
    Route::delete('/profile/image', [ProfileController::class, 'removeImage'])->name('profile.image.remove');
});

// Admin Routes (require admin privileges)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/users', [AdminController::class, 'users'])->name('users');
    Route::post('/users/{user}/approve', [AdminController::class, 'approveUser'])->name('users.approve');
    Route::post('/users/{user}/deactivate', [AdminController::class, 'deactivateUser'])->name('users.deactivate');
    Route::post('/users/{user}/role', [AdminController::class, 'updateUserRole'])->name('users.role');
    Route::delete('/users/{user}', [AdminController::class, 'deleteUser'])->name('users.delete');
    Route::get('/form-submissions', [PageController::class, 'adminFormSubmissions'])->name('form-submissions');
    Route::get('/form-submissions/{id}', [PageController::class, 'getFormSubmission'])->name('form-submission.details');
});