<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Services\WordPressApiService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\FormSubmission;
class PageController extends Controller
{
    public function contact(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('contact-us');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        // Get navigation data from global settings
        $nav = [];
        if (isset($global['acf']['nav_json'])) {
            $nav = json_decode($global['acf']['nav_json'], true) ?? [];
        }

        return view('pages.contact', compact('page', 'global', 'nav', 'colorMenu','colorLogo'));
    }
    public function about(WordPressApiService $api)
    {

        $page = $api->getPageBySlug('about-us');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        // Get navigation data from global settings
        $nav = [];
        if (isset($global['acf']['nav_json'])) {
            $nav = json_decode($global['acf']['nav_json'], true) ?? [];
        }

        return view('pages.about', compact('page', 'global', 'nav', 'colorMenu','colorLogo'));
    }
    public function tour(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('shop-tour');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        // Get navigation data from global settings
        $nav = [];
        if (isset($global['acf']['nav_json'])) {
            $nav = json_decode($global['acf']['nav_json'], true) ?? [];
        }

        return view('pages.tour', compact('page', 'global', 'nav', 'colorMenu','colorLogo'));
    }
    public function professionals(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('for-professionals');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('pages.professionals', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function millwork(WordPressApiService $api)
    {

        $page = $api->getPageBySlug('millwork');
        $global = $api->getPageById(4061);
        return view('pages.millwork', compact('page', 'global'));
    }
    public function home(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('home');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'header';

        // Quotes setup
        $quotes = [];
        for ($i = 1; $i <= 5; $i++) {
            $quote = $page['acf']['quote_' . $i] ?? null;
            $author = $page['acf']['quote_' . $i . '_author'] ?? null;

            if ($quote) {
                $quotes[] = [
                    'text' => $quote,
                    'author' => $author,
                ];
            }
        }

        // Fetch all projects with embedded data
        $allProjects = $api->get('project', [
            '_embed' => true,
            'per_page' => 100,
        ]);

        // Get one project per portfolio category
        $featuredProjects = [];

        foreach ($allProjects as $project) {
            $terms = $project['_embedded']['wp:term'][0] ?? [];

            foreach ($terms as $term) {
                if ($term['taxonomy'] === 'portfolio-category') {
                    $slug = $term['slug'];

                    if (!isset($featuredProjects[$slug])) {
                        $project['category'] = $term; // attach for blade use
                        $featuredProjects[$slug] = $project;
                    }
                }
            }
        }

        // Get navigation data from global settings
        $nav = [];
        if (isset($global['acf']['nav_json'])) {
            $nav = json_decode($global['acf']['nav_json'], true) ?? [];
        }

        return view('pages.home', compact('page', 'global', 'nav', 'quotes', 'featuredProjects','colorMenu','colorLogo'));
    }

    //Custom Work
    public function customWork(WordPressApiService $api)
    {
        // 1. Cache the page data
        $page = Cache::remember("wp_page_custom_work", now()->addMinutes(30), function() use ($api) {
            return $api->getPageBySlug('custom-work');
        });

        // 2. Cache the global settings
        $global = Cache::remember("wp_global_settings", now()->addMinutes(30), function() use ($api) {
            return $api->getPageById(4061);
        });

        // 3. Cache the child pages (“What We Can Do”)
        $customPages = Cache::remember("wp_pages_parent_{$page['id']}", now()->addMinutes(30), function() use ($api, $page) {
            return $api->getPagesByParentId($page['id']);
        });

        // 4. Cache the blog posts
        $posts = Cache::remember("wp_posts_category_103", now()->addMinutes(30), function() use ($api) {
            return $api->getPosts([
                'per_page'   => 3,
                'categories' => 103,
            ]);
        });

        $colorLogo = $colorMenu = 'white';

        return view(
            'pages.custom-work.index',
            compact('page','global','customPages','posts','colorMenu','colorLogo')
        );
    }

    public function customWorkCabinets(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('cabinets-casework', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.cabinets', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkCommercial(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('commercial', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.commercial', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkDesign(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('design-visualization', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('pages.custom-work.design', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkDoors(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('doors-windows', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.doors', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkFurniture(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('furniture-fabrication', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.furniture', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkManufacturing(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('manufacturing', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.manufacturing', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkMetalworking(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('metalworking', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('pages.custom-work.metal', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkTrim(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('trim-moulding', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'header';

        return view('pages.custom-work.trim', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function customWorkWoodworking(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('woodworking', true);
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'white';

        return view('pages.custom-work.wood', compact('page', 'global','colorMenu','colorLogo'));
    }

    public function building(WordPressApiService $api)
    {
        $page = $api->getPageBySlug('kc-baking-powder-building');
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('pages.building', compact('page', 'global','colorMenu','colorLogo'));
    }
    public function show($slug)
    {
        $page = Http::get("https://your-wp-site.com/wp-json/wp/v2/pages?slug={$slug}&_embed")->json()[0];

        // Fetch all projects
        $projects = Http::get('https://your-wp-site.com/wp-json/wp/v2/project?per_page=100&_embed')->json();

        // Collect one project per taxonomy
        $seen = [];
        $featuredProjects = collect($projects)->filter(function ($project) use (&$seen) {
            $terms = $project['project_category'] ?? [];
            foreach ($terms as $term) {
                if (!in_array($term['slug'], $seen)) {
                    $seen[] = $term['slug'];
                    return true;
                }
            }
            return false;
        });

        return view('pages.home', [
            'page' => $page,
            'featuredProjects' => $featuredProjects,
        ]);
    }

    public function login(WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.login', compact('global', 'colorMenu', 'colorLogo'));
    }

    public function loginSubmit(Request $request)
    {
        // Validate the form data
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        // Attempt to authenticate the user
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials, $request->filled('remember'))) {
            // Authentication successful
            $request->session()->regenerate();

            // Redirect to dashboard (we'll create this next)
            return redirect()->intended('/dashboard')->with('success', 'Welcome back, ' . Auth::user()->name . '!');
        }

        // Authentication failed
        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->withInput($request->except('password'));
    }

    public function dashboard(WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.dashboard', compact('global', 'colorMenu', 'colorLogo'));
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')->with('success', 'You have been logged out successfully.');
    }

    public function register(WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.register', compact('global', 'colorMenu', 'colorLogo'));
    }

    public function registerSubmit(Request $request)
    {
        // Validate registration data
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'company' => 'nullable|string|max:255',
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
            'company' => $request->company,
            'user_type' => 'client',
            'is_active' => false, // Requires admin approval
        ]);

        // Send email verification
        $user->sendEmailVerificationNotification();

        return redirect()->route('login')->with('success', 'Registration successful! Please check your email to verify your account.');
    }

    public function verifyNotice(WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.verify-email', compact('global', 'colorMenu', 'colorLogo'));
    }

    public function verifyEmail(Request $request)
    {
        $user = User::find($request->route('id'));

        if (!$user || !hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return redirect()->route('login')->withErrors(['email' => 'Invalid verification link.']);
        }

        if ($user->hasVerifiedEmail()) {
            return redirect()->route('dashboard')->with('success', 'Email already verified!');
        }

        $user->markEmailAsVerified();

        return redirect()->route('dashboard')->with('success', 'Email verified successfully! Welcome to your dashboard.');
    }

    public function resendVerification(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return redirect()->route('dashboard');
        }

        $request->user()->sendEmailVerificationNotification();

        return back()->with('success', 'Verification email sent!');
    }

    public function forgotPassword(WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.forgot-password', compact('global', 'colorMenu', 'colorLogo'));
    }

    public function sendResetLink(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
            ? back()->with('success', 'Password reset link sent to your email!')
            : back()->withErrors(['email' => 'We could not find a user with that email address.']);
    }

    public function resetPassword(Request $request, WordPressApiService $api)
    {
        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        return view('auth.reset-password', [
            'token' => $request->token,
            'email' => $request->email,
            'global' => $global,
            'colorMenu' => $colorMenu,
            'colorLogo' => $colorLogo,
        ]);
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ]);

                $user->save();
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('success', 'Password reset successfully! You can now log in.')
            : back()->withErrors(['email' => 'There was an error resetting your password.']);
    }

    /**
     * Handle contact form submission
     */
    public function submitContactForm(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|max:2000',
        ]);

        // Store in database
        $submission = FormSubmission::create([
            'form_type' => 'contact',
            'name' => $validated['name'],
            'email' => $validated['email'],
            'company' => $request->input('company'),
            'phone' => $request->input('phone'),
            'message' => $validated['message'],
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Send email notification
        $this->sendFormNotificationEmail($submission);

        Log::info('Contact form submitted successfully', [
            'submission_id' => $submission->id,
            'name' => $submission->name,
            'email' => $submission->email
        ]);

        return back()->with('success', 'Thank you for your message! We\'ve sent a confirmation email to ' . $validated['email'] . ' and will get back to you within 24 hours.');
    }

    /**
     * Handle tour form submission
     */
    public function submitTourForm(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'company' => 'nullable|string|max:255',
            'industry' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'preferred_date' => 'required|date|after:today',
            'message' => 'nullable|string|max:2000',
        ]);

        // Store in database
        $submission = FormSubmission::create([
            'form_type' => 'tour',
            'name' => $validated['name'],
            'email' => $validated['email'],
            'company' => $request->input('company'),
            'industry' => $request->input('industry'),
            'phone' => $request->input('phone'),
            'preferred_date' => $validated['preferred_date'],
            'message' => $request->input('message'),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Send email notification
        $this->sendFormNotificationEmail($submission);

        Log::info('Tour form submitted successfully', [
            'submission_id' => $submission->id,
            'name' => $submission->name,
            'email' => $submission->email,
            'preferred_date' => $submission->preferred_date
        ]);

        return back()->with('success', 'Thank you for scheduling a tour! We\'ve sent a confirmation email to ' . $validated['email'] . ' and will contact you within 24 hours to confirm your visit for ' . date('F j, Y', strtotime($validated['preferred_date'])) . '.');
    }

    /**
     * Send email notification for form submissions
     */
    private function sendFormNotificationEmail(FormSubmission $submission)
    {
        try {
            $adminEmail = config('mail.admin_email', '<EMAIL>');

            $subject = $submission->form_type === 'contact'
                ? 'New Contact Form Submission'
                : 'New Tour Request';

            $emailData = [
                'submission' => $submission,
                'subject' => $subject,
            ];

            // Send to admin
            Mail::send('emails.form-notification', $emailData, function ($message) use ($adminEmail, $subject, $submission) {
                $message->to($adminEmail)
                        ->subject($subject)
                        ->replyTo($submission->email, $submission->name);
            });

            // Send confirmation to user
            $confirmationSubject = $submission->form_type === 'contact'
                ? 'Thank you for contacting Cache River Mill & Steel Works'
                : 'Thank you for your tour request';

            Mail::send('emails.form-confirmation', $emailData, function ($message) use ($submission, $confirmationSubject) {
                $message->to($submission->email, $submission->name)
                        ->subject($confirmationSubject);
            });

            Log::info('Form emails sent successfully', ['submission_id' => $submission->id, 'type' => $submission->form_type]);

        } catch (\Exception $e) {
            Log::error('Failed to send form emails', [
                'submission_id' => $submission->id,
                'error' => $e->getMessage()
            ]);
            // Don't throw the exception to avoid breaking the form submission
        }
    }

    /**
     * Admin view for form submissions
     */
    public function adminFormSubmissions()
    {
        $submissions = FormSubmission::latest()->get();
        return view('admin.form-submissions', compact('submissions'));
    }

    /**
     * Get form submission details (AJAX)
     */
    public function getFormSubmission($id)
    {
        $submission = FormSubmission::findOrFail($id);
        return response()->json([
            'id' => $submission->id,
            'form_type' => $submission->form_type,
            'name' => $submission->name,
            'email' => $submission->email,
            'company' => $submission->company,
            'industry' => $submission->industry,
            'phone' => $submission->phone,
            'preferred_date' => $submission->preferred_date ? $submission->preferred_date->format('F j, Y') : null,
            'message' => $submission->message,
            'ip_address' => $submission->ip_address,
            'created_at' => $submission->created_at->format('F j, Y \a\t g:i A'),
            'is_read' => $submission->is_read,
        ]);
    }

    /**
     * Mark form submission as read
     */
    public function markSubmissionAsRead($id)
    {
        $submission = FormSubmission::findOrFail($id);
        $submission->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }

    /**
     * Mark form submission as unread
     */
    public function markSubmissionAsUnread($id)
    {
        $submission = FormSubmission::findOrFail($id);
        $submission->update(['is_read' => false]);

        return response()->json(['success' => true]);
    }

    /**
     * Mark all form submissions as read
     */
    public function markAllSubmissionsAsRead()
    {
        FormSubmission::where('is_read', false)->update(['is_read' => true]);

        return response()->json(['success' => true]);
    }
}
