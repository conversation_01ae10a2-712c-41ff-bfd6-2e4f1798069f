<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\FormSubmission;
use App\Services\WordPressApiService;

class AdminController extends Controller
{
    public function dashboard(WordPressApiService $api)
    {
        // Only allow admin and super_admin users
        if (!in_array(Auth::user()->user_type, ['admin', 'super_admin'])) {
            return redirect()->route('dashboard')->with('error', 'Access denied. Admin privileges required.');
        }

        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        // Get user statistics
        $totalUsers = User::count();
        $pendingUsers = User::where('is_active', false)->count();
        $activeUsers = User::where('is_active', true)->count();
        $recentUsers = User::latest()->take(5)->get();

        // Get form submission statistics
        $totalSubmissions = FormSubmission::count();
        $unreadSubmissions = FormSubmission::where('is_read', false)->count();

        return view('admin.dashboard', compact(
            'global', 'colorMenu', 'colorLogo',
            'totalUsers', 'pendingUsers', 'activeUsers', 'recentUsers',
            'totalSubmissions', 'unreadSubmissions'
        ));
    }

    public function users(WordPressApiService $api)
    {
        // Only allow admin and super_admin users
        if (!in_array(Auth::user()->user_type, ['admin', 'super_admin'])) {
            return redirect()->route('dashboard')->with('error', 'Access denied. Admin privileges required.');
        }

        // Get global settings for header/footer
        $global = $api->getPageById(4061);
        $colorLogo = $colorMenu = 'bronze';

        // Get all users with pagination
        $users = User::orderBy('created_at', 'desc')->paginate(15);

        return view('admin.users', compact('global', 'colorMenu', 'colorLogo', 'users'));
    }

    public function approveUser(Request $request, User $user)
    {
        // Only allow admin and super_admin users
        if (!in_array(Auth::user()->user_type, ['admin', 'super_admin'])) {
            return redirect()->back()->with('error', 'Access denied. Admin privileges required.');
        }

        $user->update([
            'is_active' => true,
            'approved_at' => now(),
        ]);

        return redirect()->back()->with('success', "User {$user->name} has been approved and activated.");
    }

    public function deactivateUser(Request $request, User $user)
    {
        // Only allow admin and super_admin users
        if (!in_array(Auth::user()->user_type, ['admin', 'super_admin'])) {
            return redirect()->back()->with('error', 'Access denied. Admin privileges required.');
        }

        // Prevent deactivating yourself
        if ($user->id === Auth::id()) {
            return redirect()->back()->with('error', 'You cannot deactivate your own account.');
        }

        $user->update(['is_active' => false]);

        return redirect()->back()->with('success', "User {$user->name} has been deactivated.");
    }

    public function updateUserRole(Request $request, User $user)
    {
        // Only allow super_admin users to change roles
        if (Auth::user()->user_type !== 'super_admin') {
            return redirect()->back()->with('error', 'Access denied. Super admin privileges required.');
        }

        $request->validate([
            'user_type' => 'required|in:client,admin,super_admin'
        ]);

        // Prevent changing your own role
        if ($user->id === Auth::id()) {
            return redirect()->back()->with('error', 'You cannot change your own role.');
        }

        $user->update(['user_type' => $request->user_type]);

        return redirect()->back()->with('success', "User {$user->name} role updated to {$request->user_type}.");
    }

    public function deleteUser(Request $request, User $user)
    {
        // Only allow super_admin users to delete
        if (Auth::user()->user_type !== 'super_admin') {
            return redirect()->back()->with('error', 'Access denied. Super admin privileges required.');
        }

        // Prevent deleting yourself
        if ($user->id === Auth::id()) {
            return redirect()->back()->with('error', 'You cannot delete your own account.');
        }

        $userName = $user->name;
        $user->delete();

        return redirect()->back()->with('success', "User {$userName} has been deleted.");
    }
}
