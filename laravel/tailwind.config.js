/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js',
    ],
    safelist: [
        'bg-header',
        'text-bronze',
        'bg-charcoal',
        'bg-lightgray',
        'text-lightgray',
        'text-gray',
        'text-header',
        'text-cream',
        'bg-cream',
        'serif',
        'sans',
        '[x-cloak]',
        'scale-0',
        'scale-100',
        'transition-transform',
        'duration-700',
        'bg-header-watermark',
        'bg-about',
        'animate-glow',
        'bg-yellow-300',
    ],
    theme: {
        extend: {
            fontFamily: {
                sans: ['Jost', 'ui-sans-serif', 'system-ui'],
                serif: ['Cormorant Garamond', 'ui-serif', 'system-ui'],
            },
            colors: {
                cream: '#faf8f3',
                charcoal: '#1a1a1a',
                bronze: '#FFCE1B',
                gray: '#808080',
                lightgray: '#dcdcdc',
                header: '#074975',
                about: '#14507c',
            },
            spacing: {
                '18': '4.5rem',
                '22': '5.5rem',
                '30': '7.5rem',
            },
            container: {
                center: true,
                padding: '1.5rem',
                screens: {
                    lg: '1024px',
                    xl: '1200px',
                },
            },
            gradient: {
                height: '10%',
                background: 'linear-gradient(0deg, transparent, #FFCE1B 80%) no-repeat',
            },
            keyframes: {
                'fade-in-up': {
                    '0%': { opacity: 0, transform: 'translateY(20px)' },
                    '100%': { opacity: 1, transform: 'translateY(0)' },
                },
                'zoom-in': {
                    '0%': { opacity: 0, transform: 'scale(0.95)' },
                    '100%': { opacity: 1, transform: 'scale(1)' },
                },
                'move-vertical': {
                    '0%': {
                        transform: 'translateX(var(--shift-from-x, -50%)) translateY(var(--shift-from-y, 0))',
                    },
                    '100%': {
                        transform: 'translateX(var(--shift-to-x, -50%)) translateY(var(--shift-to-y, 0))',
                        color: '#fff',
                        fill: '#fff',
                    },
                },
                'scale-up-center': {
                    '0%': {
                        clipPath: 'inset(150px 421px 92px 421px round 226px)',
                    },
                    '50%': {
                        clipPath: 'inset(150px 345px 92px 345px round 226px)',
                    },
                    '100%': {
                        clipPath: 'inset(0 round 0)',
                    },
                },
                'infinity-rotate': {
                    '0%': {
                        transform: 'rotate(-360deg)',
                    },
                    '100%': {
                        transform: 'rotate(0)',
                    },
                },
                glow: {
                    '0%, 100%': {
                        opacity: '0.5',
                        boxShadow: '0 0 5px 2px rgba(255, 215, 0, 0.4)',
                    },
                    '50%': {
                        opacity: '1',
                        boxShadow: '0 0 15px 8px rgba(255, 255, 0, 0.9)',
                    },
                },
            },
            animation: {
                'fade-in-up': 'fade-in-up 0.5s ease-out both',
                'zoom-in': 'zoom-in 0.4s ease-out both',
                'move-vertical': 'move-vertical 0.6s ease-out 0.6s both',
                'scale-up-center': 'scale-up-center 0.6s ease-out 0.6s both',
                'infinity-rotate': 'infinity-rotate 30s linear 4s infinite',
                'glow': 'glow 1.8s ease-in-out infinite',
                'flicker': 'flicker 2s ease-in-out infinite',
            },
            backgroundImage: {
                'header-watermark': "url('/img/loader_bg.png')",
            },
        },
    },
    plugins: [
        require('@tailwindcss/typography'),
    ],
};
