/*!==========================================================================
 * ==========================================================================
 * ==========================================================================
 *
 * Rhye – AJAX Portfolio HTML5 Template
 *
 * [Table of Contents]
 *
 * 1. Aside Counters
 * 2. Arrow
 * 3. Backgrounds
 * 4. Bootstrap Layout
 * 5. Button
 * 6. Button Bordered
 * 7. Button Solid
 * 8. Change Text Hover
 * 9. <PERSON> Button
 * 10. Comments
 * 11. Counter
 * 12. Cursor
 * 13. Curtain
 * 14. Figure Feature
 * 15. Figure Icon
 * 16. Figure Image
 * 17. Figure Info
 * 18. Figure Logo
 * 19. Figure Member
 * 20. Figure Project
 * 21. Figure Post
 * 22. Figure Testimonial
 * 23. Filter
 * 24. Footer
 * 25. Footer Logo
 * 26. Footer Themes
 * 27. Form
 * 28. Figure Service
 * 29. Form Contacts
 * 30. Gallery
 * 31. Gmap
 * 32. Grid
 * 33. Grid Fluid
 * 34. Header
 * 35. Header Logo
 * 36. Header Sticky
 * 37. Header Themes
 * 38. Input Float
 * 39. Input Search
 * 40. Hover Zoom
 * 41. Lazy
 * 42. Logo
 * 43. List Projects
 * 44. Menu
 * 45. Menu Overlay
 * 46. Modal
 * 47. Overlay
 * 48. Page Indicator
 * 49. Pagination
 * 50. Arts Parallax
 * 51. Parallax
 * 52. Post
 * 53. Post Meta
 * 54. Preloader
 * 55. Pswp
 * 56. Scroll Down
 * 57. Scroll
 * 58. Section
 * 59. Section Height
 * 60. Section Offset
 * 61. Section About
 * 62. Section Blog
 * 63. Section CTA
 * 64. Section Content
 * 65. Section Demo
 * 66. Section Grid
 * 67. Section Image
 * 68. Section List Themes
 * 69. Section Nav Projects
 * 70. Section Nav Projects Themes
 * 71. Section Masthead
 * 72. Section Scroll
 * 73. Section Services
 * 74. Section Video
 * 75. Select
 * 76. Sidebar
 * 77. Slider
 * 78. Slider Categories
 * 79. Slider Counter
 * 80. Slider Dots
 * 81. Slider Themes
 * 82. Slider Images
 * 83. Slider Halfscreen Projects
 * 84. Slider Fullscreen Projects
 * 85. Slider Testimonials
 * 86. Social
 * 87. Slider Services
 * 88. Spinner
 * 89. Svg-rectangle
 * 90. Tags
 * 91. Themes
 * 92. Transition Curtain
 * 93. Image Alignment
 * 94. Typography
 * 95. Fluid Margins
 * 96. Fluid Margins Negative
 * 97. Fluid Paddings
 * 98. Margins
 * 99. Offsets
 * 100. Paddings
 * 101. Utilities
 * 102. Widget
 * 103. Widget Archive
 * 104. Widget Calendar
 * 105. Widget Categories
 * 106. Widget Menu Inline
 * 107. Widget Nav Menu
 * 108. Widget Polylang
 * 109. Widget RSS
 * 110. Widget Recent Comments
 * 111. Widget Recent Entries
 * 112. Widget Social
 * 113. Widget WPML
 * 114. Widget CTA
 * 115. Widget Text
 * 116. Widget Logo
 *
 * ==========================================================================
 * ==========================================================================
 * ==========================================================================
 */

:root {
  /* Fonts */
  --font-primary: "Raleway", sans-serif;
  --font-secondary: "Cinzel", serif;
  /* Colors */
  --color-dark-1: #111111;
  --color-dark-2: #262626;
  --color-dark-3: #333333;
  --color-dark-4: #555555;
  --color-light-1: #eeece6;
  --color-light-2: #f2f1ed;
  --color-light-3: #f7f6f3;
  --color-light-4: #f1e9db;
  --color-gray-1: #888888;
  --color-gray-2: #cccccc;
  /* XL heading */
  --xl-max-font-size: 188;
  --xl-min-font-size: 54;
  --xl-line-height: 1.1;
  --xl-color-light: #eeece6;
  --xl-color-dark: #333333;
  /* h1 heading */
  --h1-max-font-size: 104;
  --h1-min-font-size: 35;
  --h1-line-height: 1.13;
  --h1-color-light: #eeece6;
  --h1-color-dark: #333333;
  /* h2 heading */
  --h2-max-font-size: 65;
  --h2-min-font-size: 31;
  --h2-line-height: 1.31;
  --h2-color-light: #eeece6;
  --h2-color-dark: #333333;
  /* h3 heading */
  --h3-max-font-size: 42;
  --h3-min-font-size: 24;
  --h3-line-height: 1.29;
  --h3-color-light: #eeece6;
  --h3-color-dark: #333333;
  /* h4 heading */
  --h4-max-font-size: 26;
  --h4-min-font-size: 22;
  --h4-line-height: 1.62;
  --h4-color-light: #ffffff;
  --h4-color-dark: #262626;
  /* h5 heading */
  --h5-max-font-size: 18;
  --h5-min-font-size: 18;
  --h5-line-height: 1.6;
  --h5-color-light: #ffffff;
  --h5-color-dark: #262626;
  /* h6 heading */
  --h6-max-font-size: 14;
  --h6-min-font-size: 14;
  --h6-line-height: 1.6;
  --h6-color-light: #ffffff;
  --h6-color-dark: #262626;
  /* Blockquote */
  --blockquote-max-font-size: 24;
  --blockquote-min-font-size: 16;
  --blockquote-line-height: 1.6;
  --blockquote-color-light: #ffffff;
  --blockquote-color-dark: #262626;
  /* Paragraph */
  --paragraph-max-font-size: 18;
  --paragraph-min-font-size: 16;
  --paragraph-line-height: 1.8;
  --paragraph-color-light: #cccccc;
  --paragraph-color-dark: #262626;
  /* Dropcap */
  --dropcap-max-font-size: 110;
  --dropcap-min-font-size: 60;
  --dropcap-line-height: 0.7;
  --dropcap-color-light: #ffffff;
  --dropcap-color-dark: #111111;
  /* Fluid paddings & margins (min values) */
  --distance-min-xsmall: 30;
  --distance-min-small: 30;
  --distance-min-medium: 50;
  --distance-min-large: 100;
  --distance-min-xlarge: 160;
  /* Fluid paddings & margins (max values) */
  --distance-max-xsmall: 50;
  --distance-max-small: 120;
  --distance-max-normal: 240;
  --distance-max-large: 360;
  --distance-max-xlarge: 400;
  /* Container & page gutters */
  --gutter-horizontal: 120px;
  --gutter-vertical: 80px;
  /* preloader circle */
  --preloader-circle-max-size: 960;
  --preloader-circle-min-size: 280;
}
@media screen and (max-width: 1400px) {
  :root {
    --gutter-horizontal: 80px;
    --gutter-vertical: 60px;
  }
}
@media screen and (max-width: 1280px) {
  :root {
    --gutter-horizontal: 60px;
    --gutter-vertical: 40px;
  }
}
@media screen and (max-width: 991px) {
  :root {
    --gutter-horizontal: 20px;
    --gutter-vertical: 20px;
  }
}

html {
  font-size: 18px;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -webkit-text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-kerning: none;
}

body {
  position: relative;
  width: 100%;
  min-width: 320px;
  font-family: var(--font-primary);
  font-size: 100%;
  line-height: 2;
  color: var(--paragraph-color-dark);
  word-wrap: break-word;
}

.body_lock-scroll {
  overflow: hidden;
  position: fixed;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

img, iframe {
  max-width: 100%;
  height: auto;
}

a {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  text-decoration: none;
  color: var(--color-gray-1);
}
a:hover {
  color: var(--color-dark-2);
  text-decoration: none;
}

.page-wrapper__content {
  position: relative;
  overflow: hidden;
  z-index: 50;
}

.swiper-slide:not(.swiper-slide-active) .pointer-events-auto, .swiper-slide:not(.swiper-slide-active) .pointer-events-none .pointer-events-auto {
  pointer-events: none !important;
}
.swiper-slide:not(.swiper-slide-active) .pointer-events-auto *, .swiper-slide:not(.swiper-slide-active) .pointer-events-none .pointer-events-auto * {
  pointer-events: none !important;
}

/*!========================================================================
 * 1. Aside Counters
 * ======================================================================!*/
.aside-counters_2 .aside-counters__wrapper-item:after, .aside-counters_3 .aside-counters__wrapper-item:after, .aside-counters_4 .aside-counters__wrapper-item:after {
  content: "";
  display: block;
  position: absolute;
  top: 10px;
  bottom: 10px;
  right: 0;
  width: 1px;
  background-color: rgba(128, 128, 128, 0.3);
}

.aside-counters_2 .aside-counters__wrapper-item:nth-child(2):after {
  display: none;
}
.aside-counters_2 .aside-counters__wrapper-item:last-child:after {
  display: none;
}

.aside-counters_3 .aside-counters__wrapper-item:nth-child(3):after {
  display: none;
}
.aside-counters_3 .aside-counters__wrapper-item:last-child:after {
  display: none;
}

.aside-counters_4 .aside-counters__wrapper-item:nth-child(4):after {
  display: none;
}
.aside-counters_4 .aside-counters__wrapper-item:last-child:after {
  display: none;
}

@media only screen and (max-width: 991px) {
  .aside-counters__wrapper-item:after {
    top: 0;
    bottom: 0;
  }
  .aside-counters_3 .aside-counters__wrapper-item:nth-child(2):after {
    display: none;
  }
  .aside-counters_4 .aside-counters__wrapper-item:nth-child(2):after {
    display: none;
  }
}
/*!========================================================================
 * 2. Arrow
 * ======================================================================!*/
.arrow {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  cursor: pointer;
  outline: none;
}
.arrow .svg-circle {
  width: 60px;
  height: 60px;
}
.arrow .circle {
  stroke: rgba(128, 128, 128, 0.5);
  stroke-width: 1px;
}
.arrow:hover .arrow__pointer {
  background-color: var(--color-dark-2);
}
.arrow:hover .circle {
  stroke: var(--color-dark-2);
}
.arrow:hover .arrow__triangle {
  border-color: transparent transparent transparent var(--color-dark-2);
}

.arrow_mini .svg-circle {
  width: 40px;
  height: 40px;
}
.arrow_mini .arrow__pointer {
  width: 50% !important;
  right: 22px;
}
.arrow_mini .arrow__triangle {
  border-width: 4px 0 4px 6px;
}
.arrow_mini .circle {
  stroke-width: 2px;
}

.js-arrow:hover .arrow__pointer {
  transform: scaleX(0.3) translateX(24px);
  width: 100% !important;
}
.js-arrow:hover .arrow__triangle {
  transform: translate(calc(-50% + 6px), -50%);
}

.arrow-left {
  transform: rotate(180deg);
}

.arrow__pointer {
  display: block;
  position: absolute;
  width: 100%;
  height: 2px;
  right: 30px;
  top: 0;
  bottom: 0;
  margin: auto;
  background-color: rgba(128, 128, 128, 0.5);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform-origin: right center;
}

.arrow__triangle {
  display: inline-block;
  border-style: solid;
  border-width: 6px 0 6px 8px;
  border-color: transparent transparent transparent rgba(128, 128, 128, 0.5);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.arrow-up {
  transform: rotate(-90deg);
}
.arrow-up .arrow__pointer {
  width: 80%;
}

.arrow-down {
  transform: rotate(90deg);
}
.arrow-down .arrow__pointer {
  width: 80%;
}

@media screen and (max-width: 991px) {
  .arrow {
    width: 36px;
    height: 36px;
  }
  .arrow .svg-circle {
    width: 36px;
    height: 36px;
  }
  .arrow .arrow__pointer {
    width: 50% !important;
    right: 20px;
  }
  .arrow .arrow__triangle {
    top: 50%;
    border-width: 4px 0 4px 6px;
  }
  .arrow .circle {
    stroke-width: 2px;
  }
}
/*!========================================================================
 * 3. Backgrounds
 * ======================================================================!*/
.bg-dark-1 {
  background-color: var(--color-dark-1) !important;
}

.bg-dark-2 {
  background-color: var(--color-dark-2) !important;
}

.bg-dark-3 {
  background-color: var(--color-dark-3) !important;
}

.bg-dark-4 {
  background-color: var(--color-dark-4) !important;
}

.bg-light-1 {
  background-color: var(--color-light-1) !important;
}

.bg-light-2 {
  background-color: var(--color-light-2) !important;
}

.bg-light-3 {
  background-color: var(--color-light-3) !important;
}

.bg-light-4 {
  background-color: var(--color-light-4) !important;
}

.bg-white {
  background-color: #fff !important;
}

/*!========================================================================
 * 4. Bootstrap Layout
 * ======================================================================!*/
.container-fluid {
  padding-left: var(--gutter-horizontal);
  padding-right: var(--gutter-horizontal);
}

.container-fluid_paddings {
  padding-top: var(--gutter-horizontal);
  padding-bottom: var(--gutter-horizontal);
}

@media screen and (max-width: 991px) {
  .container_p-md-0 {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
  }
  .container_px-md-0 {
    padding-left: 0;
    padding-right: 0;
  }
  .container_px-md-0 > .row {
    margin-left: 0;
    margin-right: 0;
  }
  .container_px-md-0 [class*=col-] {
    padding-left: 0;
    padding-right: 0;
  }
  .container_py-md-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
}
@media screen and (max-width: 767px) {
  .container_p-sm-0 {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
  }
  .container_px-sm-0 {
    padding-left: 0;
    padding-right: 0;
  }
  .container_px-sm-0 > .row {
    margin-left: 0;
    margin-right: 0;
  }
  .container_px-sm-0 [class*=col-] {
    padding-left: 0;
    padding-right: 0;
  }
  .container_py-sm-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
}
@media screen and (max-width: 576px) {
  .container_p-xs-0 {
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    padding-left: 0;
  }
  .container_px-xs-0 {
    padding-left: 0;
    padding-right: 0;
  }
  .container_px-xs-0 > .row {
    margin-left: 0;
    margin-right: 0;
  }
  .container_px-xs-0 [class*=col-] {
    padding-left: 0;
    padding-right: 0;
  }
  .container_py-xs-0 {
    padding-top: 0;
    padding-bottom: 0;
  }
}
/*!========================================================================
 * 5. Button
 * ======================================================================!*/
.button {
  position: relative;
  display: inline-block;
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  font-weight: bold;
  text-align: center;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  cursor: pointer;
  outline: none;
  box-shadow: none;
  border-width: 2px;
  border-style: solid;
  padding: 21px 48px;
  border-radius: 64px;
}
@media screen and (min-width: 320px) {
  .button {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .button {
    font-size: calc(13 * 1px);
  }
}
.button:focus {
  outline: none;
}

.button[data-hover]:before {
  content: attr(data-hover);
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(100%);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  opacity: 0;
  visibility: hidden;
}
.button[data-hover]:hover .button__label-hover {
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
}
.button[data-hover]:hover:before {
  transform: translateY(-50%);
  opacity: 1;
  visibility: visible;
}

.button__label-hover {
  display: block;
  transform: translateY(0%);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  opacity: 1;
  visibility: visible;
}

.button_icon {
  display: inline-flex;
  padding: 0;
  border: none;
}

.button__label {
  display: inline-block;
  padding: 21px 48px;
}

.button__icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.button__icon i {
  font-size: 24px;
}

.button_fullwidth {
  width: 100%;
}

@media screen and (max-width: 1680px) {
  .button {
    padding: 14px 32px;
  }
  .button__label {
    padding: 18px 32px;
  }
}
@media screen and (max-width: 991px) {
  .button {
    padding: 12px 28px;
  }
  .button__label {
    padding: 12px 28px;
  }
  .button__icon {
    width: 43px;
  }
  .button__icon i {
    font-size: 20px;
  }
  .button_icon {
    padding: 0;
  }
}
/*!========================================================================
 * 6. Button Bordered
 * ======================================================================!*/
.button_bordered.button_white {
  border-color: #fff;
  color: #fff;
}
.button_bordered.button_black {
  border-color: var(--color-dark-1);
  color: var(--color-dark-1);
}

/*!========================================================================
 * 7. Button Solid
 * ======================================================================!*/
.button_solid.button_black {
  border-color: var(--color-dark-1);
  background-color: var(--color-dark-1);
  color: #fff;
}

/*!========================================================================
 * 8. Change Text Hover
 * ======================================================================!*/
.change-text-hover {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  line-height: 1;
  padding-left: 5px;
}
.change-text-hover.text-right .change-text-hover__hover {
  left: auto;
  right: 0;
}
@media screen and (min-width: 990px) {
  .change-text-hover.text-lg-right .change-text-hover__hover {
    left: auto;
    right: 0;
  }
}

.change-text-hover_line-visible .change-text-hover__line {
  transform: scaleX(1) !important;
  transform-origin: "left center" !important;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.change-text-hover_line-visible .change-text-hover__normal {
  transform: translateX(100%);
}

.change-text-hover__hover {
  display: inline-flex;
  align-items: center;
  position: absolute;
  top: 0;
}

.change-text-hover__line {
  display: inline-block;
  width: 60px;
  height: 1px;
  background-color: var(--color-gray-1);
  margin-right: 1em;
  transform: scaleX(0);
}

/*!========================================================================
 * 9. Circle Button
 * ======================================================================!*/
.js-circle-button[data-arts-os-animation] {
  visibility: visible !important;
}

.circle-button {
  position: relative;
  display: inline-flex;
  vertical-align: bottom;
  align-items: center;
  justify-content: center;
  width: calc(1 * (100 * 1px));
  height: calc(1 * (100 * 1px));
  transition: none;
}
@media screen and (min-width: 320px) {
  .circle-button {
    width: calc(1 * (100 * 1px + (160 - 100) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button {
    width: calc(1 * (160 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .circle-button {
    height: calc(1 * (100 * 1px + (160 - 100) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button {
    height: calc(1 * (160 * 1px));
  }
}

.circle-button_link {
  width: calc(1 * (80 * 1px));
  height: calc(1 * (80 * 1px));
}
@media screen and (min-width: 320px) {
  .circle-button_link {
    width: calc(1 * (80 * 1px + (100 - 80) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button_link {
    width: calc(1 * (100 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .circle-button_link {
    height: calc(1 * (80 * 1px + (100 - 80) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button_link {
    height: calc(1 * (100 * 1px));
  }
}
.circle-button_link .circle-button__outer {
  z-index: 50;
}
.circle-button_link .circle-button__label {
  color: #fff;
}
.circle-button_link .circle-button__icon {
  color: --var(--color-gray-1);
  line-height: 0;
  z-index: 50;
}
.circle-button_link .circle-button__inner .svg-circle {
  border: none;
  background-color: var(--color-dark-1);
  width: calc(1 * (90 * 1px));
  height: calc(1 * (90 * 1px));
}
@media screen and (min-width: 320px) {
  .circle-button_link .circle-button__inner .svg-circle {
    width: calc(1 * (90 * 1px + (120 - 90) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button_link .circle-button__inner .svg-circle {
    width: calc(1 * (120 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .circle-button_link .circle-button__inner .svg-circle {
    height: calc(1 * (90 * 1px + (120 - 90) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button_link .circle-button__inner .svg-circle {
    height: calc(1 * (120 * 1px));
  }
}
.circle-button_link .circle-button__circle:hover .svg-circle {
  transform: scale(1);
}

.circle-button__outer {
  width: 100%;
  height: 100%;
}

.circle-button__wrapper-label {
  width: 100%;
  height: 100%;
}

.circle-button__inner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.circle-button__inner .svg-circle {
  width: calc(1 * (60 * 1px));
  height: calc(1 * (60 * 1px));
  border: 1px solid rgba(104, 104, 104, 0.5);
  border-radius: 100%;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
@media screen and (min-width: 320px) {
  .circle-button__inner .svg-circle {
    width: calc(1 * (60 * 1px + (100 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button__inner .svg-circle {
    width: calc(1 * (100 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .circle-button__inner .svg-circle {
    height: calc(1 * (60 * 1px + (100 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .circle-button__inner .svg-circle {
    height: calc(1 * (100 * 1px));
  }
}
.circle-button__inner .circle {
  stroke-width: 4px;
}

.circle-button__icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  pointer-events: none;
}
.circle-button__icon svg {
  height: 35px;
}

.circle-button__circle {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.circle-button__circle:hover .svg-circle {
  transform: scale(0.85);
  border-color: rgb(104, 104, 104);
}
.circle-button__circle:hover ~ .circle-button__icon .svg-mouse__wheel {
  transform: translateY(4px);
}

.circle-button__icon .svg-mouse__wheel {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.no-touchevents .circle-button__icon-mouse {
  display: block !important;
}
.no-touchevents .circle-button__icon-touch {
  display: none !important;
}

.touchevents .circle-button__icon-mouse {
  display: none !important;
}
.touchevents .circle-button__icon-touch {
  display: block !important;
}

@media screen and (max-width: 991px) {
  .circle-button__label {
    font-size: 10px !important;
    letter-spacing: 0.3px !important;
  }
  .circle-button__icon svg {
    height: 27px;
  }
}
/*!========================================================================
 * 10. Comments
 * ======================================================================!*/
.comments-title, .comment-reply-title {
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 1em;
}

.comment-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  padding-left: 0 !important;
}
.comment-list > li {
  padding-top: 1em;
  padding-bottom: 1em;
}
.comment-list > li ol.children {
  list-style-type: none;
  padding: 0;
  margin: 0;
  padding-left: 4%;
}
.comment-list > li ol.children li {
  padding-top: 1em;
  padding-bottom: 1em;
}
.comment-list > li ol.children li:last-child {
  padding-bottom: 0;
}
.comment-list > li:not(:last-child) {
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}
.comment-list > ol {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.comment-author {
  max-width: 100px;
  margin-right: 2em;
  flex: 0 0 auto;
}
.comment-author .avatar {
  position: relative;
  width: 100%;
  max-width: 100px;
  max-height: 100px;
  border-radius: 100%;
  display: block;
}

.comment-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5em;
}

.comment-metadata {
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  margin-left: 10px;
}
@media screen and (min-width: 320px) {
  .comment-metadata {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .comment-metadata {
    font-size: calc(13 * 1px);
  }
}
.comment-metadata a {
  border-bottom: none !important;
}

.comment-body {
  display: flex;
}
.comment-body .fn {
  font-family: var(--font-secondary);
  font-size: 20px;
  font-weight: bold;
  line-height: 1.7;
}

.comment-content {
  width: 100%;
}

.reply {
  line-height: 1;
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.comment-reply-link {
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  border-bottom: none !important;
  margin-right: 24px;
}
@media screen and (min-width: 320px) {
  .comment-reply-link {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .comment-reply-link {
    font-size: calc(13 * 1px);
  }
}
.comment-reply-link:before {
  content: "\f112";
  font-family: "FontAwesome";
  text-transform: none;
  margin-right: 8px;
}

.comment-respond {
  margin-top: 30px;
}

.comment-edit-link {
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  color: var(--color-accent-primary);
  border-bottom: none !important;
}
@media screen and (min-width: 320px) {
  .comment-edit-link {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .comment-edit-link {
    font-size: calc(13 * 1px);
  }
}
.comment-edit-link:before {
  content: "\f044";
  font-family: "FontAwesome";
  text-transform: none;
  margin-right: 6px;
}

@media only screen and (max-width: 991px) {
  .comment-author {
    margin-right: 1em;
  }
  .comment-author .avatar {
    max-width: 50px;
    max-height: 50px;
  }
  .comment-meta {
    flex-wrap: wrap;
  }
  .comment-metadata {
    margin-top: 5px;
    margin-left: 0;
  }
}
/*!========================================================================
 * 11. Counter
 * ======================================================================!*/
.counter__number {
  font-family: var(--font-secondary);
  font-size: calc(43 * 1px);
  font-weight: 200;
  line-height: 1;
}
@media screen and (min-width: 320px) {
  .counter__number {
    font-size: calc(43 * 1px + (104 - 43) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .counter__number {
    font-size: calc(104 * 1px);
  }
}

.counter__label {
  margin-top: 5px;
  color: var(--color-gray-1);
  line-height: 1.5;
  font-size: calc(14 * 1px);
}
@media screen and (min-width: 320px) {
  .counter__label {
    font-size: calc(14 * 1px + (18 - 14) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .counter__label {
    font-size: calc(18 * 1px);
  }
}

/*!========================================================================
 * 12. Cursor
 * ======================================================================!*/
.cursor {
  position: fixed;
  transform: translate(-50%, -50%);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
  z-index: 10000;
  display: none;
  color: var(--color-gray-1);
}

.cursor__follower {
  position: relative;
  width: 50px;
  height: 50px;
  border-radius: 100%;
}
.cursor__follower svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.cursor__follower svg #inner, .cursor__follower svg #outer {
  fill: none;
  stroke-linecap: butt;
}
.cursor__follower svg #inner {
  stroke-width: 1px;
  stroke: var(--color-gray-1);
  opacity: 0.7;
}
.cursor__follower svg #outer {
  stroke-width: 2px;
  stroke: #c5c6c9;
}

.cursor-progress {
  cursor: progress !important;
}
.cursor-progress * {
  cursor: progress !important;
}

.cursor-none {
  cursor: none !important;
}
.cursor-none * {
  cursor: none !important;
}

.cursor__wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.cursor__arrow {
  position: absolute;
  margin: auto;
  font-size: 24px !important;
  width: 24px;
  height: 24px;
  opacity: 0;
  visibility: hidden;
}

.cursor__arrow_left {
  top: 0;
  bottom: 0;
  left: 0;
}

.cursor__arrow_right {
  top: 0;
  bottom: 0;
  right: 0;
}

.cursor__arrow_up {
  top: 0;
  left: 0;
  right: 0;
}

.cursor__arrow_down {
  bottom: 0;
  left: 0;
  right: 0;
}

.cursor__label {
  display: block;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 10px;
  margin: auto;
  font-size: 11px;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: 0.7px;
  text-transform: uppercase;
  opacity: 0;
  visibility: hidden;
  white-space: nowrap;
}

.cursor__icon {
  display: block;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  margin: auto;
  font-size: 28px !important;
  width: 28px;
  height: 28px;
}

/*!========================================================================
 * 13. Curtain
 * ======================================================================!*/
.curtain__wrapper-svg {
  width: 100%;
  height: 100%;
}

.curtain__rect {
  flex: 1 0 80%;
  background: #000000;
}

.curtain {
  width: 100%;
  height: 100%;
}

.curtain-svg {
  width: 100%;
  height: 100%;
}

.curtain-svg__curve {
  visibility: hidden;
}

.curtain-svg_bottom {
  display: none !important;
  transform: rotate(180deg) !important;
}

/*!========================================================================
 * 14. Figure Feature
 * ======================================================================!*/
.figure-feature {
  position: relative;
  text-align: center;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: calc(1 * (40 * 1px));
  padding-bottom: calc(1 * (40 * 1px));
  width: 100%;
  outline: 1px solid rgba(128, 128, 128, 0.3);
  outline-offset: -20px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
@media screen and (min-width: 320px) {
  .figure-feature {
    padding-top: calc(1 * (40 * 1px + (100 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-feature {
    padding-top: calc(1 * (100 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .figure-feature {
    padding-bottom: calc(1 * (40 * 1px + (100 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-feature {
    padding-bottom: calc(1 * (100 * 1px));
  }
}

.figure-feature__icon {
  height: calc(1 * (60 * 1px));
}
@media screen and (min-width: 320px) {
  .figure-feature__icon {
    height: calc(1 * (60 * 1px + (80 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-feature__icon {
    height: calc(1 * (80 * 1px));
  }
}
.figure-feature__icon img {
  width: auto;
  height: 100%;
}

/*!========================================================================
 * 15. Figure Icon
 * ======================================================================!*/
.figure-icon__wrapper-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: calc(1 * (100 * 1px));
  height: calc(1 * (100 * 1px));
  border-radius: 50%;
  border: 1px solid rgba(128, 128, 128, 0.3);
  transition: border-color 0.3s ease;
}
@media screen and (min-width: 320px) {
  .figure-icon__wrapper-icon {
    width: calc(1 * (100 * 1px + (160 - 100) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-icon__wrapper-icon {
    width: calc(1 * (160 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .figure-icon__wrapper-icon {
    height: calc(1 * (100 * 1px + (160 - 100) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-icon__wrapper-icon {
    height: calc(1 * (160 * 1px));
  }
}
.figure-icon__wrapper-icon:hover {
  border-color: var(--color-dark-1);
}

.figure-icon__icon {
  font-size: calc(30 * 1px);
}
@media screen and (min-width: 320px) {
  .figure-icon__icon {
    font-size: calc(30 * 1px + (40 - 30) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .figure-icon__icon {
    font-size: calc(40 * 1px);
  }
}

/*!========================================================================
 * 16. Figure Image
 * ======================================================================!*/
.figure-image {
  display: block;
  margin-bottom: 0;
}

.figure-image__link {
  display: block;
}

.figure-image__wrapper-img {
  position: relative;
  overflow: hidden;
}

.figure-image__wrapper-img-zoom {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform: scale(1.25);
  transform-origin: center center;
}

.figure-image__caption {
  display: block !important;
}

.figure-image__wrapper-caption {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-delay: 0s;
}

/*!========================================================================
 * 17. Figure Info
 * ======================================================================!*/
.figure-info__option {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  color: var(--color-gray-1);
}

.figure-info__value {
  margin-top: 0;
  margin-bottom: 0;
}

/*!========================================================================
 * 18. Figure Logo
 * ======================================================================!*/
.figure-logo {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 340px;
  padding: 30px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.figure-logo img {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.figure-logo:hover .figure-logo__description {
  transform: scaleY(1);
}
.figure-logo:hover .figure-logo__description p {
  transform: translateY(0px);
  opacity: 1;
  visibility: visible;
  transition-delay: 0.15s;
}
.figure-logo:hover .figure-logo__description .figure-logo__line {
  transition-delay: 0.2s;
  transform: scaleX(1);
}
.figure-logo:hover img {
  transform: translateY(-15px);
}

.figure-logo__description {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-dark-3);
  color: #fff;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform: scaleY(0);
  transform-origin: bottom center;
}
.figure-logo__description p {
  color: #fff;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.figure-logo__line {
  display: inline-block;
  width: 30px;
  height: 1px;
  background-color: #fff;
  transform: scaleX(0);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

@media screen and (max-width: 1280px) {
  .figure-logo {
    width: 270px;
    height: 270px;
    max-width: 100%;
  }
}
@media screen and (max-width: 991px) {
  .figure-logo {
    width: 100%;
  }
}
/*!========================================================================
 * 19. Figure Member
 * ======================================================================!*/
.figure-member_has-social:hover .figure-member__avatar img {
  transform: scale(1.1);
}
.figure-member_has-social:hover .figure-member__headline {
  width: 80px;
}
.figure-member_has-social:hover .figure-member__position {
  transition-delay: 50ms;
  transform: translateY(-30px);
  opacity: 0;
  visibility: hidden;
}
.figure-member_has-social:hover .figure-member__footer .figure-member__name {
  transform: translateY(-20px);
  opacity: 0;
  visibility: hidden;
  transition-delay: 0ms;
}
.figure-member_has-social:hover .figure-member__social li a {
  transform: translateY(0px);
  opacity: 1;
  visibility: visible;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(1) {
  transition-delay: 80ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(2) {
  transition-delay: 110ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(3) {
  transition-delay: 140ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(4) {
  transition-delay: 170ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(5) {
  transition-delay: 200ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(6) {
  transition-delay: 230ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(7) {
  transition-delay: 260ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(8) {
  transition-delay: 290ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(9) {
  transition-delay: 320ms;
}
.figure-member_has-social:hover .figure-member__social li a:nth-child(10) {
  transition-delay: 350ms;
}

.figure-member__avatar {
  overflow: hidden;
}
.figure-member__avatar img {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.figure-member__footer {
  position: relative;
}

.figure-member__name {
  margin-top: 0;
  margin-bottom: 0;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-delay: 150ms;
}

.figure-member__position {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-delay: 150ms;
}

.figure-member__headline {
  display: inline-block;
  width: 60px;
  height: 1px;
  vertical-align: middle;
  background-color: var(--color-gray-1);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.figure-member__social {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}
.figure-member__social li a {
  transform: translateY(30px);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  opacity: 0;
  visibility: hidden;
}
.figure-member__social li a:last-child {
  margin-right: 0;
}

/*!========================================================================
 * 20. Figure Project
 * ======================================================================!*/
.figure-project {
  display: block;
}

.figure-project:not(.figure-project_no-hover):hover:not(:focus) .figure-project__content {
  transform: translateY(-15px);
}

.figure-project__link {
  position: relative;
  display: block;
  z-index: 50;
}

.figure-project__heading {
  position: relative;
  margin-top: 0;
  margin-bottom: 0;
  color: var(--color-dark-1);
  z-index: 50;
}

.figure-project__category {
  line-height: 1;
}

.figure-project__content {
  transition: transform 0.3s ease;
}
.figure-project__content.text-right {
  padding-right: calc(var(--gutter-horizontal) - 20px);
}
.figure-project__content.text-left {
  padding-left: calc(var(--gutter-horizontal) - 20px);
}

.figure-project__letter {
  display: inline-block;
  line-height: 1;
  font-size: calc(67 * 1px);
  color: var(--color-dark-1);
  opacity: 0.05;
  font-family: var(--font-secondary);
}
@media screen and (min-width: 320px) {
  .figure-project__letter {
    font-size: calc(67 * 1px + (400 - 67) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .figure-project__letter {
    font-size: calc(400 * 1px);
  }
}

.figure-project__wrapper-img {
  position: relative;
}

.figure-project__wrapper-letter {
  display: inline-block;
  position: absolute;
  bottom: 0;
  left: 0;
  transform: translate(-50%, 50%);
  z-index: 0;
}

.figure-project__content_absolute {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #fff;
  z-index: 50;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform: none !important;
}

.figure-project__overlay-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.figure-project__category_absolute {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 80px;
  margin-top: 0;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.figure-project__wrapper-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 50;
  opacity: 0;
  visibility: hidden;
  transition: all 0.6s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-delay: 0s;
}

.figure-project_hover-inner .figure-project__overlay {
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transition: all 0.6s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.figure-project_hover-inner:hover .figure-project__overlay {
  opacity: 1;
  visibility: visible;
}
.figure-project_hover-inner:hover .figure-project__wrapper-content {
  opacity: 1;
  visibility: visible;
}

@media screen and (max-width: 991px) {
  .figure-project__content_absolute {
    padding: 40px;
  }
  .figure-project__category_absolute {
    padding: 40px;
  }
}
@media screen and (max-width: 576px) {
  .figure-project__content_absolute {
    padding: 30px;
  }
  .figure-project__category_absolute {
    padding: 30px;
  }
}
/*!========================================================================
 * 21. Figure Post
 * ======================================================================!*/
.figure-post__categories {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.figure-post__categories:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: calc(1 * (40 * 1px));
  height: 1px;
  margin-right: 1em;
  background-color: var(--color-gray-1);
}
@media screen and (min-width: 320px) {
  .figure-post__categories:before {
    width: calc(1 * (40 * 1px + (60 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__categories:before {
    width: calc(1 * (60 * 1px));
  }
}
.figure-post__categories li {
  display: inline-block;
}

.figure-post__media > a {
  display: block;
  position: relative;
  overflow: hidden;
}
.figure-post__media > a img {
  transition: transform 0.3s ease;
  transform-origin: center center;
  will-change: transform;
}
.figure-post__media > a:hover img {
  transform: scale(1.1);
}

.figure-post__date {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  will-change: transform;
  position: absolute;
  top: 0;
  left: 0;
  width: calc(1 * (70 * 1px));
  height: calc(1 * (70 * 1px));
  background-color: #fff;
  z-index: 50;
  text-align: center;
}
@media screen and (min-width: 320px) {
  .figure-post__date {
    width: calc(1 * (70 * 1px + (100 - 70) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__date {
    width: calc(1 * (100 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .figure-post__date {
    height: calc(1 * (70 * 1px + (100 - 70) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__date {
    height: calc(1 * (100 * 1px));
  }
}

.figure-post__date_centered {
  right: 0;
  margin: 0 auto;
}

.figure-post__date-day {
  display: block;
  font-weight: normal;
  line-height: 1;
}

.figure-post__date-month {
  display: block;
  line-height: 1;
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
}
@media screen and (min-width: 320px) {
  .figure-post__date-month {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__date-month {
    font-size: calc(13 * 1px);
  }
}

.figure-post__date_small {
  width: calc(1 * (60 * 1px));
  height: calc(1 * (60 * 1px));
}
@media screen and (min-width: 320px) {
  .figure-post__date_small {
    width: calc(1 * (60 * 1px + (80 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__date_small {
    width: calc(1 * (80 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .figure-post__date_small {
    height: calc(1 * (60 * 1px + (80 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-post__date_small {
    height: calc(1 * (80 * 1px));
  }
}
.figure-post__date_small .figure-post__date-day {
  font-weight: normal;
}

.figure-post__content > *:first-child {
  margin-top: 0;
}
.figure-post__content > *:last-child {
  margin-bottom: 0;
}

/*!========================================================================
 * 22. Figure Testimonial
 * ======================================================================!*/
.figure-testimonial__avatar {
  width: calc(1 * (120 * 1px));
  height: calc(1 * (120 * 1px));
  border-radius: 100%;
}
@media screen and (min-width: 320px) {
  .figure-testimonial__avatar {
    width: calc(1 * (120 * 1px + (400 - 120) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-testimonial__avatar {
    width: calc(1 * (400 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .figure-testimonial__avatar {
    height: calc(1 * (120 * 1px + (400 - 120) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .figure-testimonial__avatar {
    height: calc(1 * (400 * 1px));
  }
}

.figure-testimonial__text {
  margin-top: 0;
  margin-bottom: 0;
}

.figure-testimonial__sign {
  width: 2.5em;
  height: 2.5em;
  margin-bottom: 1em;
}

.figure-testimonial__author {
  margin-top: 2em;
}

@media screen and (max-width: 991px) {
  .figure-testimonial {
    text-align: center;
  }
  .figure-testimonial__avatar {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 1em;
  }
  .figure-testimonial__sign {
    display: none;
  }
}
/*!========================================================================
 * 23. Filter
 * ======================================================================!*/
.filter {
  position: relative;
}

.filter__inner {
  position: relative;
}

.filter__item {
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  padding-top: 1em;
  padding-bottom: 1em;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  color: var(--color-gray-1);
}
@media screen and (min-width: 320px) {
  .filter__item {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .filter__item {
    font-size: calc(13 * 1px);
  }
}
.filter__item:not(.filter__item_active):hover {
  color: var(--color-dark-1);
}

.filter__item_active {
  color: var(--color-dark-1);
}

.filter__underline {
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--color-dark-1);
  width: 0;
  height: 1px;
}

@media screen and (max-width: 1199px) {
  .filter__underline {
    display: none;
  }
}
@media screen and (max-width: 991px) {
  .filter .filter__item:first-child {
    padding-top: 0;
  }
  .filter .filter__item:last-child {
    padding-bottom: 0;
  }
}
/*!========================================================================
 * 24. Footer
 * ======================================================================!*/
.footer__area-border-top {
  border-top: 1px solid rgba(128, 128, 128, 0.3);
}

.footer__area .widgettitle {
  margin-top: 0;
  margin-bottom: 0.5em;
}
.footer__area .widgettitle:after {
  display: none;
}

@media screen and (max-width: 991px) {
  .text-center .logo__wrapper-img {
    margin-left: auto;
    margin-right: auto;
  }
}
/*!========================================================================
 * 25. Footer Logo
 * ======================================================================!*/
[data-arts-footer-logo=primary] .logo__img-primary {
  opacity: 1;
  visibility: visible;
}
[data-arts-footer-logo=primary] .logo__img-secondary {
  opacity: 0;
  visibility: hidden;
}

[data-arts-footer-logo=secondary] .logo__img-primary {
  opacity: 0;
  visibility: hidden;
}
[data-arts-footer-logo=secondary] .logo__img-secondary {
  opacity: 1;
  visibility: visible;
}

/*!========================================================================
 * 26. Footer Themes
 * ======================================================================!*/
[data-arts-theme-text=light] .widget_nav_menu ul.menu > li a:hover {
  color: #fff;
}
[data-arts-theme-text=light] .logo__text-title {
  color: #fff;
}
[data-arts-theme-text=light] .logo__text-tagline {
  color: var(--color-gray-2);
}

/*!========================================================================
 * 27. Form
 * ======================================================================!*/
.form {
  width: 100%;
}

.form__submit {
  margin-top: 1.5em;
}

.form__col {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.form__col_submit {
  margin-top: 1.5em;
  margin-bottom: 0;
}

.form__error, span.wpcf7-not-valid-tip {
  display: block;
  font-size: 12px;
  color: red;
  text-align: left;
  margin-top: 4px;
}

.form__heading {
  margin-top: 0;
  margin-bottom: 1em;
}

div.wpcf7-validation-errors, div.wpcf7-acceptance-missing {
  border-color: red;
  padding: 15px;
  margin: 1.5em 0 0;
  display: none !important;
}

@media only screen and (max-width: 992px) {
  .form__col_submit {
    text-align: left;
  }
}
/*!========================================================================
 * 28. Figure Service
 * ======================================================================!*/
.figure-service {
  padding-left: 0;
  padding-right: 0;
}

.figure-service__content {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.figure-service__footer {
  width: 100%;
  padding-top: 2em;
  border-top: 1px solid rgba(128, 128, 128, 0.3);
}

.figure-service__wrapper-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media screen and (max-width: 1600px) {
  .figure-service {
    max-width: 900px;
  }
}
@media screen and (max-width: 1280px) {
  .figure-service {
    max-width: 800px;
  }
}
@media screen and (max-width: 991px) {
  .figure-service__wrapper-bg {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    height: 500px;
    max-height: 50vh;
  }
}
/*!========================================================================
 * 29. Form Contacts
 * ======================================================================!*/
.form-contact_paddings {
  padding: 80px;
}

@media screen and (max-width: 991px) {
  .form-contact_paddings {
    padding: 40px;
  }
}
@media screen and (max-width: 991px) {
  .form-contact_paddings {
    padding: 40px 20px 60px;
  }
}
/*!========================================================================
 * 30. Gallery
 * ======================================================================!*/
.gallery {
  margin-top: 2em;
  margin-bottom: 2em;
}

.gallery-item {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
}

.gallery-columns-2 .gallery-item {
  max-width: 50%;
}

.gallery-columns-3 .gallery-item {
  max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
  max-width: 25%;
}

.gallery-columns-5 .gallery-item {
  max-width: 20%;
}

.gallery-columns-6 .gallery-item {
  max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
  max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
  max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
  max-width: 11.11%;
}

.gallery-caption {
  display: block;
}

/*!========================================================================
 * 31. Gmap
 * ======================================================================!*/
.gmap {
  width: 100%;
  height: 100%;
}

.gmap__container {
  width: 100%;
  height: 100%;
}

@media only screen and (max-width: 991px) {
  .gmap__container {
    height: 600px;
    max-height: 120vh;
  }
}
/*!========================================================================
 * 32. Grid
 * ======================================================================!*/
.grid {
  overflow: hidden;
}

.grid__sizer {
  padding: 0 !important;
  margin: 0 !important;
  height: 0 !important;
}

.grid__item {
  display: block;
  width: 100%;
}

.grid__item-link {
  display: block;
  position: relative;
}
.grid__item-link .figure-image__wrapper-img-inner {
  overflow: hidden;
}
.grid__item-link .figure-image__wrapper-img-inner, .grid__item-link .section-image__caption-horizontal, .grid__item-link .figure-image__wrapper-img-zoom {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform-origin: center center;
}
.grid__item-link .section-image__caption-vertical-left, .grid__item-link .section-image__caption-vertical-right {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.grid__item-link:hover .section-image__caption {
  color: var(--color-gray-1);
}
.grid__item-link:hover .figure-image__wrapper-img-zoom {
  transform: scale(1.15);
}
.grid__item-link:hover .figure-image__wrapper-img-inner {
  transform: scale(0.9);
}
.grid__item-link:hover .section-image__caption-horizontal {
  transform: translate(0, -1.5em);
}
.grid__item-link:hover .section-image__caption-horizontal.text-left {
  transform: translate(1.5em, -2em);
}
.grid__item-link:hover .section-image__caption-horizontal.text-right {
  transform: translate(-1.5em, -2em);
}
.grid__item-link:hover .section-image__caption-vertical-left {
  transform: rotate(-90deg) translate(2em, 1.5em);
}
.grid__item-link:hover .section-image__caption-vertical-right {
  transform: rotate(-90deg) translate(-2em, -1.5em);
}

@media screen and (max-width: 1280px) {
  .grid {
    margin-left: auto;
    margin-right: auto;
  }
}
@media screen and (max-width: 991px) {
  .grid__item-link:hover .section-image__caption-vertical-left, .grid__item-link:hover .section-image__caption-vertical-right {
    transform: rotate(0deg) translate(0, -1.5em);
  }
}
@media only screen and (min-width: 992px) {
  .grid__item_desktop-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .grid__item_desktop-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .grid__item_desktop-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .grid__item_desktop-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .grid__item_tablet-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .grid__item_tablet-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .grid__item_tablet-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .grid__item_tablet-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
}
@media only screen and (max-width: 767px) {
  .grid__item_mobile-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .grid__item_mobile-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  .grid__item_mobile-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .grid__item_mobile-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
}
/*!========================================================================
 * 33. Grid Fluid
 * ======================================================================!*/
.grid_fluid-1 {
  margin: -1vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-1 {
    margin: -20px;
  }
}

.grid__item_fluid-1 {
  padding: 1vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-1 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-1-fancy:nth-of-type(3) {
    margin-top: 2vw;
  }
}

.grid_fluid-2 {
  margin: -2vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-2 {
    margin: -20px;
  }
}

.grid__item_fluid-2 {
  padding: 2vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-2 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-2-fancy:nth-of-type(3) {
    margin-top: 4vw;
  }
}

.grid_fluid-3 {
  margin: -3vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-3 {
    margin: -20px;
  }
}

.grid__item_fluid-3 {
  padding: 3vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-3 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-3-fancy:nth-of-type(3) {
    margin-top: 6vw;
  }
}

.grid_fluid-4 {
  margin: -4vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-4 {
    margin: -20px;
  }
}

.grid__item_fluid-4 {
  padding: 4vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-4 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-4-fancy:nth-of-type(3) {
    margin-top: 8vw;
  }
}

.grid_fluid-5 {
  margin: -5vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-5 {
    margin: -20px;
  }
}

.grid__item_fluid-5 {
  padding: 5vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-5 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-5-fancy:nth-of-type(3) {
    margin-top: 10vw;
  }
}

.grid_fluid-6 {
  margin: -6vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-6 {
    margin: -20px;
  }
}

.grid__item_fluid-6 {
  padding: 6vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-6 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-6-fancy:nth-of-type(3) {
    margin-top: 12vw;
  }
}

.grid_fluid-7 {
  margin: -7vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-7 {
    margin: -20px;
  }
}

.grid__item_fluid-7 {
  padding: 7vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-7 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-7-fancy:nth-of-type(3) {
    margin-top: 14vw;
  }
}

.grid_fluid-8 {
  margin: -8vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-8 {
    margin: -20px;
  }
}

.grid__item_fluid-8 {
  padding: 8vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-8 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-8-fancy:nth-of-type(3) {
    margin-top: 16vw;
  }
}

.grid_fluid-9 {
  margin: -9vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-9 {
    margin: -20px;
  }
}

.grid__item_fluid-9 {
  padding: 9vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-9 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-9-fancy:nth-of-type(3) {
    margin-top: 18vw;
  }
}

.grid_fluid-10 {
  margin: -10vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-10 {
    margin: -20px;
  }
}

.grid__item_fluid-10 {
  padding: 10vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-10 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-10-fancy:nth-of-type(3) {
    margin-top: 20vw;
  }
}

.grid_fluid-11 {
  margin: -11vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-11 {
    margin: -20px;
  }
}

.grid__item_fluid-11 {
  padding: 11vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-11 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-11-fancy:nth-of-type(3) {
    margin-top: 22vw;
  }
}

.grid_fluid-12 {
  margin: -12vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-12 {
    margin: -20px;
  }
}

.grid__item_fluid-12 {
  padding: 12vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-12 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-12-fancy:nth-of-type(3) {
    margin-top: 24vw;
  }
}

.grid_fluid-13 {
  margin: -13vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-13 {
    margin: -20px;
  }
}

.grid__item_fluid-13 {
  padding: 13vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-13 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-13-fancy:nth-of-type(3) {
    margin-top: 26vw;
  }
}

.grid_fluid-14 {
  margin: -14vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-14 {
    margin: -20px;
  }
}

.grid__item_fluid-14 {
  padding: 14vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-14 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-14-fancy:nth-of-type(3) {
    margin-top: 28vw;
  }
}

.grid_fluid-15 {
  margin: -15vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-15 {
    margin: -20px;
  }
}

.grid__item_fluid-15 {
  padding: 15vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-15 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-15-fancy:nth-of-type(3) {
    margin-top: 30vw;
  }
}

.grid_fluid-16 {
  margin: -16vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-16 {
    margin: -20px;
  }
}

.grid__item_fluid-16 {
  padding: 16vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-16 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-16-fancy:nth-of-type(3) {
    margin-top: 32vw;
  }
}

.grid_fluid-17 {
  margin: -17vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-17 {
    margin: -20px;
  }
}

.grid__item_fluid-17 {
  padding: 17vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-17 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-17-fancy:nth-of-type(3) {
    margin-top: 34vw;
  }
}

.grid_fluid-18 {
  margin: -18vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-18 {
    margin: -20px;
  }
}

.grid__item_fluid-18 {
  padding: 18vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-18 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-18-fancy:nth-of-type(3) {
    margin-top: 36vw;
  }
}

.grid_fluid-19 {
  margin: -19vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-19 {
    margin: -20px;
  }
}

.grid__item_fluid-19 {
  padding: 19vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-19 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-19-fancy:nth-of-type(3) {
    margin-top: 38vw;
  }
}

.grid_fluid-20 {
  margin: -20vw;
}
@media only screen and (max-width: 767px) {
  .grid_fluid-20 {
    margin: -20px;
  }
}

.grid__item_fluid-20 {
  padding: 20vw;
}
@media only screen and (max-width: 767px) {
  .grid__item_fluid-20 {
    padding: 20px;
  }
}

@media only screen and (min-width: 768px) {
  .grid:not(.grid_filtered) .grid__item_fluid-20-fancy:nth-of-type(3) {
    margin-top: 40vw;
  }
}

/*!========================================================================
 * 34. Header
 * ======================================================================!*/
.header {
  pointer-events: none;
  padding-top: var(--gutter-vertical);
  padding-bottom: 0;
}
.header a, .header select, .header input {
  pointer-events: initial;
}
.header.opened .logo {
  pointer-events: none;
}

.header_absolute {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 500;
}

.header_fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 500;
}

.header__container {
  position: relative;
  z-index: 501;
}

.header__col {
  opacity: 0;
  visibility: hidden;
}

.header__wrapper-burger {
  position: absolute;
  width: 80px;
  height: 80px;
}

.header__burger {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 38px;
  vertical-align: middle;
  cursor: pointer;
  pointer-events: initial;
  z-index: 500;
}
.header__burger:hover .header__burger-line:nth-of-type(1) {
  transform: scaleX(1);
}
.header__burger:hover .header__burger-line:nth-of-type(2) {
  transform: scaleX(0.75);
}
.header__burger:hover .header__burger-line:nth-of-type(3) {
  transform: scaleX(1);
}

.header__burger_opened .header__burger-line:nth-of-type(1), .header__burger_opened:hover .header__burger-line:nth-of-type(1) {
  transform: scaleX(1) rotate(45deg) translate(13px, 20px);
}
.header__burger_opened .header__burger-line:nth-of-type(2), .header__burger_opened:hover .header__burger-line:nth-of-type(2) {
  transform: scaleX(0);
}
.header__burger_opened .header__burger-line:nth-of-type(3), .header__burger_opened:hover .header__burger-line:nth-of-type(3) {
  transform: scaleX(1) rotate(-45deg) translate(13px, -20px);
}

.header__burger-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: var(--color-dark-2);
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
.header__burger-line:nth-of-type(1) {
  top: 9px;
  left: 0;
  transform-origin: right center;
  transform: scaleX(0.75);
}
.header__burger-line:nth-of-type(2) {
  top: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  transform-origin: left center;
}
.header__burger-line:nth-of-type(3) {
  bottom: 9px;
  left: 0;
  transform-origin: right center;
  transform: scaleX(0.75);
}

.header__wrapper-menu {
  position: relative;
  width: 100%;
  max-width: 50%;
  margin: auto 0;
}

.header__wrapper-overlay-menu {
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 500;
  background: none !important;
  background-color: unset !important;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  opacity: 0;
  visibility: hidden;
  pointer-events: initial;
}

.header_menu-right .menu .sub-menu {
  left: auto;
  right: 15px;
}
.header_menu-right .menu .sub-menu ul {
  left: auto;
  right: calc(100% + 1px);
}

.header__overlay-menu-back {
  display: inline-block;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  left: -2px;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  pointer-events: initial;
}

.header-curtain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  transform: translateY(100%);
  margin: auto;
  z-index: 100;
  overflow: hidden;
}

.header-curtain_transition {
  z-index: 101;
}

.header__wrapper-overlay-widgets {
  margin-top: auto;
  width: 100%;
  max-width: 50%;
}

.header__widget-content {
  font-size: 14px;
}
.header__widget-content p {
  font-size: 14px;
  line-height: 2;
  margin-top: 0;
  margin-bottom: 0;
}

@media screen and (max-width: 991px) {
  .header__wrapper-overlay-menu {
    text-align: center;
  }
  .header__wrapper-menu {
    max-width: 100%;
    margin-top: calc(var(--gutter-horizontal) * 2);
    margin-bottom: calc(var(--gutter-horizontal) * 2);
  }
  .header__wrapper-overlay-widgets {
    max-width: 100%;
    margin-top: 0;
    margin-bottom: calc(var(--gutter-horizontal) * 2);
  }
  .header__widget {
    margin-bottom: 1em;
  }
  .header__widget:last-child {
    margin-bottom: 2em;
  }
  .header__burger {
    width: 30px;
    height: 35px;
  }
  .header__burger_opened .header__burger-line:nth-of-type(1), .header__burger_opened:hover .header__burger-line:nth-of-type(1) {
    transform: scaleX(1) rotate(45deg) translate(10px, 15px);
  }
  .header__burger_opened .header__burger-line:nth-of-type(2), .header__burger_opened:hover .header__burger-line:nth-of-type(2) {
    transform: scaleX(0);
  }
  .header__burger_opened .header__burger-line:nth-of-type(3), .header__burger_opened:hover .header__burger-line:nth-of-type(3) {
    transform: scaleX(1) rotate(-45deg) translate(11px, -16px);
  }
  .header__overlay-menu-back {
    top: 2px;
  }
}
/*!========================================================================
 * 35. Header Logo
 * ======================================================================!*/
[data-arts-header-logo=primary] .logo__img-primary {
  opacity: 1;
  visibility: visible;
}
[data-arts-header-logo=primary] .logo__img-secondary {
  opacity: 0;
  visibility: hidden;
}

[data-arts-header-logo=secondary] .logo__img-primary {
  opacity: 0;
  visibility: hidden;
}
[data-arts-header-logo=secondary] .logo__img-secondary {
  opacity: 1;
  visibility: visible;
}
[data-arts-header-logo=secondary] .logo__text-title {
  color: #fff;
}
[data-arts-header-logo=secondary] .logo__text-tagline {
  color: var(--color-gray-2);
}

/*!========================================================================
 * 36. Header Sticky
 * ======================================================================!*/
.js-header-sticky {
  transition: all 0.4s ease;
  box-shadow: 0px 0px 30px 0px rgba(24, 24, 24, 0);
  will-change: padding, box-shadow, background-color;
}

.header_sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: 15px;
  padding-bottom: 15px;
  box-shadow: 0px 0px 30px 0px rgba(24, 24, 24, 0.1);
}
.header_sticky[data-arts-header-sticky-logo=primary] .logo__img-primary {
  opacity: 1;
  visibility: visible;
}
.header_sticky[data-arts-header-sticky-logo=primary] .logo__img-secondary {
  opacity: 0;
  visibility: hidden;
}
.header_sticky[data-arts-header-sticky-logo=primary] .logo__text-title {
  color: var(--paragraph-color-dark);
}
.header_sticky[data-arts-header-sticky-logo=primary] .logo__text-tagline {
  color: var(--color-gray-1);
}
.header_sticky[data-arts-header-sticky-logo=secondary] .logo__img-primary {
  opacity: 0;
  visibility: hidden;
}
.header_sticky[data-arts-header-sticky-logo=secondary] .logo__img-secondary {
  opacity: 1;
  visibility: visible;
}
.header_sticky[data-arts-header-sticky-logo=secondary] .logo__text-title {
  color: #fff;
}
.header_sticky[data-arts-header-sticky-logo=secondary] .logo__text-tagline {
  color: var(--color-gray-2);
}

/*!========================================================================
 * 37. Header Themes
 * ======================================================================!*/
.header[data-arts-theme-text=light] .logo__text-title {
  color: #fff;
}
.header[data-arts-theme-text=light] a:hover {
  color: #fff;
}
.header[data-arts-theme-text=light] .header__burger-line {
  background-color: #fff;
}

.header.bg-dark-1 .header__burger-line, .header.bg-dark-2 .header__burger-line, .header.bg-dark-3 .header__burger-line, .header.bg-dark-4 .header__burger-line {
  background-color: #fff;
}
.header.bg-light-1 .header__burger-line, .header.bg-light-2 .header__burger-line, .header.bg-light-3 .header__burger-line, .header.bg-light-4 .header__burger-line, .header.bg-white .header__burger-line {
  background-color: var(--color-gray-1);
}

.header.opened[data-arts-header-overlay-theme=light] {
  color: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] p, .header.opened[data-arts-header-overlay-theme=light] .paragraph {
  color: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] .header__burger-line {
  background-color: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=light] .header__burger:hover .header__burger-line {
  background-color: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] a {
  color: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=light] a:hover {
  color: var(--color-dark-1);
}
.header.opened[data-arts-header-overlay-theme=light] .header__overlay-menu-back {
  color: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] .header__overlay-menu-back:hover .arrow .circle {
  stroke: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] .header__overlay-menu-back:hover .arrow__pointer {
  background-color: var(--color-dark-2);
}
.header.opened[data-arts-header-overlay-theme=light] .header__overlay-menu-back:hover .arrow__triangle {
  border-color: transparent transparent transparent var(--color-dark-2);
}

.header.opened[data-arts-header-overlay-theme=dark] {
  color: #fff;
}
.header.opened[data-arts-header-overlay-theme=dark] .header__burger-line {
  background-color: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] .header__burger:hover .header__burger-line {
  background-color: #fff;
}
.header.opened[data-arts-header-overlay-theme=dark] a {
  color: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] a:hover {
  color: #fff;
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back .arrow .circle {
  stroke: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back .arrow__pointer {
  background-color: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back .arrow__triangle {
  border-color: transparent transparent transparent var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back:hover .arrow .circle {
  stroke: var(--color-gray-1);
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back:hover .arrow__pointer {
  background-color: #fff;
}
.header.opened[data-arts-header-overlay-theme=dark] .header__overlay-menu-back:hover .arrow__triangle {
  border-color: transparent transparent transparent #fff;
}

/*!========================================================================
 * 38. Input Float
 * ======================================================================!*/
.input-float {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 0;
}

.input-float__label {
  position: absolute;
  top: 0.75em;
  left: 0;
  margin: auto;
  display: block;
  font-size: 16px;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transform-origin: left center;
  cursor: text;
  color: var(--color-gray-1);
}

.input-float__input {
  display: block;
  width: 100%;
  width: 100%;
  border-bottom: 1px solid var(--color-gray-2);
  border-top: none;
  border-right: none;
  border-left: none;
  outline: none;
  padding: 10px 0 4px;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  background-color: transparent;
  border-radius: 0;
  color: inherit;
}

.input-float__input_textarea {
  resize: none;
  min-height: 200px;
}

.input-float__input_focused + .input-float__label, .input-float__input_not-empty + .input-float__label {
  transform: scale(0.8571) translateY(-1.75rem);
}

.input-float__input_focused {
  border-color: var(--color-dark-1);
}
.input-float__input_focused + .input-float__label {
  color: var(--color-dark-1);
}

/*!========================================================================
 * 39. Input Search
 * ======================================================================!*/
.input-search__input {
  padding-right: 30px;
}

.input-search__submit {
  display: inline-block;
  font-size: 18px;
  width: 24px;
  height: 24px;
  padding: 0;
  position: absolute;
  right: 0;
  top: 12px;
  bottom: 0;
  margin: auto;
  background: transparent;
  border: none;
  color: var(--color-gray-1);
  outline: none;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  cursor: pointer;
}
.input-search__submit:hover {
  color: var(--color-dark-3);
}
.input-search__submit:focus {
  outline: none;
}

/*!========================================================================
 * 40. Hover Zoom
 * ======================================================================!*/
.hover-zoom {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.hover-zoom:hover:not(:focus) .hover-zoom__zoom {
  transform: scale(1.15);
}
.hover-zoom:hover:not(:focus) .hover-zoom__inner {
  transform: scale(0.9);
}
.hover-zoom:hover:not(:focus) .section-image__caption-horizontal {
  transform: translate(0, -15px);
  transition-delay: 0.06s;
}
.hover-zoom:hover:not(:focus) .section-image__caption-horizontal.text-left {
  transform: translate(15px, -15px);
}
.hover-zoom:hover:not(:focus) .section-image__caption-horizontal.text-right {
  transform: translate(-15px, -15px);
}

.hover-zoom__inner {
  overflow: hidden;
}

.hover-zoom__zoom, .hover-zoom__inner {
  transform-origin: center center;
  transition: transform 0.3s ease;
  will-change: transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

.hover-zoom__caption {
  transition: transform 0.3s ease;
  will-change: transform;
}

/*!========================================================================
 * 41. Lazy
 * ======================================================================!*/
.lazy {
  background-color: rgba(136, 136, 136, 0.1);
}
.lazy > img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.lazy_loaded {
  background-color: unset;
}

.lazy__img:not(img) {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.lazy-bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: rgba(136, 136, 136, 0.1);
}

.lazy-bg_loaded {
  background-color: unset;
}

/*!========================================================================
 * 42. Logo
 * ======================================================================!*/
.logo {
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.logo__wrapper-img {
  position: relative;
  align-items: center;
  flex: 1 0 auto;
  height: 100%;
  margin-right: 15px;
}
.logo__wrapper-img img {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  display: block;
}

.logo__text-title {
  display: block;
  font-family: var(--font-secondary);
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
  color: var(--paragraph-color-dark);
}

.logo__text-tagline {
  display: block;
  font-style: italic;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.3;
}

.logo__img-secondary {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
}

.logo__description {
  margin-top: 1em;
}

/*!========================================================================
 * 43. List Projects
 * ======================================================================!*/
.list-projects {
  position: relative;
  z-index: 50;
}

.list-projects_hover .list-projects__item {
  opacity: 0.05;
  border-color: rgba(0, 0, 0, 0);
}
.list-projects_hover .list-projects__item:hover {
  opacity: 1;
}

.list-projects__item {
  display: block;
  position: relative;
  z-index: 60;
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
  color: var(--color-dark-1);
}
.list-projects__item.blend-difference:hover {
  color: #fff;
}
.list-projects__item.blend-difference:hover .list-projects__wrapper-link {
  color: #fff;
}

.list-projects:not(.list-demos) .list-projects__item:first-child {
  padding-top: 0;
}
.list-projects:not(.list-demos) .list-projects__item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.list-demos__item {
  border-bottom: none;
}

.list-demos__divider {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 1px;
  height: 100%;
  background-color: rgba(128, 128, 128, 0.3);
}

.list-projects__heading {
  margin-top: 0;
  margin-bottom: 0;
  z-index: 50;
}

.list-projects__wrapper-link {
  color: var(--color-gray-1);
  z-index: 50;
  padding-left: 120px;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.list-projects__wrapper-cover {
  position: fixed;
  left: 0;
  top: 0;
  z-index: -1;
}

.list-projects__covers {
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  pointer-events: none;
}

.list-projects__cover-reveal {
  position: relative !important;
  flex: 1 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: initial !important;
  will-change: height transform;
}
.list-projects__cover-reveal img {
  top: auto !important;
  left: auto !important;
  position: initial !important;
}

.list-projects__cover-wrapper {
  height: 0;
  padding-bottom: initial !important;
}

.list-projects__cover {
  display: none;
  flex: 1 0 50%;
}
.list-projects__cover > div {
  width: 100%;
  height: 100%;
}

.list-projects__wrapper-cover-inner {
  position: relative;
}

.list-projects__items {
  position: relative;
  z-index: 150;
}

.list-project__canvas {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 100;
}

.list-projects__wrapper-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: inline-block;
}

.list-projects__thumbnail {
  width: 240px;
  height: 240px;
}

.list-projects__thumbnail_small {
  width: 180px;
  height: 180px;
}

@media screen and (max-width: 767px) {
  .list-projects__item {
    flex-wrap: wrap;
  }
  .list-projects__heading {
    display: inline-block;
    margin-top: 0.75em;
    margin-bottom: 0.5em;
  }
  .list-projects__wrapper-link {
    line-height: 1;
    padding-left: 0;
  }
  .list-projects__cover {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  .list-projects_hover .list-projects__item {
    opacity: 1;
    border-color: rgba(128, 128, 128, 0.3);
  }
  .list-projects_hover .list-projects__item:hover {
    opacity: 1;
  }
  .list-project__canvas {
    display: none;
  }
  .list-projects__thumbnail {
    margin-left: auto;
    margin-right: auto;
  }
  .list-demos__item {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}
/*!========================================================================
 * 44. Menu
 * ======================================================================!*/
.menu {
  position: relative;
  list-style-type: none;
  padding: 0;
  margin: 0;
  margin: 0 -15px;
  word-wrap: normal;
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
}
@media screen and (min-width: 320px) {
  .menu {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .menu {
    font-size: calc(13 * 1px);
  }
}
.menu > li {
  display: inline-block;
}
.menu > li:not(:last-child) {
  margin-right: 17px;
}
.menu > li a {
  display: block;
  padding: 0px 15px;
}
.menu > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
}
.menu .menu-item-has-children {
  position: relative;
}
.menu .menu-item-has-children > a:hover ~ ul {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
  z-index: 50;
}

.menu.menu_disabled .sub-menu {
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translate(0px, 20px) !important;
}

.menu .sub-menu {
  position: absolute;
  top: 100%;
  left: 15px;
  transform: translate(0px, 20px);
  list-style-type: none;
  padding: 0;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
  background-color: #fff;
  border-radius: 2px;
  text-align: left;
  box-shadow: 0px 0px 30px 0px rgba(24, 24, 24, 0.04);
}
.menu .sub-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translate(0px, 0px);
  z-index: 50;
}
.menu .sub-menu > li {
  white-space: nowrap;
}
.menu .sub-menu > li a {
  padding: 10px 15px;
  background-color: transparent;
  border-left: 2px solid transparent;
}
.menu .sub-menu > li a:hover {
  background-color: #fafafa;
  border-color: var(--color-dark-2);
  color: var(--color-dark-2);
}
.menu .sub-menu ul {
  top: 0;
  left: calc(100% + 1px);
  transform: translate(10px, 0px);
  opacity: 0;
  visibility: hidden;
}

/*!========================================================================
 * 45. Menu Overlay
 * ======================================================================!*/
.menu-overlay {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.menu-overlay > li {
  display: block;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.menu-overlay > li > a {
  display: inline-block;
}

.menu-overlay__item-wrapper {
  width: 100%;
  height: 100%;
}

.menu-overlay .sub-menu {
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 100%;
  list-style-type: none;
  padding: 0;
  margin: 0;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
}
.menu-overlay .sub-menu > li {
  display: block;
}
.menu-overlay .sub-menu > li > a {
  display: inline-block;
  padding: 8px 0;
}

/*!========================================================================
 * 46. Modal
 * ======================================================================!*/
.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 6000;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.modal__message {
  font-size: 18px;
}

.modal-content__wrapper-button {
  text-align: center;
}
.modal-content__wrapper-button .button {
  min-width: 200px;
}

.modal__close {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  z-index: 60;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.modal-dialog_container {
  max-width: 980px !important;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
.modal.show .modal-dialog {
  transform: none;
}

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-header, .modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 6px;
  outline: 0;
  padding: 3em;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.7;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}
.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
.modal-footer > :not(:first-child) {
  margin-left: 0.25rem;
}
.modal-footer > :not(:last-child) {
  margin-right: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 600px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
  }
  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg, .modal-xl {
    max-width: 800px;
  }
  .modal__message {
    font-size: 24px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
/*!========================================================================
 * 47. Overlay
 * ======================================================================!*/
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  pointer-events: none;
}

.overlay_dark {
  background-color: rgba(0, 0, 0, 0.6);
}

.overlay_dark-30 {
  background-color: rgba(0, 0, 0, 0.2);
}

.overlay_deither:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  opacity: 0.2;
  background: url(data:image/png;base64,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) repeat;
}

.overlay_circle-dark {
  background-image: radial-gradient(circle at center, rgba(0, 0, 0, 0) 0%, rgb(0, 0, 0) 90%, rgb(0, 0, 0) 100%);
}

.overlay_circle-dark-70 {
  background-image: radial-gradient(circle at center, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.7) 100%);
}

.overlay_bottom-dark {
  background-image: linear-gradient(0deg, rgb(17, 17, 17) 0%, rgba(17, 17, 17, 0) 100%);
}

.overlay_top-dark {
  background-image: linear-gradient(180deg, rgb(17, 17, 17) 0%, rgba(17, 17, 17, 0) 100%);
}

.overlay_light {
  background-color: rgba(255, 255, 255, 0.8);
}

/*!========================================================================
 * 48. Page Indicator
 * ======================================================================!*/
.page-indicator {
  pointer-events: none;
  position: fixed;
  bottom: 60px;
  right: var(--gutter-horizontal);
  z-index: 500;
}

/*!========================================================================
 * 49. Pagination
 * ======================================================================!*/
.pagination {
  margin-top: calc(1 * (40 * 1px));
  border-top: 2px solid var(--color-dark-1);
  padding: 20px 0 0;
  font-family: var(--font-secondary);
}
@media screen and (min-width: 320px) {
  .pagination {
    margin-top: calc(1 * (40 * 1px + (90 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pagination {
    margin-top: calc(1 * (90 * 1px));
  }
}
.pagination .nav-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pagination .nav-links__container {
  margin-left: auto;
  margin-right: auto;
}
.pagination .page-numbers {
  display: inline-block;
  line-height: 1;
  font-weight: 600;
  margin: 0 15px;
  background-image: none;
  vertical-align: middle;
}
.pagination .page-numbers.prev {
  font-size: 20px;
  font-weight: normal;
  margin-left: 0;
}
.pagination .page-numbers.next {
  font-size: 20px;
  font-weight: normal;
  margin-right: 0;
}
.pagination .page-numbers:not(a) {
  color: var(--color-gray-1);
}

.page-links {
  margin-top: calc(1 * (40 * 1px));
  border-top: 2px solid var(--color-dark-1);
  padding: 20px 0 0;
}
@media screen and (min-width: 320px) {
  .page-links {
    margin-top: calc(1 * (40 * 1px + (90 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .page-links {
    margin-top: calc(1 * (90 * 1px));
  }
}
.page-links .page-number {
  display: inline-block;
  line-height: 1;
  font-weight: 600;
  margin: 0 15px;
  border: none;
}
.page-links a {
  background-image: none;
}
.page-links .post-page-numbers:not(a) {
  color: var(--color-gray-1);
}

.comments-pagination {
  text-align: center;
}
.comments-pagination .page-numbers {
  display: inline-block;
  line-height: 1;
  font-weight: 600;
  margin: 0 20px;
}

@media screen and (max-width: 991px) {
  .pagination {
    padding: 10px 0 0;
  }
  .page-links {
    padding: 10px 0 0;
  }
}
/*!========================================================================
 * 50. Arts Parallax
 * ======================================================================!*/
[data-arts-parallax] {
  position: relative;
  overflow: hidden;
}
[data-arts-parallax] img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
[data-arts-parallax] > div {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/*!========================================================================
 * 51. Parallax
 * ======================================================================!*/
[data-scroll-speed="-1.1"] .lazy-bg, [data-scroll-speed="-1.1"] .lazy, [data-scroll-speed="1.1"] .lazy-bg, [data-scroll-speed="1.1"] .lazy {
  transform: scale(1.025);
  transform-origin: center center;
}

[data-scroll-speed="-1.2"] .lazy-bg, [data-scroll-speed="-1.2"] .lazy, [data-scroll-speed="1.2"] .lazy-bg, [data-scroll-speed="1.2"] .lazy {
  transform: scale(1.05);
  transform-origin: center center;
}

[data-scroll-speed="-1.3"] .lazy-bg, [data-scroll-speed="-1.3"] .lazy, [data-scroll-speed="1.3"] .lazy-bg, [data-scroll-speed="1.3"] .lazy {
  transform: scale(1.075);
  transform-origin: center center;
}

[data-scroll-speed="-1.4"] .lazy-bg, [data-scroll-speed="-1.4"] .lazy, [data-scroll-speed="1.4"] .lazy-bg, [data-scroll-speed="1.4"] .lazy {
  transform: scale(1.1);
  transform-origin: center center;
}

[data-scroll-speed="-1.5"] .lazy-bg, [data-scroll-speed="-1.5"] .lazy, [data-scroll-speed="1.5"] .lazy-bg, [data-scroll-speed="1.5"] .lazy {
  transform: scale(1.125);
  transform-origin: center center;
}

[data-scroll-speed="-1.6"] .lazy-bg, [data-scroll-speed="-1.6"] .lazy, [data-scroll-speed="1.6"] .lazy-bg, [data-scroll-speed="1.6"] .lazy {
  transform: scale(1.15);
  transform-origin: center center;
}

[data-scroll-speed="-1.7"] .lazy-bg, [data-scroll-speed="-1.7"] .lazy, [data-scroll-speed="1.7"] .lazy-bg, [data-scroll-speed="1.7"] .lazy {
  transform: scale(1.175);
  transform-origin: center center;
}

[data-scroll-speed="-1.8"] .lazy-bg, [data-scroll-speed="-1.8"] .lazy, [data-scroll-speed="1.8"] .lazy-bg, [data-scroll-speed="1.8"] .lazy {
  transform: scale(1.2);
  transform-origin: center center;
}

[data-scroll-speed="-1.9"] .lazy-bg, [data-scroll-speed="-1.9"] .lazy, [data-scroll-speed="1.9"] .lazy-bg, [data-scroll-speed="1.9"] .lazy {
  transform: scale(1.225);
  transform-origin: center center;
}

/*!========================================================================
 * 52. Post
 * ======================================================================!*/
.post {
  max-width: 900px;
}
.post blockquote:before {
  content: url("../img/general/quote-black.svg");
  display: block;
  width: 2.5em;
  height: 2.5em;
  float: left;
  margin-right: 1em;
  margin-top: 0.33em;
}
.post blockquote cite {
  display: block;
  text-align: right;
}
.post blockquote cite:before {
  content: "";
  width: 60px;
  height: 1px;
  display: inline-block;
  vertical-align: middle;
  background-color: var(--color-gray-1);
  margin: 1em;
}

.post__content > *:first-child, .post__comments > *:first-child {
  margin-top: 0;
}
.post__content > *:last-child, .post__comments > *:last-child {
  margin-bottom: 0;
}
.post__content ul, .post__comments ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  margin-bottom: 1.5em;
}
.post__content ul li, .post__comments ul li {
  display: block;
  margin-top: 1em;
  margin-bottom: 1em;
}
.post__content ul ul, .post__content ul ol, .post__comments ul ul, .post__comments ul ol {
  padding-left: 1em;
}
.post__content ul:not(.wp-block-gallery), .post__comments ul:not(.wp-block-gallery) {
  padding-left: 0.25em;
}
.post__content ul:not(.wp-block-gallery) > li:before, .post__comments ul:not(.wp-block-gallery) > li:before {
  content: "";
  display: inline-block;
  width: 10px;
  height: 1px;
  vertical-align: middle;
  margin-right: 0.5em;
  margin-bottom: 3px;
}
.post__content ol, .post__comments ol {
  margin-bottom: 24px;
  padding-left: 1.5em;
}
.post__content ol li, .post__comments ol li {
  display: list-item;
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.post__content ol ul, .post__content ol ol, .post__comments ol ul, .post__comments ol ol {
  padding-left: 1.5em;
}

.post__media {
  margin-bottom: 2em;
}

.post__tags {
  border-top: 2px solid var(--color-dark-1);
  padding-top: 1.5em;
}

.post__read-more {
  padding: 16px 30px;
}

.sticky {
  padding: 50px;
}

/*!========================================================================
 * 53. Post Meta
 * ======================================================================!*/
.post-meta {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.post-meta li {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
}
.post-meta li:not(:last-child):after {
  content: "/";
  color: var(--color-dark-1);
  display: inline-block;
  margin-left: 6px;
  margin-right: 5px;
}
.post-meta ul {
  padding-left: 0;
}

/*!========================================================================
 * 54. Preloader
 * ======================================================================!*/
.preloader {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 99;
  background-color: #fff;
  overflow: hidden;
}

.preloader__circle {
  position: fixed;
  top: 50%;
  left: 50%;
  right: 0;
  transform: translate(-50%, -50%);
  width: calc(1 * (var(--preloader-circle-min-size) * 1px));
  height: calc(1 * (var(--preloader-circle-min-size) * 1px));
  max-width: calc(100vh - var(--gutter-vertical) * 3);
  max-height: calc(100vh - var(--gutter-vertical) * 3);
  border-radius: 100%;
  z-index: 1;
  pointer-events: none;
}
@media screen and (min-width: 320px) {
  .preloader__circle {
    width: calc(1 * (var(--preloader-circle-min-size) * 1px + (var(--preloader-circle-max-size) - var(--preloader-circle-min-size)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .preloader__circle {
    width: calc(1 * (var(--preloader-circle-max-size) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .preloader__circle {
    height: calc(1 * (var(--preloader-circle-min-size) * 1px + (var(--preloader-circle-max-size) - var(--preloader-circle-min-size)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .preloader__circle {
    height: calc(1 * (var(--preloader-circle-max-size) * 1px));
  }
}

.preloader__content {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  padding: 30px;
  width: calc(1 * (var(--preloader-circle-min-size) * 1px));
  height: calc(1 * (var(--preloader-circle-min-size) * 1px));
  max-height: calc(100vh - var(--gutter-vertical) * 3);
}
@media screen and (min-width: 320px) {
  .preloader__content {
    width: calc(1 * (var(--preloader-circle-min-size) * 1px + (var(--preloader-circle-max-size) - var(--preloader-circle-min-size)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .preloader__content {
    width: calc(1 * (var(--preloader-circle-max-size) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .preloader__content {
    height: calc(1 * (var(--preloader-circle-min-size) * 1px + (var(--preloader-circle-max-size) - var(--preloader-circle-min-size)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .preloader__content {
    height: calc(1 * (var(--preloader-circle-max-size) * 1px));
  }
}

.preloader__header {
  width: 100%;
}

.preloader__counter {
  white-space: nowrap;
}

.preloader__counter-number {
  display: inline-block;
  width: 40px;
}

.preloader__counter-current {
  text-align: right;
}

.preloader__counter-total {
  text-align: left;
}

.preloader__curtain-svg {
  width: 100%;
  height: 100%;
  fill: #ccc;
}

.preloader__curtain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preloader__curtain-curve {
  visibility: hidden;
}

.preloader__curtain-normal {
  visibility: visible;
}

/*!========================================================================
 * 55. Pswp
 * ======================================================================!*/
.pswp__button {
  outline: none;
}
.pswp__button:focus {
  outline: none;
}

.pswp__button--arrow--left, .pswp__button--arrow--right {
  width: auto;
  height: auto;
  opacity: 1;
}
.pswp__button--arrow--left:before, .pswp__button--arrow--right:before {
  display: none;
}

.pswp__button--arrow--left {
  left: 30px;
}

.pswp__button--arrow--right {
  right: 30px;
}

.pswp__top-bar {
  padding: 15px 15px 0;
}

.pswp__counter {
  left: 25px;
  top: 15px;
}

.pswp__wrapper-embed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 120px;
}
.pswp__wrapper-embed iframe {
  width: 100%;
  height: 100%;
}
.pswp__wrapper-embed video {
  width: 100%;
  height: auto;
}

@media screen and (max-width: 991px) {
  .pswp__counter {
    top: 0px;
    left: 10px;
  }
  .pswp__top-bar {
    padding: 0;
  }
  .pswp__button--arrow--left {
    left: 10px;
  }
  .pswp__button--arrow--right {
    right: 10px;
  }
  .pswp__wrapper-embed {
    padding: 120px 40px;
  }
  .pswp__wrapper-embed iframe {
    width: 100%;
    height: 100%;
  }
}
/*!========================================================================
 * 56. Scroll Down
 * ======================================================================!*/
[data-arts-scroll-down] {
  cursor: pointer;
}

/*!========================================================================
 * 57. Scroll
 * ======================================================================!*/
.smooth-scroll {
  overflow: hidden;
  width: 100vw;
  height: 100vh;
}

[data-arts-os-animation]:not([data-arts-os-animation=animated]) {
  opacity: 0;
  visibility: hidden;
}

body.elementor-editor-active [data-arts-os-animation] {
  opacity: 1;
  visibility: visible;
}

.c-scrollbar {
  display: none !important;
}

[data-arts-scroll-fixed] {
  width: 100% !important;
  left: 0 !important;
  top: 0 !important;
}

/*!========================================================================
 * 58. Section
 * ======================================================================!*/
.section {
  position: relative;
}

.section-fullheight {
  display: flex;
}

.section-fullheight__inner {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  min-height: 100vh;
}

.section-fullheight__inner_mobile {
  min-height: calc(var(--fix-bar-vh, 1vh) * 100);
}

.section_z-100 {
  z-index: 100;
}

.section__content {
  position: relative;
  z-index: 60;
}

.section__headline {
  display: block;
  width: calc(1 * (60 * 1px));
  height: 1px;
  background-color: var(--color-gray-1);
}
@media screen and (min-width: 320px) {
  .section__headline {
    width: calc(1 * (60 * 1px + (120 - 60) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section__headline {
    width: calc(1 * (120 * 1px));
  }
}

.section__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

@media screen and (max-width: 991px) {
  .section-fullheight__inner_mobile-auto {
    min-height: 0;
    height: 100%;
    display: block;
  }
}
/*!========================================================================
 * 59. Section Height
 * ======================================================================!*/
.section_h-100 {
  height: 100px;
  max-height: 100vh;
}

.section_h-200 {
  height: 200px;
  max-height: 100vh;
}

.section_h-300 {
  height: 300px;
  max-height: 100vh;
}

.section_h-400 {
  height: 400px;
  max-height: 100vh;
}

.section_h-500 {
  height: 500px;
  max-height: 100vh;
}

.section_h-600 {
  height: 600px;
  max-height: 100vh;
}

.section_h-700 {
  height: 700px;
  max-height: 100vh;
}

.section_h-800 {
  height: 800px;
  max-height: 100vh;
}

.section_h-900 {
  height: 900px;
  max-height: 100vh;
}

.section_h-100vh {
  height: 100vh;
}

.section_w-container-right {
  max-width: 100%;
  width: calc(100% - (100vw - 1140px) / 2 - 20px);
  margin-left: auto;
  text-align: left;
}

.section_w-container-left {
  max-width: 100%;
  width: calc(100% - (100vw - 1140px) / 2 - 20px);
  margin-right: auto;
  text-align: right;
}

@media screen and (max-width: 1400px) {
  .section_w-container-right, .section_w-container-left {
    width: 100%;
    text-align: center;
  }
  .section_w-container-right .section-image__caption-vertical-left, .section_w-container-right .section-image__caption-vertical-right, .section_w-container-left .section-image__caption-vertical-left, .section_w-container-left .section-image__caption-vertical-right {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    transform: none;
    padding: 0 20px;
    margin-top: 1em;
    text-align: center;
    width: 100%;
  }
  .section_w-container-right .section-image__caption.text-left, .section_w-container-right .section-image__caption.text-center, .section_w-container-right .section-image__caption.text-right, .section_w-container-left .section-image__caption.text-left, .section_w-container-left .section-image__caption.text-center, .section_w-container-left .section-image__caption.text-right {
    text-align: center !important;
  }
}
@media screen and (max-width: 767px) {
  .section_h-100 {
    max-height: 70vh;
  }
  .section_h-200 {
    max-height: 70vh;
  }
  .section_h-300 {
    max-height: 70vh;
  }
  .section_h-400 {
    max-height: 70vh;
  }
  .section_h-500 {
    max-height: 70vh;
  }
  .section_h-600 {
    max-height: 70vh;
  }
  .section_h-700 {
    max-height: 70vh;
  }
  .section_h-800 {
    max-height: 70vh;
  }
  .section_h-900 {
    max-height: 70vh;
  }
}
/*!========================================================================
 * 60. Section Offset
 * ======================================================================!*/
.section-offset__content {
  position: relative;
  width: 100%;
}

/*!========================================================================
 * 61. Section About
 * ======================================================================!*/
.section-about {
  max-width: 1680px;
  margin-left: auto;
  margin-right: auto;
}

.section-about__offset-container {
  max-width: calc(100vw - var(--gutter-horizontal));
  padding-right: 0;
  margin-left: 0;
  z-index: 10;
}

.section-about__content {
  z-index: 100;
}

.section-about__floating-image {
  margin-left: auto;
  z-index: 200;
  max-width: 420px;
  height: 420px;
  margin-bottom: -420px;
  top: -250px;
}

@media screen and (max-width: 1280px) {
  .section-about__floating-image {
    max-width: 300px;
    height: 300px;
    margin-bottom: -300px;
    top: -200px;
  }
}
@media screen and (max-width: 991px) {
  .section-about__floating-image {
    margin-bottom: -150px;
  }
}
@media screen and (max-width: 767px) {
  .section-about__floating-image {
    max-width: 200px;
    height: 200px;
    margin-bottom: -100px;
    top: -120px;
  }
}
/*!========================================================================
 * 62. Section Blog
 * ======================================================================!*/
.section-blog__post {
  max-width: 940px;
}

.section-blog__sidebar {
  max-width: 370px;
}

.section-blog__wrapper-pagination {
  margin-top: calc(1 * (40 * 1px));
}
@media screen and (min-width: 320px) {
  .section-blog__wrapper-pagination {
    margin-top: calc(1 * (40 * 1px + (90 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section-blog__wrapper-pagination {
    margin-top: calc(1 * (90 * 1px));
  }
}

.section-blog__wrapper-post {
  margin-bottom: calc(1 * (40 * 1px));
}
@media screen and (min-width: 320px) {
  .section-blog__wrapper-post {
    margin-bottom: calc(1 * (40 * 1px + (90 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section-blog__wrapper-post {
    margin-bottom: calc(1 * (90 * 1px));
  }
}
.section-blog__wrapper-post:last-child {
  margin-bottom: 0 !important;
}

@media screen and (max-width: 991px) {
  .section-blog__sidebar {
    max-width: 100%;
  }
}
/*!========================================================================
 * 63. Section CTA
 * ======================================================================!*/
.section-cta__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/*!========================================================================
 * 64. Section Content
 * ======================================================================!*/
.section-content__heading > *:first-child, .section-content__text > *:first-child {
  margin-top: 0;
}
.section-content__heading > *:last-child, .section-content__text > *:last-child {
  margin-bottom: 0;
}

.section-content__text {
  max-width: 700px;
}

.text-left .section-content__text {
  margin-left: 0;
  margin-right: auto;
}

.text-center .section-content__text {
  margin-left: auto;
  margin-right: auto;
}

.text-right .section-content__text {
  margin-left: auto;
  margin-right: 0;
}

/*!========================================================================
 * 65. Section Demo
 * ======================================================================!*/
.section-demo__bg {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  max-width: calc(33vw - var(--gutter-horizontal));
}

.section-demo__bg_right {
  right: 0;
}

.section-demo__bg_left {
  left: 0;
}

.section-demo__bg_wide {
  max-width: calc(37.5vw - var(--gutter-horizontal));
}

.section-demo__stacked-images {
  display: flex;
  z-index: 100;
}

.section-demo__stacked-image {
  position: relative;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.03);
}
.section-demo__stacked-image:nth-child(1) {
  z-index: 50;
}
.section-demo__stacked-image:nth-child(2) {
  z-index: 40;
  margin-left: -50%;
  margin-top: 80px;
}
.section-demo__stacked-image:nth-child(3) {
  z-index: 30;
  margin-left: -50%;
  margin-top: 160px;
}

.section-demo__content {
  max-width: 550px;
}

.section-demo__wrapper-button {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-demo__wrapper-button_right {
  right: calc(33vw - var(--gutter-horizontal));
  transform: translateX(50%);
}

.section-demo__wrapper-button_left {
  left: calc(37.5vw - var(--gutter-horizontal));
  transform: translateX(-50%);
}

@media screen and (min-width: 1401px) {
  .section-demo__container-left {
    padding-left: 0;
  }
  .section-demo__container-right {
    padding-right: 0;
  }
}
@media screen and (max-width: 1400px) {
  .section-demo__content {
    max-width: 450px;
  }
  .section-demo__bg {
    max-width: calc(37.5vw - var(--gutter-horizontal));
  }
  .section-demo__wrapper-button_right {
    right: calc(37.5vw - var(--gutter-horizontal));
  }
  .section-demo__wrapper-button_left {
    left: calc(37.5vw - var(--gutter-horizontal));
  }
}
@media screen and (max-width: 1920px) {
  .section-demo__bg_wide {
    max-width: calc(42vw - var(--gutter-horizontal) / 2);
  }
  .section-demo__wrapper-button_left {
    left: calc(42vw - var(--gutter-horizontal) / 2);
  }
}
@media screen and (max-width: 1400px) {
  .section-demo__bg_wide {
    max-width: calc(50vw - var(--gutter-horizontal));
  }
  .section-demo__wrapper-button_left {
    left: calc(50vw - var(--gutter-horizontal));
  }
}
@media screen and (max-width: 991px) {
  .section-demo__content {
    max-width: 100%;
  }
  .section-demo__bg {
    position: relative;
    min-height: 70vh;
    max-width: 100%;
    background-color: transparent !important;
  }
  .section-demo__stacked-image:nth-child(1) {
    z-index: 50;
  }
  .section-demo__stacked-image:nth-child(2) {
    z-index: 40;
    margin-left: -25%;
    margin-top: 40px;
  }
  .section-demo__stacked-image:nth-child(3) {
    z-index: 30;
    margin-left: -25%;
    margin-top: 80px;
  }
}
/*!========================================================================
 * 66. Section Grid
 * ======================================================================!*/
.section-grid__item {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}

@media screen and (min-width: 990px) {
  .section-grid__item_padding {
    padding: calc(1 * (80 * 1px));
  }
}
@media screen and (min-width: 990px) and (min-width: 991px) {
  .section-grid__item_padding {
    padding: calc(1 * (80 * 1px + (120 - 80) * ((100vw - 991px) / 929)));
  }
}
@media screen and (min-width: 990px) and (min-width: 1920) {
  .section-grid__item_padding {
    padding: calc(1 * (120 * 1px));
  }
}
/*!========================================================================
 * 67. Section Image
 * ======================================================================!*/
.section-image {
  display: flex;
  flex-direction: column;
}

.section-image__content {
  margin-top: 40px;
}

.section-image__wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.section-image__caption {
  display: inline-block;
  margin-top: 1em;
}

.section-image__caption-vertical-left {
  position: absolute;
  bottom: -1em;
  left: -2em;
  transform: rotate(-90deg);
  transform-origin: left center;
  text-align: left;
}

.section-image__caption-vertical-right {
  position: absolute;
  top: -2em;
  right: -2em;
  transform: rotate(-90deg);
  transform-origin: right center;
  text-align: left;
}

.section-image__wrapper_absolute {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.section-image__overlay {
  z-index: 0;
}

@media screen and (max-width: 1280px) {
  .section-image__caption-vertical-left {
    left: -1.5em;
  }
  .section-image__caption-vertical-right {
    right: -1.5em;
  }
}
@media screen and (max-width: 991px) {
  .section-image__caption-vertical-left, .section-image__caption-vertical-right {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    transform: none;
    padding: 0 20px;
    margin-top: 1em;
    text-align: center;
    width: 100%;
  }
}
/*!========================================================================
 * 68. Section List Themes
 * ======================================================================!*/
@media screen and (max-width: 767px) {
  [data-arts-theme-text=dark] .list-projects__item.blend-difference {
    mix-blend-mode: initial !important;
    color: initial !important;
  }
}

@media screen and (max-width: 767px) {
  [data-arts-theme-text=light] .list-projects__item.blend-difference {
    mix-blend-mode: #fff !important;
    color: #fff !important;
  }
}

/*!========================================================================
 * 69. Section Nav Projects
 * ======================================================================!*/
.section-nav-projects[data-arts-os-animation] {
  visibility: visible !important;
}

.section-nav-projects {
  flex-wrap: wrap;
  overflow: hidden;
  height: 200vh;
}

.section-nav-projects__wrapper-scroll-down {
  position: absolute;
  bottom: calc(var(--gutter-vertical) - 10px);
  left: 0;
  right: 0;
  margin: auto;
}

.section__divider {
  display: block;
  width: 100%;
  height: 1px;
  background-color: rgba(128, 128, 128, 0.3);
  position: absolute;
}

.section__divider_top {
  top: 0;
  left: 0;
  right: 0;
}

.section-nav-projects__link {
  display: block;
  pointer-events: none;
  opacity: 0.2;
  color: var(--color-dark-2);
}

.section-nav-projects__header {
  cursor: pointer;
}
.section-nav-projects__header:hover .section-nav-projects__link {
  opacity: 1 !important;
  color: var(--color-dark-1) !important;
}

.section-nav-projects__next-image {
  right: 0;
  left: 0;
  margin: 0 auto;
  max-width: 500px;
}

.section-nav-projects__inner_actual {
  height: 100vh;
  overflow: hidden;
}

/*!========================================================================
 * 70. Section Nav Projects Themes
 * ======================================================================!*/
[data-arts-theme-text=light] .section-nav-projects__header:hover .section-nav-projects__link {
  opacity: 1 !important;
  color: #fff !important;
}
[data-arts-theme-text=light] .section-nav-projects__subheading {
  color: #fff;
}
[data-arts-theme-text=light] .section-nav-projects__link {
  opacity: 0.2;
}

/*!========================================================================
 * 71. Section Masthead
 * ======================================================================!*/
.section-masthead__inner {
  position: relative;
  overflow: hidden;
  z-index: 50;
}

.section-masthead__background_fullscreen, .section-masthead__background_halfscreen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.section-masthead__background_halfscreen-gutters {
  padding: calc(var(--gutter-horizontal) + 40px) var(--gutter-horizontal) var(--gutter-horizontal);
}

.section-masthead__text {
  display: inline-block;
  max-width: 700px;
}

.section-masthead__wrapper-scroll-down {
  display: inline-block;
  position: absolute;
  bottom: 2px;
  z-index: 50;
}

.section-masthead__wrapper-scroll-down_center {
  left: 0;
  right: 0;
  margin: auto;
}

.section-masthead__wrapper-scroll-down_left {
  left: calc(var(--gutter-horizontal) - 20px);
}

.section-masthead__wrapper-scroll-down_right {
  right: calc(var(--gutter-horizontal) - 20px);
}

.overlay.section-masthead__overlay {
  z-index: 1;
  transform: scale(1.02);
}

.section-masthead__header {
  z-index: 50;
}

.section-masthead__header_absolute {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 50;
}

.section-masthead__wrapper-info {
  margin-top: 2em;
  margin-bottom: 2em;
}
.section-masthead__wrapper-info:first-child {
  margin-top: 0;
}
.section-masthead__wrapper-info:last-child {
  margin-bottom: 0;
}

.section-masthead__meta-item {
  display: inline-block;
}
.section-masthead__meta-item:not(:last-child):after {
  content: "";
  display: inline-block;
  vertical-align: middle;
  width: 60px;
  width: calc(1 * (40 * 1px));
  height: 1px;
  margin: 1em;
  background-color: var(--color-gray-1);
}
@media screen and (min-width: 320px) {
  .section-masthead__meta-item:not(:last-child):after {
    width: calc(1 * (40 * 1px + (60 - 40) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section-masthead__meta-item:not(:last-child):after {
    width: calc(1 * (60 * 1px));
  }
}
.section-masthead__meta-item > * {
  display: inline-block;
  vertical-align: middle;
}

.section-masthead__meta-categories {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.section-masthead__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

@media screen and (max-width: 991px) {
  .section-masthead__wrapper-scroll-down {
    bottom: 10px;
  }
  .section-masthead__background_halfscreen {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    height: 100vh;
    transform: none;
  }
  .section-masthead__background_halfscreen-gutters {
    padding: 0;
  }
}
/*!========================================================================
 * 72. Section Scroll
 * ======================================================================!*/
.section-scroll {
  transition: background-color 0.4s ease;
}

/*!========================================================================
 * 73. Section Services
 * ======================================================================!*/
.section-services__wrapper-letter {
  display: inline-block;
  position: absolute;
  bottom: 66%;
  z-index: 0;
}

.section-services__wrapper-letter_right {
  right: 100px;
}

.section-services__wrapper-letter_left {
  left: 100px;
}

.section-services__letter {
  display: inline-block;
  line-height: 1;
  font-size: calc(67 * 1px);
  color: var(--color-dark-1);
  opacity: 0.05;
  font-family: var(--font-secondary);
}
@media screen and (min-width: 320px) {
  .section-services__letter {
    font-size: calc(67 * 1px + (400 - 67) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .section-services__letter {
    font-size: calc(400 * 1px);
  }
}

.section-services__wrapper-content {
  position: relative;
}

@media screen and (max-width: 991px) {
  .section-services__container {
    padding-left: 0;
    padding-right: 0;
  }
  .section-services__container > .row {
    margin-left: 0;
    margin-right: 0;
  }
}
/*!========================================================================
 * 74. Section Video
 * ======================================================================!*/
.section-video__link {
  display: block;
  margin: auto;
  width: calc(1 * (80 * 1px));
  height: calc(1 * (80 * 1px));
  border-radius: 100%;
  will-change: transform;
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  color: #fff;
  z-index: 60;
}
@media screen and (min-width: 320px) {
  .section-video__link {
    width: calc(1 * (80 * 1px + (160 - 80) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section-video__link {
    width: calc(1 * (160 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .section-video__link {
    height: calc(1 * (80 * 1px + (160 - 80) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .section-video__link {
    height: calc(1 * (160 * 1px));
  }
}
@media screen and (min-width: 320px) {
  .section-video__link {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .section-video__link {
    font-size: calc(13 * 1px);
  }
}

.section-video__link-inner {
  background-color: var(--color-dark-1);
  color: #fff;
  border-radius: 100%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-touchevents .section-video__link-inner[data-arts-cursor-label]:hover .section-video__icon {
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
}

.section-video__container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-video__icon.material-icons {
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  font-size: calc(24 * 1px);
}
@media screen and (min-width: 320px) {
  .section-video__icon.material-icons {
    font-size: calc(24 * 1px + (32 - 24) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .section-video__icon.material-icons {
    font-size: calc(32 * 1px);
  }
}
.section-video__icon.material-icons > * {
  color: #fff !important;
}

/*!========================================================================
 * 75. Select
 * ======================================================================!*/
select {
  display: block;
  border: 1px solid rgba(128, 128, 128, 0.3);
  padding: 10px 35px 10px 15px;
  background-color: transparent;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../img/general/select_black.png");
  background-position: right 15px bottom 50%;
  background-repeat: no-repeat;
  background-size: 6px 4px;
}

/*!========================================================================
 * 76. Sidebar
 * ======================================================================!*/
.sidebar_no-margin-last-widget .widget:last-child {
  margin-bottom: 0;
}

.sidebar .search-form {
  margin-top: 0;
}

@media only screen and (max-width: 991px) {
  .sidebar {
    margin-top: 80px;
  }
}
@media only screen and (max-width: 767px) {
  .sidebar {
    margin-top: 60px;
  }
}
/*!========================================================================
 * 77. Slider
 * ======================================================================!*/
.slider__images-slide-inner {
  width: 100%;
  height: 100%;
  will-change: transform;
}

.slider__images-slide {
  overflow: hidden;
}

.slider__bg {
  width: 100%;
  height: 100%;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.slider__overlay {
  z-index: 1;
}

.slider__circle, .section__circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: auto;
  width: calc(100vw - var(--gutter-horizontal) * 4);
  height: calc(100vw - var(--gutter-horizontal) * 4);
  border: 1px solid rgba(128, 128, 128, 0.3);
  border-radius: 100%;
  z-index: 1;
  pointer-events: none;
}

.slider__heading {
  display: inline-block;
  margin-top: 0;
  margin-bottom: 0;
}

.slider__arrow_absolute {
  position: absolute;
}

.slider__arrow {
  z-index: 60;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  outline: none;
}
.slider__arrow:focus, .slider__arrow:hover {
  outline: none;
}
.slider__arrow.swiper-button-disabled {
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
}

.slider__arrow_left {
  top: 50%;
  transform: translateY(-50%);
  left: var(--gutter-horizontal);
}

.slider__arrow_right {
  top: 50%;
  transform: translateY(-50%);
  right: var(--gutter-horizontal);
}

.swiper-lazy {
  max-width: 100%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.swiper-lazy-loaded {
  opacity: 1;
  visibility: visible;
}

.slider__wrapper-canvas, .slider__wrapper-canvas-inner {
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.slider__canvas {
  position: absolute;
  display: block;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

.slider__images-slide-inner_circle {
  width: calc(100vh - var(--gutter-horizontal) * 2.5);
  height: calc(100vh - var(--gutter-horizontal) * 2.5);
  max-width: 33vw;
  max-height: 33vw;
  overflow: hidden;
  margin: auto;
}
.slider__images-slide-inner_circle .slider__bg {
  width: 100%;
  height: 100%;
}

[data-swiper-parallax-zoom="10%"] .slider__bg, [data-swiper-parallax-zoom="10%"] img {
  transform: scale(1.1);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="20%"] .slider__bg, [data-swiper-parallax-zoom="20%"] img {
  transform: scale(1.2);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="30%"] .slider__bg, [data-swiper-parallax-zoom="30%"] img {
  transform: scale(1.3);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="40%"] .slider__bg, [data-swiper-parallax-zoom="40%"] img {
  transform: scale(1.4);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="50%"] .slider__bg, [data-swiper-parallax-zoom="50%"] img {
  transform: scale(1.5);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="60%"] .slider__bg, [data-swiper-parallax-zoom="60%"] img {
  transform: scale(1.6);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="70%"] .slider__bg, [data-swiper-parallax-zoom="70%"] img {
  transform: scale(1.7);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="80%"] .slider__bg, [data-swiper-parallax-zoom="80%"] img {
  transform: scale(1.8);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="90%"] .slider__bg, [data-swiper-parallax-zoom="90%"] img {
  transform: scale(1.9);
  transform-origin: center center;
}

[data-swiper-parallax-zoom="100%"] .slider__bg, [data-swiper-parallax-zoom="100%"] img {
  transform: scale(2);
  transform-origin: center center;
}

.slider__zoom-container {
  transition: all 0.3s ease;
}

.slider__wrapper-arrows {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 155px;
  z-index: 100;
}

.slider__wrapper-arrows_right {
  right: var(--gutter-horizontal);
}

.slider__wrapper-arrows_right-mini {
  height: 105px;
  right: calc(var(--gutter-horizontal) - 11px);
}

.slider__wrapper-arrows_bottom {
  top: auto;
  transform: none;
  bottom: var(--gutter-vertical);
}

.slider_reveal .slider-fullscreen-projects__images .slider__images-slide-inner {
  opacity: 0;
  transition: all 0.3s ease;
  transform: scale(1.05);
}
.slider_reveal .slider-fullscreen-projects__images_reveal .swiper-slide .slider__images-slide-inner {
  transform: scale(1.05);
}
.slider_reveal .slider-fullscreen-projects__images_reveal .swiper-slide-active .slider__images-slide-inner {
  transform: scale(1);
}
.slider_reveal .slider-fullscreen-projects__images_reveal .slider__images-slide-inner {
  opacity: 1;
}

.slider__counter_current.slider__counter_current-huge {
  position: relative;
  top: auto;
  left: auto;
  text-align: center;
  font-size: calc(180 * 1px);
}
@media screen and (min-width: 320px) {
  .slider__counter_current.slider__counter_current-huge {
    font-size: calc(180 * 1px + (400 - 180) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .slider__counter_current.slider__counter_current-huge {
    font-size: calc(400 * 1px);
  }
}
.slider__counter_current.slider__counter_current-huge .swiper-container {
  height: calc(1 * (180 * 1px));
}
@media screen and (min-width: 320px) {
  .slider__counter_current.slider__counter_current-huge .swiper-container {
    height: calc(1 * (180 * 1px + (350 - 180) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .slider__counter_current.slider__counter_current-huge .swiper-container {
    height: calc(1 * (350 * 1px));
  }
}

.slider__text {
  max-width: 500px;
}

[data-drag-cursor] .swiper-wrapper {
  cursor: -webkit-grab;
  cursor: grab;
}

@media screen and (max-width: 991px) {
  .slider__circle, .section__circle {
    width: calc(100vw + var(--gutter-horizontal) * 2);
    height: calc(100vw + var(--gutter-horizontal) * 2);
  }
  .slider__wrapper-arrows {
    height: auto;
  }
  .slider__wrapper-arrows_right-mini {
    right: calc(var(--gutter-horizontal) - 7px);
  }
  .slider__images-slide-inner_circle {
    max-width: 50vw;
    max-height: 50vw;
  }
}
/*!========================================================================
 * 78. Slider Categories
 * ======================================================================!*/
.slider-categories {
  display: inline-flex;
  position: relative;
}
.slider-categories.text-right .slider-categories__category:not(:nth-child(1)) {
  left: auto;
  right: 0;
}

.slider-categories__category {
  display: inline-block;
  white-space: nowrap;
  line-height: 1;
}
.slider-categories__category:not(:nth-child(1)) {
  position: absolute;
  top: 0;
  left: 0;
}

@media screen and (max-width: 991px) {
  .slider-categories {
    width: 100%;
    margin-top: 1em;
    margin-bottom: 1em;
  }
  .slider-categories__category {
    width: 100%;
  }
}
@media screen and (min-width: 990px) {
  .slider-categories.text-lg-right .slider-categories__category:not(:nth-child(1)) {
    left: auto;
    right: 0;
  }
}
/*!========================================================================
 * 79. Slider Counter
 * ======================================================================!*/
.slider__wrapper-counter {
  display: inline-block;
  width: 100px;
  height: 100px;
  z-index: 60;
  pointer-events: none;
}

.slider__counter {
  font-family: var(--font-secondary);
  overflow: hidden;
  pointer-events: none;
}
.slider__counter .swiper-container {
  height: 43px;
}

.slider__counter_current {
  position: absolute;
  left: 20px;
  font-size: 48px;
  line-height: 1;
  text-align: left;
}

.slider__counter_total {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 16px;
  text-align: right;
  line-height: 1;
}

.slider__counter-divider {
  border: 1px solid rgba(128, 128, 128, 0.3);
  position: absolute;
  left: 30px;
  bottom: 0;
  transform: rotate(-45deg);
  transform-origin: left center;
  width: 100%;
  height: 1px;
}

.slider__counter_mini, .slider__total_mini {
  font-size: calc(14 * 1px);
  font-family: var(--font-secondary);
  height: 18px;
  line-height: 1;
}
@media screen and (min-width: 320px) {
  .slider__counter_mini, .slider__total_mini {
    font-size: calc(14 * 1px + (18 - 14) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .slider__counter_mini, .slider__total_mini {
    font-size: calc(18 * 1px);
  }
}

.slider__counter_mini .swiper-container {
  height: 18px;
}

@media screen and (max-width: 991px) {
  .slider__counter .swiper-container {
    height: 24px;
  }
  .slider__counter-divider {
    position: relative;
    transform: none;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
  }
  .slider__counter_current {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    font-size: 24px;
    text-align: center;
  }
  .slider__counter_total {
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    bottom: auto;
    font-size: 24px;
    text-align: center;
  }
  .slider__wrapper-counter {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 40px;
    height: 80px;
  }
}
@media screen and (max-width: 576px) {
  .slider__counter .swiper-container {
    height: 18px;
  }
  .slider__counter_current {
    font-size: 18px;
  }
  .slider__counter_total {
    font-size: 18px;
  }
  .slider__wrapper-counter {
    width: 30px;
    height: 60px;
  }
}
/*!========================================================================
 * 80. Slider Dots
 * ======================================================================!*/
.slider__dots {
  display: inline-flex;
}

.slider__dots_vertical {
  flex-direction: column;
  height: auto;
}
.slider__dots_vertical .slider__dot {
  margin: 6px 0;
}

.slider__dot {
  position: relative;
  width: 25px;
  height: 25px;
  margin: 0 6px;
  cursor: pointer;
  outline: none;
  display: inline-block;
  border-radius: 100%;
  vertical-align: middle;
  z-index: 50;
}
.slider__dot svg {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: auto;
  width: 25px;
  height: 25px;
  border-radius: 100%;
  stroke-width: 10px;
  z-index: 50;
}
.slider__dot svg .circle {
  stroke: var(--color-dark-1);
}
.slider__dot:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 100%;
  background-color: rgba(104, 104, 104, 0.5);
  transition: all 0.6s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

.slider__dot_active:after {
  background-color: var(--color-dark-1);
}

@media screen and (max-width: 767px) {
  .slider__dot {
    width: 20px;
    height: 20px;
    margin: 0 4px;
  }
  .slider__dot svg {
    width: 20px;
    height: 20px;
  }
  .slider__dots_vertical .slider__dot {
    margin: 4px 0;
  }
}
/*!========================================================================
 * 81. Slider Themes
 * ======================================================================!*/
.slider[data-arts-theme-text=dark] .slider__counter_current {
  color: #fff;
}
.slider[data-arts-theme-text=dark] .slider__counter_total {
  color: var(--color-gray-1);
}
.slider[data-arts-theme-text=dark] a {
  color: #fff;
}

/*!========================================================================
 * 82. Slider Images
 * ======================================================================!*/
.slider-images__header {
  margin-bottom: 1em;
}

.slider-images__footer {
  margin-top: 1em;
}

.slider-images_touched .slider__zoom-container {
  transform: scale(1.1);
}

@media screen and (max-width: 991px) {
  .slider-images__header {
    padding-left: 20px;
    padding-right: 20px;
  }
  .slider-images__footer {
    padding-left: 20px;
    padding-right: 20px;
  }
}
/*!========================================================================
 * 83. Slider Halfscreen Projects
 * ======================================================================!*/
.slider-halfscreen-projects {
  height: 100%;
}

.slider-halfscreen-projects_pt {
  padding-top: 180px;
}

.slider-halfscreen-projects__content {
  padding-bottom: 80px;
  padding-left: 80px;
  z-index: 100;
}

.slider-halfscreen-projects__images_reduced-width-right {
  max-width: calc(100% - var(--gutter-horizontal) - 40px);
  margin-left: 0 !important;
}

@media screen and (max-width: 991px) {
  .slider-halfscreen-projects_pt {
    padding-top: 0;
  }
  .slider-halfscreen-projects__images {
    height: 100vh;
  }
  .slider-halfscreen-projects__content {
    padding: var(--gutter-vertical) 0 !important;
    max-width: calc(100% - var(--gutter-horizontal) * 4);
    margin-left: var(--gutter-horizontal);
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
  }
  .slider-halfscreen-projects__col {
    position: static !important;
  }
  .slider-halfscreen-projects__images_reduced-width-right {
    max-width: 100% !important;
  }
}
/*!========================================================================
 * 84. Slider Fullscreen Projects
 * ======================================================================!*/
.slider-fullscreen-projects__images {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 50;
  will-change: transform;
  transition: all 0.3s ease;
}
.slider-fullscreen-projects__images[data-drag-class] {
  overflow: visible;
}

.slider-fullscreen-projects__images_scale-up {
  transform: scale(1.04) !important;
}

.slider-fullscreen-projects__images_scale-down {
  transform: scale(0.8) !important;
}
.slider-fullscreen-projects__images_scale-down .slider__images-slide-inner_circle .slider__zoom-container {
  transform: scale(1.1) !important;
}

.slider-fullscreen-projects__content {
  z-index: 60;
  color: #fff;
  margin-left: calc(var(--gutter-horizontal) * 2);
  margin-right: calc(var(--gutter-horizontal) * 2);
}

.slider-fullscreen-projects__content_reduced-sides {
  margin-left: calc(var(--gutter-horizontal));
  margin-right: calc(var(--gutter-horizontal));
}

.slider-fullscreen-projects__counter {
  position: absolute;
  right: var(--gutter-horizontal);
  bottom: 40px;
  z-index: 60;
}

.slider-fullscreen-projects__counter_centered {
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  right: auto;
  bottom: auto;
  transform: translate(-50%, -50%) !important;
  width: auto !important;
  height: auto !important;
  text-align: center;
  z-index: 1 !important;
  opacity: 0.05 !important;
}

.slider-fullscreen-projects__footer {
  position: absolute;
  bottom: 40px;
  width: auto;
  padding-left: 0;
  padding-right: 0;
  left: var(--gutter-horizontal);
  right: var(--gutter-horizontal);
  z-index: 60;
}

.slider-fullscreen-projects__footer_content {
  bottom: 45px;
}

@media screen and (max-width: 991px) {
  .slider-fullscreen-projects__content {
    max-width: calc(100% - var(--gutter-horizontal) * 4);
  }
  .slider-fullscreen-projects__content_reduced-sides {
    margin-left: var(--gutter-horizontal);
  }
  .slider-fullscreen-projects__footer {
    bottom: var(--gutter-vertical);
  }
  .slider-fullscreen-projects__counter {
    bottom: var(--gutter-vertical);
  }
}
/*!========================================================================
 * 85. Slider Testimonials
 * ======================================================================!*/
.slider-testimonials__row {
  border-top: 1px solid rgba(128, 128, 128, 0.3);
  padding-top: 1em;
}

.slider-testimonials__arrows {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 60;
}
.slider-testimonials__arrows * {
  pointer-events: initial;
}

@media screen and (max-width: 576px) {
  .slider-testimonials__arrows {
    justify-content: center;
    position: relative;
    transform: none;
    top: auto;
  }
  .slider-testimonials__arrows .slider__arrow {
    top: auto;
    transform: none;
    margin: 10px 5px 0;
  }
}
/*!========================================================================
 * 86. Social
 * ======================================================================!*/
.social {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.social__item {
  display: inline-block;
  transition: none;
}
.social__item a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  font-size: 14px;
}

/*!========================================================================
 * 87. Slider Services
 * ======================================================================!*/
.slider-services__footer {
  max-width: 1180px;
}

@media screen and (max-width: 1600px) {
  .slider-services__footer {
    max-width: 940px;
  }
}
@media screen and (max-width: 1280px) {
  .slider-services__footer {
    max-width: 840px;
  }
}
/*!========================================================================
 * 88. Spinner
 * ======================================================================!*/
.spinner {
  position: fixed;
  left: var(--gutter-horizontal);
  bottom: calc(var(--fix-bar-vh, 30px) + 15px);
  width: 30px;
  height: 30px;
  z-index: 10000;
  -webkit-animation: rotator 1.2s ease-in-out infinite;
          animation: rotator 1.2s ease-in-out infinite;
  opacity: 0;
  visibility: hidden;
}

@-webkit-keyframes rotator {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(270deg);
  }
}

@keyframes rotator {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(270deg);
  }
}
.spinner__path {
  stroke: var(--color-gray-1);
  stroke-dasharray: 202;
  stroke-dashoffset: 0;
  transform-origin: center;
  -webkit-animation: dash 1.2s ease-in-out infinite;
          animation: dash 1.2s ease-in-out infinite;
}

@-webkit-keyframes dash {
  0% {
    stroke-dashoffset: 202;
    transform: rotate(0deg);
  }
  50% {
    stroke-dashoffset: 50.5;
    transform: rotate(135deg);
  }
  100% {
    stroke-dashoffset: 202;
    transform: rotate(450deg);
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 202;
    transform: rotate(0deg);
  }
  50% {
    stroke-dashoffset: 50.5;
    transform: rotate(135deg);
  }
  100% {
    stroke-dashoffset: 202;
    transform: rotate(450deg);
  }
}
/*!========================================================================
 * 89. Svg-rectangle
 * ======================================================================!*/
.svg-rectangle {
  fill: #fff;
}

.svg-rectangle__normal {
  visibility: hidden;
}

/*!========================================================================
 * 90. Tags
 * ======================================================================!*/
.tagcloud a, .widget .tagcloud a {
  display: inline-block;
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  background-color: transparent;
  padding: 8px 14px;
  margin-bottom: 6px;
  margin-right: 4px;
  border-radius: 100px;
  border: 1px solid rgba(128, 128, 128, 0.3);
}
@media screen and (min-width: 320px) {
  .tagcloud a, .widget .tagcloud a {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .tagcloud a, .widget .tagcloud a {
    font-size: calc(13 * 1px);
  }
}
.tagcloud a:hover, .widget .tagcloud a:hover {
  border-color: var(--color-dark-1);
}
.tagcloud ul, .widget .tagcloud ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.tagcloud ul li, .widget .tagcloud ul li {
  display: inline-block;
  margin-bottom: 0;
}

/*!========================================================================
 * 91. Themes
 * ======================================================================!*/
[data-arts-theme-text=light] {
  color: #fff;
}
[data-arts-theme-text=light] .xl {
  color: var(--xl-color-light);
}
[data-arts-theme-text=light] h1, [data-arts-theme-text=light] .h1 {
  color: var(--h1-color-light);
}
[data-arts-theme-text=light] h2, [data-arts-theme-text=light] .h2 {
  color: var(--h2-color-light);
}
[data-arts-theme-text=light] h3, [data-arts-theme-text=light] .h3 {
  color: var(--h3-color-light);
}
[data-arts-theme-text=light] h4, [data-arts-theme-text=light] .h4 {
  color: var(--h4-color-light);
}
[data-arts-theme-text=light] h5, [data-arts-theme-text=light] .h5 {
  color: var(--h5-color-light);
}
[data-arts-theme-text=light] h6, [data-arts-theme-text=light] .h6 {
  color: var(--h6-color-light);
}
[data-arts-theme-text=light] p, [data-arts-theme-text=light] .paragraph {
  color: var(--paragraph-color-light);
}
[data-arts-theme-text=light] .figure-post__date {
  background-color: #181818;
}
[data-arts-theme-text=light] .post blockquote:before {
  content: url("../img/general/quote-white.svg");
}
[data-arts-theme-text=light] blockquote, [data-arts-theme-text=light] .blockquote {
  color: var(--blockquote-color-light);
}
[data-arts-theme-text=light] blockquote p, [data-arts-theme-text=light] .blockquote p {
  color: var(--blockquote-color-light);
}
[data-arts-theme-text=light] blockquote cite, [data-arts-theme-text=light] .blockquote cite {
  color: var(--blockquote-color-light);
}
[data-arts-theme-text=light] .has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter, [data-arts-theme-text=light] .drop-cap:first-letter {
  color: var(--dropcap-color-light);
}
[data-arts-theme-text=light] .split-text:not(.js-split-text) .has-drop-cap > div:first-child {
  color: var(--dropcap-color-light);
}
[data-arts-theme-text=light] .post__tags {
  border-color: var(--color-gray-1);
}
[data-arts-theme-text=light] .tagcloud a:hover, [data-arts-theme-text=light] .widget .tagcloud a:hover {
  border-color: #fff;
}
[data-arts-theme-text=light] .input-float__input_focused {
  border-color: #fff;
}
[data-arts-theme-text=light] .input-float__input_focused + .input-float__label {
  color: #fff;
}
[data-arts-theme-text=light] a {
  color: var(--color-gray-1);
}
[data-arts-theme-text=light] a:hover {
  color: #fff;
}
[data-arts-theme-text=light] a.hover-zoom:hover .hover-zoom__caption {
  color: #fff;
}
[data-arts-theme-text=light] .change-text-hover__line {
  background-color: var(--color-gray-1);
}
[data-arts-theme-text=light] .change-text-hover__normal {
  color: var(--color-gray-1);
}
[data-arts-theme-text=light] .change-text-hover__hover {
  color: #fff;
}
[data-arts-theme-text=light] .arrow:hover .arrow__pointer {
  background-color: #fff;
}
[data-arts-theme-text=light] .arrow:hover .circle {
  stroke: #fff;
}
[data-arts-theme-text=light] .arrow:hover .arrow__triangle {
  border-color: transparent transparent transparent #fff;
}
[data-arts-theme-text=light] .filter__item {
  color: #fff;
  opacity: 0.3;
}
[data-arts-theme-text=light] .filter__item:hover {
  color: #fff;
  opacity: 1;
}
[data-arts-theme-text=light] .filter__item_active {
  opacity: 1;
}
[data-arts-theme-text=light] .filter__underline {
  background-color: #fff;
}
[data-arts-theme-text=light] .figure-project__heading {
  color: #fff;
}
[data-arts-theme-text=light] .slider__dot svg .circle {
  stroke: #fff;
}
[data-arts-theme-text=light] .slider__dot {
  border-color: var(--color-dark-4);
}
[data-arts-theme-text=light] .slider__dot:after {
  background-color: var(--color-dark-4);
}
[data-arts-theme-text=light] .slider__dot_active:after {
  background-color: #fff;
}
[data-arts-theme-text=light] .widgettitle {
  color: #fff;
}
[data-arts-theme-text=light] .circle-button__inner .svg-circle {
  border: 1px solid rgba(255, 255, 255, 0.3);
}
[data-arts-theme-text=light] .circle-button__inner .circle-button__icon {
  fill: #fff;
}
[data-arts-theme-text=light] .circle-button__circle:hover .svg-circle {
  border-color: rgb(255, 255, 255);
}
[data-arts-theme-text=light] .section-nav-projects__link {
  color: #fff;
}
[data-arts-theme-text=light] .section-nav-projects__link:hover {
  opacity: 1 !important;
  color: var(--color-gray-2) !important;
}
[data-arts-theme-text=light] .figure-icon__wrapper-icon:hover {
  border-color: #fff;
}

/*!========================================================================
 * 92. Transition Curtain
 * ======================================================================!*/
.transition-curtain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  transform: translateY(100%);
  margin: auto;
  z-index: 100;
  overflow: hidden;
}

/*!========================================================================
 * 93. Image Alignment
 * ======================================================================!*/
.alignnone {
  margin: 1em 0;
}

.aligncenter, div.aligncenter {
  display: block;
  margin: 1.5em auto 1.5em auto;
}

.alignright {
  float: right;
  margin: 0.5em 0 0.5em 1.5em;
}

.alignleft {
  float: left;
  margin: 0.5em 1.5em 0.5em 0;
}

a img.alignright {
  float: right;
  margin: 5px 0 24px 24px;
}
a img.alignnone {
  margin: 5px 24px 24px 0;
}
a img.alignleft {
  float: left;
  margin: 5px 24px 24px 0;
}
a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.wp-block-image {
  margin-top: 2em;
  margin-bottom: 2em;
}
.wp-block-image figcaption {
  margin-top: 1em;
  margin-bottom: 1em;
}

.wp-caption {
  max-width: 100%;
  text-align: center;
}
.wp-caption.alignnone, .wp-caption.alignleft {
  margin: 0.25em 1.5em 1.5em 0;
}
.wp-caption.alignright {
  margin: 0.25em 0 1.5em 1.5em;
}
.wp-caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 100%;
  padding: 0;
  width: auto;
}
.wp-caption p.wp-caption-text {
  font-size: 11px;
  line-height: 17px;
  margin: 0;
  padding: 0 0.25em 0.25em;
}

.wp-caption-text {
  margin-top: 0.5em;
}

.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
}
.screen-reader-text:focus {
  background-color: var(--color-gray-2);
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #21759b;
  display: block;
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
}

.bypostauthor {
  display: block;
}

/*!========================================================================
 * 94. Typography
 * ======================================================================!*/
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, .xl {
  font-family: var(--font-secondary);
}

h1, h2, h3 {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

h4, h5, h6 {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.xl {
  font-size: calc(var(--xl-min-font-size) * 1px);
  font-weight: normal;
  line-height: var(--xl-line-height);
  color: var(--xxl-color-dark);
}
@media screen and (min-width: 320px) {
  .xl {
    font-size: calc(var(--xl-min-font-size) * 1px + (var(--xl-max-font-size) - var(--xl-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .xl {
    font-size: calc(var(--xl-max-font-size) * 1px);
  }
}

h1, .h1 {
  font-size: calc(var(--h1-min-font-size) * 1px);
  font-weight: normal;
  line-height: var(--h1-line-height);
  color: var(--h1-color-dark);
}
@media screen and (min-width: 320px) {
  h1, .h1 {
    font-size: calc(var(--h1-min-font-size) * 1px + (var(--h1-max-font-size) - var(--h1-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h1, .h1 {
    font-size: calc(var(--h1-max-font-size) * 1px);
  }
}

h2, .h2 {
  font-size: calc(var(--h2-min-font-size) * 1px);
  font-weight: normal;
  line-height: var(--h2-line-height);
  color: var(--h2-color-dark);
}
@media screen and (min-width: 320px) {
  h2, .h2 {
    font-size: calc(var(--h2-min-font-size) * 1px + (var(--h2-max-font-size) - var(--h2-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h2, .h2 {
    font-size: calc(var(--h2-max-font-size) * 1px);
  }
}

h3, .h3 {
  font-size: calc(var(--h3-min-font-size) * 1px);
  font-weight: normal;
  line-height: var(--h3-line-height);
  color: var(--h3-color-dark);
}
@media screen and (min-width: 320px) {
  h3, .h3 {
    font-size: calc(var(--h3-min-font-size) * 1px + (var(--h3-max-font-size) - var(--h3-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h3, .h3 {
    font-size: calc(var(--h3-max-font-size) * 1px);
  }
}

h4, .h4 {
  font-size: calc(var(--h4-min-font-size) * 1px);
  font-weight: bold;
  line-height: var(--h4-line-height);
  color: var(--h4-color-dark);
}
@media screen and (min-width: 320px) {
  h4, .h4 {
    font-size: calc(var(--h4-min-font-size) * 1px + (var(--h4-max-font-size) - var(--h4-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h4, .h4 {
    font-size: calc(var(--h4-max-font-size) * 1px);
  }
}

h5, .h5 {
  font-size: calc(var(--h5-min-font-size) * 1px);
  font-weight: bold;
  line-height: var(--h5-line-height);
  color: var(--h5-color-dark);
}
@media screen and (min-width: 320px) {
  h5, .h5 {
    font-size: calc(var(--h5-min-font-size) * 1px + (var(--h5-max-font-size) - var(--h5-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h5, .h5 {
    font-size: calc(var(--h5-max-font-size) * 1px);
  }
}

h6, .h6 {
  font-size: calc(var(--h6-min-font-size) * 1px);
  font-weight: bold;
  line-height: var(--h6-line-height);
  color: var(--h6-color-dark);
}
@media screen and (min-width: 320px) {
  h6, .h6 {
    font-size: calc(var(--h6-min-font-size) * 1px + (var(--h6-max-font-size) - var(--h6-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  h6, .h6 {
    font-size: calc(var(--h6-max-font-size) * 1px);
  }
}

p {
  margin-top: 1em;
  margin-bottom: 1em;
  font-size: calc(var(--paragraph-min-font-size) * 1px);
  line-height: var(--paragraph-line-height);
  color: var(--paragraph-color-dark);
}
@media screen and (min-width: 320px) {
  p {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  p {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}

.paragraph {
  font-size: calc(var(--paragraph-min-font-size) * 1px);
  line-height: var(--paragraph-line-height);
  color: var(--paragraph-color-dark);
}
@media screen and (min-width: 320px) {
  .paragraph {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .paragraph {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}

blockquote, .blockquote {
  display: block;
  font-size: calc(var(--blockquote-min-font-size) * 1px);
  line-height: var(--blockquote-line-height);
  font-style: italic;
  margin-top: 2em;
  margin-bottom: 2em;
  color: var(--blockquote-color-dark);
}
@media screen and (min-width: 320px) {
  blockquote, .blockquote {
    font-size: calc(var(--blockquote-min-font-size) * 1px + (var(--blockquote-max-font-size) - var(--blockquote-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  blockquote, .blockquote {
    font-size: calc(var(--blockquote-max-font-size) * 1px);
  }
}
blockquote p, .blockquote p {
  margin-top: 0;
  font-size: calc(var(--blockquote-min-font-size) * 1px);
  line-height: var(--blockquote-line-height);
}
@media screen and (min-width: 320px) {
  blockquote p, .blockquote p {
    font-size: calc(var(--blockquote-min-font-size) * 1px + (var(--blockquote-max-font-size) - var(--blockquote-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  blockquote p, .blockquote p {
    font-size: calc(var(--blockquote-max-font-size) * 1px);
  }
}
blockquote p:last-child, .blockquote p:last-child {
  margin-bottom: 0;
}
blockquote cite, .blockquote cite {
  font-size: calc(var(--paragraph-min-font-size) * 1px);
  font-style: normal;
  color: var(--blockquote-color-dark);
}
@media screen and (min-width: 320px) {
  blockquote cite, .blockquote cite {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  blockquote cite, .blockquote cite {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}

strong, b, .strong {
  font-weight: bold;
}

em, i, .em {
  font-style: italic;
}

small, .small {
  display: block;
  font-size: 14px;
}

.small-caps {
  font-size: calc(10 * 1px);
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1.3;
  text-transform: uppercase;
  color: var(--color-gray-1);
}
@media screen and (min-width: 320px) {
  .small-caps {
    font-size: calc(10 * 1px + (13 - 10) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .small-caps {
    font-size: calc(13 * 1px);
  }
}

.has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter {
  float: left;
  font-family: var(--font-secondary);
  font-size: calc(var(--dropcap-min-font-size) * 1px);
  color: var(--dropcap-color-dark);
  line-height: var(--dropcap-line-height);
  text-transform: uppercase;
  font-style: normal;
  margin: 0.15em 0.25em 0 0;
}
@media screen and (min-width: 320px) {
  .has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter {
    font-size: calc(var(--dropcap-min-font-size) * 1px + (var(--dropcap-max-font-size) - var(--dropcap-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter {
    font-size: calc(var(--dropcap-max-font-size) * 1px);
  }
}
.has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter:after {
  content: "";
  display: table;
  clear: both;
}
.has-drop-cap:not(:focus):not(.has-drop-cap_split):first-letter:after {
  content: "";
  display: table;
  clear: both;
}

.split-text:not(.js-split-text) .has-drop-cap > div:first-child {
  display: inline-block !important;
  float: left;
}
.split-text:not(.js-split-text) .has-drop-cap > div:first-child:after {
  content: "";
  display: table;
  clear: both;
}

.drop-cap {
  float: left;
  font-family: var(--font-secondary);
  font-size: calc(var(--dropcap-min-font-size) * 1px);
  color: var(--dropcap-color-dark);
  line-height: var(--dropcap-line-height);
  text-transform: uppercase;
  font-style: normal;
  margin: 0.15em 0.25em 0 0;
}
@media screen and (min-width: 320px) {
  .drop-cap {
    font-size: calc(var(--dropcap-min-font-size) * 1px + (var(--dropcap-max-font-size) - var(--dropcap-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .drop-cap {
    font-size: calc(var(--dropcap-max-font-size) * 1px);
  }
}
.drop-cap:after {
  content: "";
  display: table;
  clear: both;
}

@media screen and (max-width: 767px) {
  blockquote, .blockquote {
    font-size: calc(var(--paragraph-min-font-size) * 1px);
    line-height: var(--paragraph-line-height);
  }
}
@media screen and (max-width: 767px) and (min-width: 320px) {
  blockquote, .blockquote {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (max-width: 767px) and (min-width: 2560px) {
  blockquote, .blockquote {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}
@media screen and (max-width: 767px) {
  blockquote p, .blockquote p {
    font-size: calc(var(--paragraph-min-font-size) * 1px);
    line-height: var(--paragraph-line-height);
  }
}
@media screen and (max-width: 767px) and (min-width: 320px) {
  blockquote p, .blockquote p {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (max-width: 767px) and (min-width: 2560px) {
  blockquote p, .blockquote p {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}
/*!========================================================================
 * 95. Fluid Margins
 * ======================================================================!*/
.m-xsmall {
  margin: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .m-xsmall {
    margin: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .m-xsmall {
    margin: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.m-small {
  margin: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .m-small {
    margin: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .m-small {
    margin: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.m-medium {
  margin: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .m-medium {
    margin: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .m-medium {
    margin: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.m-large {
  margin: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .m-large {
    margin: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .m-large {
    margin: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.m-xlarge {
  margin: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .m-xlarge {
    margin: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .m-xlarge {
    margin: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.my-xsmall {
  margin-top: calc(1 * (var(--distance-min-xsmall) * 1px));
  margin-bottom: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .my-xsmall {
    margin-top: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-xsmall {
    margin-top: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .my-xsmall {
    margin-bottom: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-xsmall {
    margin-bottom: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.my-small {
  margin-top: calc(1 * (var(--distance-min-small) * 1px));
  margin-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .my-small {
    margin-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-small {
    margin-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .my-small {
    margin-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-small {
    margin-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.my-medium {
  margin-top: calc(1 * (var(--distance-min-medium) * 1px));
  margin-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .my-medium {
    margin-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-medium {
    margin-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .my-medium {
    margin-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-medium {
    margin-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.my-large {
  margin-top: calc(1 * (var(--distance-min-large) * 1px));
  margin-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .my-large {
    margin-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-large {
    margin-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .my-large {
    margin-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-large {
    margin-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.my-xlarge {
  margin-top: calc(1 * (var(--distance-min-xlarge) * 1px));
  margin-bottom: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .my-xlarge {
    margin-top: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-xlarge {
    margin-top: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .my-xlarge {
    margin-bottom: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .my-xlarge {
    margin-bottom: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mx-xsmall {
  margin-left: calc(1 * (var(--distance-min-xsmall) * 1px));
  margin-right: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mx-xsmall {
    margin-left: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-xsmall {
    margin-left: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .mx-xsmall {
    margin-right: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-xsmall {
    margin-right: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mx-small {
  margin-left: calc(1 * (var(--distance-min-small) * 1px));
  margin-right: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mx-small {
    margin-left: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-small {
    margin-left: calc(1 * (var(--distance-max-small) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .mx-small {
    margin-right: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-small {
    margin-right: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.mx-medium {
  margin-left: calc(1 * (var(--distance-min-medium) * 1px));
  margin-right: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mx-medium {
    margin-left: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-medium {
    margin-left: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .mx-medium {
    margin-right: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-medium {
    margin-right: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.mx-large {
  margin-left: calc(1 * (var(--distance-min-large) * 1px));
  margin-right: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mx-large {
    margin-left: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-large {
    margin-left: calc(1 * (var(--distance-max-large) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .mx-large {
    margin-right: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-large {
    margin-right: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.mx-xlarge {
  margin-left: calc(1 * (var(--distance-min-xlarge) * 1px));
  margin-right: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mx-xlarge {
    margin-left: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-xlarge {
    margin-left: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .mx-xlarge {
    margin-right: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mx-xlarge {
    margin-right: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mt-xsmall {
  margin-top: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-xsmall {
    margin-top: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-xsmall {
    margin-top: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mt-small {
  margin-top: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-small {
    margin-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-small {
    margin-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.mt-medium {
  margin-top: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-medium {
    margin-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-medium {
    margin-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.mt-large {
  margin-top: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-large {
    margin-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-large {
    margin-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.mt-xlarge {
  margin-top: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-xlarge {
    margin-top: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-xlarge {
    margin-top: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mr-xsmall {
  margin-right: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-xsmall {
    margin-right: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-xsmall {
    margin-right: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mr-small {
  margin-right: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-small {
    margin-right: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-small {
    margin-right: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.mr-medium {
  margin-right: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-medium {
    margin-right: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-medium {
    margin-right: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.mr-large {
  margin-right: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-large {
    margin-right: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-large {
    margin-right: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.mr-xlarge {
  margin-right: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-xlarge {
    margin-right: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-xlarge {
    margin-right: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mb-xsmall {
  margin-bottom: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-xsmall {
    margin-bottom: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-xsmall {
    margin-bottom: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mb-small {
  margin-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-small {
    margin-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-small {
    margin-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.mb-medium {
  margin-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-medium {
    margin-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-medium {
    margin-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.mb-large {
  margin-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-large {
    margin-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-large {
    margin-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.mb-xlarge {
  margin-bottom: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-xlarge {
    margin-bottom: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-xlarge {
    margin-bottom: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.ml-xsmall {
  margin-left: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-xsmall {
    margin-left: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-xsmall {
    margin-left: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.ml-small {
  margin-left: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-small {
    margin-left: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-small {
    margin-left: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.ml-medium {
  margin-left: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-medium {
    margin-left: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-medium {
    margin-left: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.ml-large {
  margin-left: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-large {
    margin-left: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-large {
    margin-left: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.ml-xlarge {
  margin-left: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-xlarge {
    margin-left: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-xlarge {
    margin-left: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

/*!========================================================================
 * 96. Fluid Margins Negative
 * ======================================================================!*/
.mt-minus-xsmall {
  margin-top: calc(-1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-minus-xsmall {
    margin-top: calc(-1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-minus-xsmall {
    margin-top: calc(-1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mt-minus-small {
  margin-top: calc(-1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-minus-small {
    margin-top: calc(-1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-minus-small {
    margin-top: calc(-1 * (var(--distance-max-small) * 1px));
  }
}

.mt-minus-medium {
  margin-top: calc(-1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-minus-medium {
    margin-top: calc(-1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-minus-medium {
    margin-top: calc(-1 * (var(--distance-max-normal) * 1px));
  }
}

.mt-minus-large {
  margin-top: calc(-1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-minus-large {
    margin-top: calc(-1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-minus-large {
    margin-top: calc(-1 * (var(--distance-max-large) * 1px));
  }
}

.mt-minus-xlarge {
  margin-top: calc(-1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mt-minus-xlarge {
    margin-top: calc(-1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mt-minus-xlarge {
    margin-top: calc(-1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mr-minus-xsmall {
  margin-right: calc(-1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-minus-xsmall {
    margin-right: calc(-1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-minus-xsmall {
    margin-right: calc(-1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mr-minus-small {
  margin-right: calc(-1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-minus-small {
    margin-right: calc(-1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-minus-small {
    margin-right: calc(-1 * (var(--distance-max-small) * 1px));
  }
}

.mr-minus-medium {
  margin-right: calc(-1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-minus-medium {
    margin-right: calc(-1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-minus-medium {
    margin-right: calc(-1 * (var(--distance-max-normal) * 1px));
  }
}

.mr-minus-large {
  margin-right: calc(-1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-minus-large {
    margin-right: calc(-1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-minus-large {
    margin-right: calc(-1 * (var(--distance-max-large) * 1px));
  }
}

.mr-minus-xlarge {
  margin-right: calc(-1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mr-minus-xlarge {
    margin-right: calc(-1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mr-minus-xlarge {
    margin-right: calc(-1 * (var(--distance-max-xlarge) * 1px));
  }
}

.mb-minus-xsmall {
  margin-bottom: calc(-1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-minus-xsmall {
    margin-bottom: calc(-1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-minus-xsmall {
    margin-bottom: calc(-1 * (var(--distance-max-xsmall) * 1px));
  }
}

.mb-minus-small {
  margin-bottom: calc(-1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-minus-small {
    margin-bottom: calc(-1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-minus-small {
    margin-bottom: calc(-1 * (var(--distance-max-small) * 1px));
  }
}

.mb-minus-medium {
  margin-bottom: calc(-1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-minus-medium {
    margin-bottom: calc(-1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-minus-medium {
    margin-bottom: calc(-1 * (var(--distance-max-normal) * 1px));
  }
}

.mb-minus-large {
  margin-bottom: calc(-1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-minus-large {
    margin-bottom: calc(-1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-minus-large {
    margin-bottom: calc(-1 * (var(--distance-max-large) * 1px));
  }
}

.mb-minus-xlarge {
  margin-bottom: calc(-1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .mb-minus-xlarge {
    margin-bottom: calc(-1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .mb-minus-xlarge {
    margin-bottom: calc(-1 * (var(--distance-max-xlarge) * 1px));
  }
}

.ml-minus-xsmall {
  margin-left: calc(-1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-minus-xsmall {
    margin-left: calc(-1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-minus-xsmall {
    margin-left: calc(-1 * (var(--distance-max-xsmall) * 1px));
  }
}

.ml-minus-small {
  margin-left: calc(-1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-minus-small {
    margin-left: calc(-1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-minus-small {
    margin-left: calc(-1 * (var(--distance-max-small) * 1px));
  }
}

.ml-minus-medium {
  margin-left: calc(-1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-minus-medium {
    margin-left: calc(-1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-minus-medium {
    margin-left: calc(-1 * (var(--distance-max-normal) * 1px));
  }
}

.ml-minus-large {
  margin-left: calc(-1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-minus-large {
    margin-left: calc(-1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-minus-large {
    margin-left: calc(-1 * (var(--distance-max-large) * 1px));
  }
}

.ml-minus-xlarge {
  margin-left: calc(-1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .ml-minus-xlarge {
    margin-left: calc(-1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .ml-minus-xlarge {
    margin-left: calc(-1 * (var(--distance-max-xlarge) * 1px));
  }
}

/*!========================================================================
 * 97. Fluid Paddings
 * ======================================================================!*/
.p-xsmall {
  padding: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .p-xsmall {
    padding: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .p-xsmall {
    padding: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.p-small {
  padding: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .p-small {
    padding: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .p-small {
    padding: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.p-medium {
  padding: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .p-medium {
    padding: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .p-medium {
    padding: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.p-large {
  padding: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .p-large {
    padding: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .p-large {
    padding: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.p-xlarge {
  padding: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .p-xlarge {
    padding: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .p-xlarge {
    padding: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.py-xsmall {
  padding-top: calc(1 * (var(--distance-min-xsmall) * 1px));
  padding-bottom: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .py-xsmall {
    padding-top: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-xsmall {
    padding-top: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .py-xsmall {
    padding-bottom: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-xsmall {
    padding-bottom: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.py-small {
  padding-top: calc(1 * (var(--distance-min-small) * 1px));
  padding-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .py-small {
    padding-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-small {
    padding-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .py-small {
    padding-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-small {
    padding-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.py-medium {
  padding-top: calc(1 * (var(--distance-min-medium) * 1px));
  padding-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .py-medium {
    padding-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-medium {
    padding-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .py-medium {
    padding-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-medium {
    padding-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.py-large {
  padding-top: calc(1 * (var(--distance-min-large) * 1px));
  padding-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .py-large {
    padding-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-large {
    padding-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .py-large {
    padding-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-large {
    padding-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.py-xlarge {
  padding-top: calc(1 * (var(--distance-min-xlarge) * 1px));
  padding-bottom: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .py-xlarge {
    padding-top: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-xlarge {
    padding-top: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .py-xlarge {
    padding-bottom: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .py-xlarge {
    padding-bottom: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.px-xsmall {
  padding-left: calc(1 * (var(--distance-min-xsmall) * 1px));
  padding-right: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .px-xsmall {
    padding-left: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-xsmall {
    padding-left: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .px-xsmall {
    padding-right: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-xsmall {
    padding-right: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.px-small {
  padding-left: calc(1 * (var(--distance-min-small) * 1px));
  padding-right: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .px-small {
    padding-left: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-small {
    padding-left: calc(1 * (var(--distance-max-small) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .px-small {
    padding-right: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-small {
    padding-right: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.px-medium {
  padding-left: calc(1 * (var(--distance-min-medium) * 1px));
  padding-right: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .px-medium {
    padding-left: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-medium {
    padding-left: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .px-medium {
    padding-right: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-medium {
    padding-right: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.px-large {
  padding-left: calc(1 * (var(--distance-min-large) * 1px));
  padding-right: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .px-large {
    padding-left: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-large {
    padding-left: calc(1 * (var(--distance-max-large) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .px-large {
    padding-right: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-large {
    padding-right: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.px-xlarge {
  padding-left: calc(1 * (var(--distance-min-xlarge) * 1px));
  padding-right: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .px-xlarge {
    padding-left: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-xlarge {
    padding-left: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}
@media screen and (min-width: 320px) {
  .px-xlarge {
    padding-right: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .px-xlarge {
    padding-right: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.pt-xsmall {
  padding-top: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-xsmall {
    padding-top: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-xsmall {
    padding-top: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.pt-small {
  padding-top: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-small {
    padding-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-small {
    padding-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.pt-medium {
  padding-top: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-medium {
    padding-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-medium {
    padding-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.pt-large {
  padding-top: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-large {
    padding-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-large {
    padding-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pt-xlarge {
  padding-top: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-xlarge {
    padding-top: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-xlarge {
    padding-top: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.pr-xsmall {
  padding-right: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .pr-xsmall {
    padding-right: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pr-xsmall {
    padding-right: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.pr-small {
  padding-right: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pr-small {
    padding-right: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pr-small {
    padding-right: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.pr-medium {
  padding-right: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pr-medium {
    padding-right: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pr-medium {
    padding-right: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.pr-large {
  padding-right: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pr-large {
    padding-right: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pr-large {
    padding-right: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pr-xlarge {
  padding-right: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .pr-xlarge {
    padding-right: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pr-xlarge {
    padding-right: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.pb-xsmall {
  padding-bottom: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-xsmall {
    padding-bottom: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-xsmall {
    padding-bottom: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.pb-small {
  padding-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-small {
    padding-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-small {
    padding-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.pb-medium {
  padding-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-medium {
    padding-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-medium {
    padding-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.pb-large {
  padding-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-large {
    padding-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-large {
    padding-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pb-xlarge {
  padding-bottom: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-xlarge {
    padding-bottom: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-xlarge {
    padding-bottom: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

.pl-xsmall {
  padding-left: calc(1 * (var(--distance-min-xsmall) * 1px));
}
@media screen and (min-width: 320px) {
  .pl-xsmall {
    padding-left: calc(1 * (var(--distance-min-xsmall) * 1px + (var(--distance-max-xsmall) - var(--distance-min-xsmall)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pl-xsmall {
    padding-left: calc(1 * (var(--distance-max-xsmall) * 1px));
  }
}

.pl-small {
  padding-left: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pl-small {
    padding-left: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pl-small {
    padding-left: calc(1 * (var(--distance-max-small) * 1px));
  }
}

.pl-medium {
  padding-left: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pl-medium {
    padding-left: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pl-medium {
    padding-left: calc(1 * (var(--distance-max-normal) * 1px));
  }
}

.pl-large {
  padding-left: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pl-large {
    padding-left: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pl-large {
    padding-left: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pl-xlarge {
  padding-left: calc(1 * (var(--distance-min-xlarge) * 1px));
}
@media screen and (min-width: 320px) {
  .pl-xlarge {
    padding-left: calc(1 * (var(--distance-min-xlarge) * 1px + (var(--distance-max-xlarge) - var(--distance-min-xlarge)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pl-xlarge {
    padding-left: calc(1 * (var(--distance-max-xlarge) * 1px));
  }
}

/*!========================================================================
 * 98. Margins
 * ======================================================================!*/
.m-0 {
  margin: 0em;
}

.mt-0 {
  margin-top: 0em;
}

.mr-0 {
  margin-right: 0em;
}

.mb-0 {
  margin-bottom: 0em;
}

.ml-0 {
  margin-left: 0em;
}

.my-0 {
  margin-top: 0em;
  margin-bottom: 0em;
}

.mx-0 {
  margin-left: 0em;
  margin-right: 0em;
}

.m-minus-0 {
  margin: -0em;
}

.mt-minus-0 {
  margin-top: -0em;
}

.mr-minus-0 {
  margin-right: -0em;
}

.mb-minus-0 {
  margin-bottom: -0em;
}

.ml-minus-0 {
  margin-left: -0em;
}

.my-minus-0 {
  margin-top: -0em;
  margin-bottom: -0em;
}

.mx-minus-0 {
  margin-left: -0em;
  margin-right: -0em;
}

@media screen and (min-width: 768px) {
  .m-sm-0 {
    margin: 0em;
  }
  .mt-sm-0 {
    margin-top: 0em;
  }
  .mr-sm-0 {
    margin-right: 0em;
  }
  .mb-sm-0 {
    margin-bottom: 0em;
  }
  .ml-sm-0 {
    margin-left: 0em;
  }
  .my-sm-0 {
    margin-top: 0em;
    margin-bottom: 0em;
  }
  .mx-sm-0 {
    margin-left: 0em;
    margin-right: 0em;
  }
  .m-sm-minus-0 {
    margin: -0em;
  }
  .mt-sm-minus-0 {
    margin-top: -0em;
  }
  .mr-sm-minus-0 {
    margin-right: -0em;
  }
  .mb-sm-minus-0 {
    margin-bottom: -0em;
  }
  .ml-sm-minus-0 {
    margin-left: -0em;
  }
  .my-sm-minus-0 {
    margin-top: -0em;
    margin-bottom: -0em;
  }
  .mx-sm-minus-0 {
    margin-left: -0em;
    margin-right: -0em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-0 {
    margin: 0em;
  }
  .mt-md-0 {
    margin-top: 0em;
  }
  .mr-md-0 {
    margin-right: 0em;
  }
  .mb-md-0 {
    margin-bottom: 0em;
  }
  .ml-md-0 {
    margin-left: 0em;
  }
  .my-md-0 {
    margin-top: 0em;
    margin-bottom: 0em;
  }
  .mx-md-0 {
    margin-left: 0em;
    margin-right: 0em;
  }
  .m-md-minus-0 {
    margin: -0em;
  }
  .mt-md-minus-0 {
    margin-top: -0em;
  }
  .mr-md-minus-0 {
    margin-right: -0em;
  }
  .mb-md-minus-0 {
    margin-bottom: -0em;
  }
  .ml-md-minus-0 {
    margin-left: -0em;
  }
  .my-md-minus-0 {
    margin-top: -0em;
    margin-bottom: -0em;
  }
  .mx-md-minus-0 {
    margin-left: -0em;
    margin-right: -0em;
  }
}
.m-1 {
  margin: 1em;
}

.mt-1 {
  margin-top: 1em;
}

.mr-1 {
  margin-right: 1em;
}

.mb-1 {
  margin-bottom: 1em;
}

.ml-1 {
  margin-left: 1em;
}

.my-1 {
  margin-top: 1em;
  margin-bottom: 1em;
}

.mx-1 {
  margin-left: 1em;
  margin-right: 1em;
}

.m-minus-1 {
  margin: -1em;
}

.mt-minus-1 {
  margin-top: -1em;
}

.mr-minus-1 {
  margin-right: -1em;
}

.mb-minus-1 {
  margin-bottom: -1em;
}

.ml-minus-1 {
  margin-left: -1em;
}

.my-minus-1 {
  margin-top: -1em;
  margin-bottom: -1em;
}

.mx-minus-1 {
  margin-left: -1em;
  margin-right: -1em;
}

@media screen and (min-width: 768px) {
  .m-sm-1 {
    margin: 1em;
  }
  .mt-sm-1 {
    margin-top: 1em;
  }
  .mr-sm-1 {
    margin-right: 1em;
  }
  .mb-sm-1 {
    margin-bottom: 1em;
  }
  .ml-sm-1 {
    margin-left: 1em;
  }
  .my-sm-1 {
    margin-top: 1em;
    margin-bottom: 1em;
  }
  .mx-sm-1 {
    margin-left: 1em;
    margin-right: 1em;
  }
  .m-sm-minus-1 {
    margin: -1em;
  }
  .mt-sm-minus-1 {
    margin-top: -1em;
  }
  .mr-sm-minus-1 {
    margin-right: -1em;
  }
  .mb-sm-minus-1 {
    margin-bottom: -1em;
  }
  .ml-sm-minus-1 {
    margin-left: -1em;
  }
  .my-sm-minus-1 {
    margin-top: -1em;
    margin-bottom: -1em;
  }
  .mx-sm-minus-1 {
    margin-left: -1em;
    margin-right: -1em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-1 {
    margin: 1em;
  }
  .mt-md-1 {
    margin-top: 1em;
  }
  .mr-md-1 {
    margin-right: 1em;
  }
  .mb-md-1 {
    margin-bottom: 1em;
  }
  .ml-md-1 {
    margin-left: 1em;
  }
  .my-md-1 {
    margin-top: 1em;
    margin-bottom: 1em;
  }
  .mx-md-1 {
    margin-left: 1em;
    margin-right: 1em;
  }
  .m-md-minus-1 {
    margin: -1em;
  }
  .mt-md-minus-1 {
    margin-top: -1em;
  }
  .mr-md-minus-1 {
    margin-right: -1em;
  }
  .mb-md-minus-1 {
    margin-bottom: -1em;
  }
  .ml-md-minus-1 {
    margin-left: -1em;
  }
  .my-md-minus-1 {
    margin-top: -1em;
    margin-bottom: -1em;
  }
  .mx-md-minus-1 {
    margin-left: -1em;
    margin-right: -1em;
  }
}
.m-2 {
  margin: 2em;
}

.mt-2 {
  margin-top: 2em;
}

.mr-2 {
  margin-right: 2em;
}

.mb-2 {
  margin-bottom: 2em;
}

.ml-2 {
  margin-left: 2em;
}

.my-2 {
  margin-top: 2em;
  margin-bottom: 2em;
}

.mx-2 {
  margin-left: 2em;
  margin-right: 2em;
}

.m-minus-2 {
  margin: -2em;
}

.mt-minus-2 {
  margin-top: -2em;
}

.mr-minus-2 {
  margin-right: -2em;
}

.mb-minus-2 {
  margin-bottom: -2em;
}

.ml-minus-2 {
  margin-left: -2em;
}

.my-minus-2 {
  margin-top: -2em;
  margin-bottom: -2em;
}

.mx-minus-2 {
  margin-left: -2em;
  margin-right: -2em;
}

@media screen and (min-width: 768px) {
  .m-sm-2 {
    margin: 2em;
  }
  .mt-sm-2 {
    margin-top: 2em;
  }
  .mr-sm-2 {
    margin-right: 2em;
  }
  .mb-sm-2 {
    margin-bottom: 2em;
  }
  .ml-sm-2 {
    margin-left: 2em;
  }
  .my-sm-2 {
    margin-top: 2em;
    margin-bottom: 2em;
  }
  .mx-sm-2 {
    margin-left: 2em;
    margin-right: 2em;
  }
  .m-sm-minus-2 {
    margin: -2em;
  }
  .mt-sm-minus-2 {
    margin-top: -2em;
  }
  .mr-sm-minus-2 {
    margin-right: -2em;
  }
  .mb-sm-minus-2 {
    margin-bottom: -2em;
  }
  .ml-sm-minus-2 {
    margin-left: -2em;
  }
  .my-sm-minus-2 {
    margin-top: -2em;
    margin-bottom: -2em;
  }
  .mx-sm-minus-2 {
    margin-left: -2em;
    margin-right: -2em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-2 {
    margin: 2em;
  }
  .mt-md-2 {
    margin-top: 2em;
  }
  .mr-md-2 {
    margin-right: 2em;
  }
  .mb-md-2 {
    margin-bottom: 2em;
  }
  .ml-md-2 {
    margin-left: 2em;
  }
  .my-md-2 {
    margin-top: 2em;
    margin-bottom: 2em;
  }
  .mx-md-2 {
    margin-left: 2em;
    margin-right: 2em;
  }
  .m-md-minus-2 {
    margin: -2em;
  }
  .mt-md-minus-2 {
    margin-top: -2em;
  }
  .mr-md-minus-2 {
    margin-right: -2em;
  }
  .mb-md-minus-2 {
    margin-bottom: -2em;
  }
  .ml-md-minus-2 {
    margin-left: -2em;
  }
  .my-md-minus-2 {
    margin-top: -2em;
    margin-bottom: -2em;
  }
  .mx-md-minus-2 {
    margin-left: -2em;
    margin-right: -2em;
  }
}
.m-3 {
  margin: 3em;
}

.mt-3 {
  margin-top: 3em;
}

.mr-3 {
  margin-right: 3em;
}

.mb-3 {
  margin-bottom: 3em;
}

.ml-3 {
  margin-left: 3em;
}

.my-3 {
  margin-top: 3em;
  margin-bottom: 3em;
}

.mx-3 {
  margin-left: 3em;
  margin-right: 3em;
}

.m-minus-3 {
  margin: -3em;
}

.mt-minus-3 {
  margin-top: -3em;
}

.mr-minus-3 {
  margin-right: -3em;
}

.mb-minus-3 {
  margin-bottom: -3em;
}

.ml-minus-3 {
  margin-left: -3em;
}

.my-minus-3 {
  margin-top: -3em;
  margin-bottom: -3em;
}

.mx-minus-3 {
  margin-left: -3em;
  margin-right: -3em;
}

@media screen and (min-width: 768px) {
  .m-sm-3 {
    margin: 3em;
  }
  .mt-sm-3 {
    margin-top: 3em;
  }
  .mr-sm-3 {
    margin-right: 3em;
  }
  .mb-sm-3 {
    margin-bottom: 3em;
  }
  .ml-sm-3 {
    margin-left: 3em;
  }
  .my-sm-3 {
    margin-top: 3em;
    margin-bottom: 3em;
  }
  .mx-sm-3 {
    margin-left: 3em;
    margin-right: 3em;
  }
  .m-sm-minus-3 {
    margin: -3em;
  }
  .mt-sm-minus-3 {
    margin-top: -3em;
  }
  .mr-sm-minus-3 {
    margin-right: -3em;
  }
  .mb-sm-minus-3 {
    margin-bottom: -3em;
  }
  .ml-sm-minus-3 {
    margin-left: -3em;
  }
  .my-sm-minus-3 {
    margin-top: -3em;
    margin-bottom: -3em;
  }
  .mx-sm-minus-3 {
    margin-left: -3em;
    margin-right: -3em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-3 {
    margin: 3em;
  }
  .mt-md-3 {
    margin-top: 3em;
  }
  .mr-md-3 {
    margin-right: 3em;
  }
  .mb-md-3 {
    margin-bottom: 3em;
  }
  .ml-md-3 {
    margin-left: 3em;
  }
  .my-md-3 {
    margin-top: 3em;
    margin-bottom: 3em;
  }
  .mx-md-3 {
    margin-left: 3em;
    margin-right: 3em;
  }
  .m-md-minus-3 {
    margin: -3em;
  }
  .mt-md-minus-3 {
    margin-top: -3em;
  }
  .mr-md-minus-3 {
    margin-right: -3em;
  }
  .mb-md-minus-3 {
    margin-bottom: -3em;
  }
  .ml-md-minus-3 {
    margin-left: -3em;
  }
  .my-md-minus-3 {
    margin-top: -3em;
    margin-bottom: -3em;
  }
  .mx-md-minus-3 {
    margin-left: -3em;
    margin-right: -3em;
  }
}
.m-4 {
  margin: 4em;
}

.mt-4 {
  margin-top: 4em;
}

.mr-4 {
  margin-right: 4em;
}

.mb-4 {
  margin-bottom: 4em;
}

.ml-4 {
  margin-left: 4em;
}

.my-4 {
  margin-top: 4em;
  margin-bottom: 4em;
}

.mx-4 {
  margin-left: 4em;
  margin-right: 4em;
}

.m-minus-4 {
  margin: -4em;
}

.mt-minus-4 {
  margin-top: -4em;
}

.mr-minus-4 {
  margin-right: -4em;
}

.mb-minus-4 {
  margin-bottom: -4em;
}

.ml-minus-4 {
  margin-left: -4em;
}

.my-minus-4 {
  margin-top: -4em;
  margin-bottom: -4em;
}

.mx-minus-4 {
  margin-left: -4em;
  margin-right: -4em;
}

@media screen and (min-width: 768px) {
  .m-sm-4 {
    margin: 4em;
  }
  .mt-sm-4 {
    margin-top: 4em;
  }
  .mr-sm-4 {
    margin-right: 4em;
  }
  .mb-sm-4 {
    margin-bottom: 4em;
  }
  .ml-sm-4 {
    margin-left: 4em;
  }
  .my-sm-4 {
    margin-top: 4em;
    margin-bottom: 4em;
  }
  .mx-sm-4 {
    margin-left: 4em;
    margin-right: 4em;
  }
  .m-sm-minus-4 {
    margin: -4em;
  }
  .mt-sm-minus-4 {
    margin-top: -4em;
  }
  .mr-sm-minus-4 {
    margin-right: -4em;
  }
  .mb-sm-minus-4 {
    margin-bottom: -4em;
  }
  .ml-sm-minus-4 {
    margin-left: -4em;
  }
  .my-sm-minus-4 {
    margin-top: -4em;
    margin-bottom: -4em;
  }
  .mx-sm-minus-4 {
    margin-left: -4em;
    margin-right: -4em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-4 {
    margin: 4em;
  }
  .mt-md-4 {
    margin-top: 4em;
  }
  .mr-md-4 {
    margin-right: 4em;
  }
  .mb-md-4 {
    margin-bottom: 4em;
  }
  .ml-md-4 {
    margin-left: 4em;
  }
  .my-md-4 {
    margin-top: 4em;
    margin-bottom: 4em;
  }
  .mx-md-4 {
    margin-left: 4em;
    margin-right: 4em;
  }
  .m-md-minus-4 {
    margin: -4em;
  }
  .mt-md-minus-4 {
    margin-top: -4em;
  }
  .mr-md-minus-4 {
    margin-right: -4em;
  }
  .mb-md-minus-4 {
    margin-bottom: -4em;
  }
  .ml-md-minus-4 {
    margin-left: -4em;
  }
  .my-md-minus-4 {
    margin-top: -4em;
    margin-bottom: -4em;
  }
  .mx-md-minus-4 {
    margin-left: -4em;
    margin-right: -4em;
  }
}
.m-5 {
  margin: 5em;
}

.mt-5 {
  margin-top: 5em;
}

.mr-5 {
  margin-right: 5em;
}

.mb-5 {
  margin-bottom: 5em;
}

.ml-5 {
  margin-left: 5em;
}

.my-5 {
  margin-top: 5em;
  margin-bottom: 5em;
}

.mx-5 {
  margin-left: 5em;
  margin-right: 5em;
}

.m-minus-5 {
  margin: -5em;
}

.mt-minus-5 {
  margin-top: -5em;
}

.mr-minus-5 {
  margin-right: -5em;
}

.mb-minus-5 {
  margin-bottom: -5em;
}

.ml-minus-5 {
  margin-left: -5em;
}

.my-minus-5 {
  margin-top: -5em;
  margin-bottom: -5em;
}

.mx-minus-5 {
  margin-left: -5em;
  margin-right: -5em;
}

@media screen and (min-width: 768px) {
  .m-sm-5 {
    margin: 5em;
  }
  .mt-sm-5 {
    margin-top: 5em;
  }
  .mr-sm-5 {
    margin-right: 5em;
  }
  .mb-sm-5 {
    margin-bottom: 5em;
  }
  .ml-sm-5 {
    margin-left: 5em;
  }
  .my-sm-5 {
    margin-top: 5em;
    margin-bottom: 5em;
  }
  .mx-sm-5 {
    margin-left: 5em;
    margin-right: 5em;
  }
  .m-sm-minus-5 {
    margin: -5em;
  }
  .mt-sm-minus-5 {
    margin-top: -5em;
  }
  .mr-sm-minus-5 {
    margin-right: -5em;
  }
  .mb-sm-minus-5 {
    margin-bottom: -5em;
  }
  .ml-sm-minus-5 {
    margin-left: -5em;
  }
  .my-sm-minus-5 {
    margin-top: -5em;
    margin-bottom: -5em;
  }
  .mx-sm-minus-5 {
    margin-left: -5em;
    margin-right: -5em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-5 {
    margin: 5em;
  }
  .mt-md-5 {
    margin-top: 5em;
  }
  .mr-md-5 {
    margin-right: 5em;
  }
  .mb-md-5 {
    margin-bottom: 5em;
  }
  .ml-md-5 {
    margin-left: 5em;
  }
  .my-md-5 {
    margin-top: 5em;
    margin-bottom: 5em;
  }
  .mx-md-5 {
    margin-left: 5em;
    margin-right: 5em;
  }
  .m-md-minus-5 {
    margin: -5em;
  }
  .mt-md-minus-5 {
    margin-top: -5em;
  }
  .mr-md-minus-5 {
    margin-right: -5em;
  }
  .mb-md-minus-5 {
    margin-bottom: -5em;
  }
  .ml-md-minus-5 {
    margin-left: -5em;
  }
  .my-md-minus-5 {
    margin-top: -5em;
    margin-bottom: -5em;
  }
  .mx-md-minus-5 {
    margin-left: -5em;
    margin-right: -5em;
  }
}
.m-6 {
  margin: 6em;
}

.mt-6 {
  margin-top: 6em;
}

.mr-6 {
  margin-right: 6em;
}

.mb-6 {
  margin-bottom: 6em;
}

.ml-6 {
  margin-left: 6em;
}

.my-6 {
  margin-top: 6em;
  margin-bottom: 6em;
}

.mx-6 {
  margin-left: 6em;
  margin-right: 6em;
}

.m-minus-6 {
  margin: -6em;
}

.mt-minus-6 {
  margin-top: -6em;
}

.mr-minus-6 {
  margin-right: -6em;
}

.mb-minus-6 {
  margin-bottom: -6em;
}

.ml-minus-6 {
  margin-left: -6em;
}

.my-minus-6 {
  margin-top: -6em;
  margin-bottom: -6em;
}

.mx-minus-6 {
  margin-left: -6em;
  margin-right: -6em;
}

@media screen and (min-width: 768px) {
  .m-sm-6 {
    margin: 6em;
  }
  .mt-sm-6 {
    margin-top: 6em;
  }
  .mr-sm-6 {
    margin-right: 6em;
  }
  .mb-sm-6 {
    margin-bottom: 6em;
  }
  .ml-sm-6 {
    margin-left: 6em;
  }
  .my-sm-6 {
    margin-top: 6em;
    margin-bottom: 6em;
  }
  .mx-sm-6 {
    margin-left: 6em;
    margin-right: 6em;
  }
  .m-sm-minus-6 {
    margin: -6em;
  }
  .mt-sm-minus-6 {
    margin-top: -6em;
  }
  .mr-sm-minus-6 {
    margin-right: -6em;
  }
  .mb-sm-minus-6 {
    margin-bottom: -6em;
  }
  .ml-sm-minus-6 {
    margin-left: -6em;
  }
  .my-sm-minus-6 {
    margin-top: -6em;
    margin-bottom: -6em;
  }
  .mx-sm-minus-6 {
    margin-left: -6em;
    margin-right: -6em;
  }
}
@media screen and (min-width: 992px) {
  .m-md-6 {
    margin: 6em;
  }
  .mt-md-6 {
    margin-top: 6em;
  }
  .mr-md-6 {
    margin-right: 6em;
  }
  .mb-md-6 {
    margin-bottom: 6em;
  }
  .ml-md-6 {
    margin-left: 6em;
  }
  .my-md-6 {
    margin-top: 6em;
    margin-bottom: 6em;
  }
  .mx-md-6 {
    margin-left: 6em;
    margin-right: 6em;
  }
  .m-md-minus-6 {
    margin: -6em;
  }
  .mt-md-minus-6 {
    margin-top: -6em;
  }
  .mr-md-minus-6 {
    margin-right: -6em;
  }
  .mb-md-minus-6 {
    margin-bottom: -6em;
  }
  .ml-md-minus-6 {
    margin-left: -6em;
  }
  .my-md-minus-6 {
    margin-top: -6em;
    margin-bottom: -6em;
  }
  .mx-md-minus-6 {
    margin-left: -6em;
    margin-right: -6em;
  }
}
.mt-0-5 {
  margin-top: 0.5em;
}

.mb-0-5 {
  margin-bottom: 0.5em;
}

.mr-auto {
  margin-right: auto !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.ml-auto {
  margin-left: auto !important;
}

.mt-auto {
  margin-top: auto !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

/*!========================================================================
 * 99. Offsets
 * ======================================================================!*/
.offset_bottom {
  z-index: 60;
}

.pt-small.offset_bottom {
  padding-top: 0;
}
.pt-small.offset_bottom .section-offset__content, .pt-small.offset_bottom > .elementor-container {
  transform: translateY(calc(1 * var(--distance-min-small) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pt-small.offset_bottom .section-offset__content, .pt-small.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * ((var(--distance-min-small) * 1px) + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pt-small.offset_bottom .section-offset__content, .pt-small.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * var(--distance-max-small) * 1px ));
  }
}
.pt-small.offset_bottom.mb-small {
  margin-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-small.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-small.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pt-small.offset_bottom.mb-medium {
  margin-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-small.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-small.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pt-small.offset_bottom.mb-large {
  margin-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-small.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-small.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pt-medium.offset_bottom {
  padding-top: 0;
}
.pt-medium.offset_bottom .section-offset__content, .pt-medium.offset_bottom > .elementor-container {
  transform: translateY(calc(1 * var(--distance-min-medium) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pt-medium.offset_bottom .section-offset__content, .pt-medium.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * ((var(--distance-min-medium) * 1px) + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pt-medium.offset_bottom .section-offset__content, .pt-medium.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * var(--distance-max-normal) * 1px ));
  }
}
.pt-medium.offset_bottom.mb-small {
  margin-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-medium.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-medium.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pt-medium.offset_bottom.mb-medium {
  margin-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-medium.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-medium.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pt-medium.offset_bottom.mb-large {
  margin-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-medium.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-medium.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pt-large.offset_bottom {
  padding-top: 0;
}
.pt-large.offset_bottom .section-offset__content, .pt-large.offset_bottom > .elementor-container {
  transform: translateY(calc(1 * var(--distance-min-medium) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pt-large.offset_bottom .section-offset__content, .pt-large.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * ((var(--distance-min-medium) * 1px) + (var(--distance-max-large) - var(--distance-min-medium)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pt-large.offset_bottom .section-offset__content, .pt-large.offset_bottom > .elementor-container {
    transform: translateY(calc(1 * var(--distance-max-large) * 1px ));
  }
}
.pt-large.offset_bottom.mb-small {
  margin-bottom: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-large.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-large.offset_bottom.mb-small {
    margin-bottom: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pt-large.offset_bottom.mb-medium {
  margin-bottom: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-large.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-large.offset_bottom.mb-medium {
    margin-bottom: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pt-large.offset_bottom.mb-large {
  margin-bottom: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pt-large.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pt-large.offset_bottom.mb-large {
    margin-bottom: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pb-small.offset_top {
  padding-bottom: 0;
}
.pb-small.offset_top .section-offset__content, .pb-small.offset_top > .elementor-container {
  transform: translateY(calc(-1 * var(--distance-min-small) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pb-small.offset_top .section-offset__content, .pb-small.offset_top > .elementor-container {
    transform: translateY(calc(-1 * ((var(--distance-min-small) * 1px) + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pb-small.offset_top .section-offset__content, .pb-small.offset_top > .elementor-container {
    transform: translateY(calc(-1 * var(--distance-max-small) * 1px ));
  }
}
.pb-small.offset_top.mt-small {
  margin-top: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-small.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-small.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pb-small.offset_top.mt-medium {
  margin-top: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-small.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-small.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pb-small.offset_top.mt-large {
  margin-top: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-small.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-small.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pb-medium.offset_top {
  padding-bottom: 0;
}
.pb-medium.offset_top .section-offset__content, .pb-medium.offset_top > .elementor-container {
  transform: translateY(calc(-1 * var(--distance-min-medium) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pb-medium.offset_top .section-offset__content, .pb-medium.offset_top > .elementor-container {
    transform: translateY(calc(-1 * ((var(--distance-min-medium) * 1px) + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pb-medium.offset_top .section-offset__content, .pb-medium.offset_top > .elementor-container {
    transform: translateY(calc(-1 * var(--distance-max-normal) * 1px ));
  }
}
.pb-medium.offset_top.mt-small {
  margin-top: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-medium.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-medium.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pb-medium.offset_top.mt-medium {
  margin-top: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-medium.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-medium.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pb-medium.offset_top.mt-large {
  margin-top: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-medium.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-medium.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}

.pb-large.offset_top {
  padding-bottom: 0;
}
.pb-large.offset_top .section-offset__content, .pb-large.offset_top > .elementor-container {
  transform: translateY(calc(-1 * var(--distance-min-large) * 1px ));
}
@media only screen and (min-width: 320px) and (max-width: 2560px) {
  .pb-large.offset_top .section-offset__content, .pb-large.offset_top > .elementor-container {
    transform: translateY(calc(-1 * ((var(--distance-min-large) * 1px) + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240))));
  }
}
@media only screen and (min-width: 2560px) {
  .pb-large.offset_top .section-offset__content, .pb-large.offset_top > .elementor-container {
    transform: translateY(calc(-1 * var(--distance-max-large) * 1px ));
  }
}
.pb-large.offset_top.mt-small {
  margin-top: calc(1 * (var(--distance-min-small) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-large.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-min-small) * 1px + (var(--distance-max-small) - var(--distance-min-small)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-large.offset_top.mt-small {
    margin-top: calc(1 * (var(--distance-max-small) * 1px));
  }
}
.pb-large.offset_top.mt-medium {
  margin-top: calc(1 * (var(--distance-min-medium) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-large.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-min-medium) * 1px + (var(--distance-max-normal) - var(--distance-min-medium)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-large.offset_top.mt-medium {
    margin-top: calc(1 * (var(--distance-max-normal) * 1px));
  }
}
.pb-large.offset_top.mt-large {
  margin-top: calc(1 * (var(--distance-min-large) * 1px));
}
@media screen and (min-width: 320px) {
  .pb-large.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-min-large) * 1px + (var(--distance-max-large) - var(--distance-min-large)) * ((100vw - 320px) / 2240)));
  }
}
@media screen and (min-width: 2560px) {
  .pb-large.offset_top.mt-large {
    margin-top: calc(1 * (var(--distance-max-large) * 1px));
  }
}

/*!========================================================================
 * 100. Paddings
 * ======================================================================!*/
.p-0 {
  padding: 0em;
}

.pt-0 {
  padding-top: 0em;
}

.pr-0 {
  padding-right: 0em;
}

.pb-0 {
  padding-bottom: 0em;
}

.pl-0 {
  padding-left: 0em;
}

.py-0 {
  padding-top: 0em;
  padding-bottom: 0em;
}

.px-0 {
  padding-left: 0em;
  padding-right: 0em;
}

@media screen and (min-width: 768px) {
  .p-sm-0 {
    padding: 0em !important;
  }
  .pt-sm-0 {
    padding-top: 0em !important;
  }
  .pr-sm-0 {
    padding-right: 0em !important;
  }
  .pb-sm-0 {
    padding-bottom: 0em !important;
  }
  .pl-sm-0 {
    padding-left: 0em !important;
  }
  .py-sm-0 {
    padding-top: 0em !important;
    padding-bottom: 0em !important;
  }
  .px-sm-0 {
    padding-left: 0em !important;
    padding-right: 0em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-0 {
    padding: 0em !important;
  }
  .pt-md-0 {
    padding-top: 0em !important;
  }
  .pr-md-0 {
    padding-right: 0em !important;
  }
  .pb-md-0 {
    padding-bottom: 0em !important;
  }
  .pl-md-0 {
    padding-left: 0em !important;
  }
  .py-md-0 {
    padding-top: 0em !important;
    padding-bottom: 0em !important;
  }
  .px-md-0 {
    padding-left: 0em !important;
    padding-right: 0em !important;
  }
}
.p-1 {
  padding: 1em;
}

.pt-1 {
  padding-top: 1em;
}

.pr-1 {
  padding-right: 1em;
}

.pb-1 {
  padding-bottom: 1em;
}

.pl-1 {
  padding-left: 1em;
}

.py-1 {
  padding-top: 1em;
  padding-bottom: 1em;
}

.px-1 {
  padding-left: 1em;
  padding-right: 1em;
}

@media screen and (min-width: 768px) {
  .p-sm-1 {
    padding: 1em !important;
  }
  .pt-sm-1 {
    padding-top: 1em !important;
  }
  .pr-sm-1 {
    padding-right: 1em !important;
  }
  .pb-sm-1 {
    padding-bottom: 1em !important;
  }
  .pl-sm-1 {
    padding-left: 1em !important;
  }
  .py-sm-1 {
    padding-top: 1em !important;
    padding-bottom: 1em !important;
  }
  .px-sm-1 {
    padding-left: 1em !important;
    padding-right: 1em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-1 {
    padding: 1em !important;
  }
  .pt-md-1 {
    padding-top: 1em !important;
  }
  .pr-md-1 {
    padding-right: 1em !important;
  }
  .pb-md-1 {
    padding-bottom: 1em !important;
  }
  .pl-md-1 {
    padding-left: 1em !important;
  }
  .py-md-1 {
    padding-top: 1em !important;
    padding-bottom: 1em !important;
  }
  .px-md-1 {
    padding-left: 1em !important;
    padding-right: 1em !important;
  }
}
.p-2 {
  padding: 2em;
}

.pt-2 {
  padding-top: 2em;
}

.pr-2 {
  padding-right: 2em;
}

.pb-2 {
  padding-bottom: 2em;
}

.pl-2 {
  padding-left: 2em;
}

.py-2 {
  padding-top: 2em;
  padding-bottom: 2em;
}

.px-2 {
  padding-left: 2em;
  padding-right: 2em;
}

@media screen and (min-width: 768px) {
  .p-sm-2 {
    padding: 2em !important;
  }
  .pt-sm-2 {
    padding-top: 2em !important;
  }
  .pr-sm-2 {
    padding-right: 2em !important;
  }
  .pb-sm-2 {
    padding-bottom: 2em !important;
  }
  .pl-sm-2 {
    padding-left: 2em !important;
  }
  .py-sm-2 {
    padding-top: 2em !important;
    padding-bottom: 2em !important;
  }
  .px-sm-2 {
    padding-left: 2em !important;
    padding-right: 2em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-2 {
    padding: 2em !important;
  }
  .pt-md-2 {
    padding-top: 2em !important;
  }
  .pr-md-2 {
    padding-right: 2em !important;
  }
  .pb-md-2 {
    padding-bottom: 2em !important;
  }
  .pl-md-2 {
    padding-left: 2em !important;
  }
  .py-md-2 {
    padding-top: 2em !important;
    padding-bottom: 2em !important;
  }
  .px-md-2 {
    padding-left: 2em !important;
    padding-right: 2em !important;
  }
}
.p-3 {
  padding: 3em;
}

.pt-3 {
  padding-top: 3em;
}

.pr-3 {
  padding-right: 3em;
}

.pb-3 {
  padding-bottom: 3em;
}

.pl-3 {
  padding-left: 3em;
}

.py-3 {
  padding-top: 3em;
  padding-bottom: 3em;
}

.px-3 {
  padding-left: 3em;
  padding-right: 3em;
}

@media screen and (min-width: 768px) {
  .p-sm-3 {
    padding: 3em !important;
  }
  .pt-sm-3 {
    padding-top: 3em !important;
  }
  .pr-sm-3 {
    padding-right: 3em !important;
  }
  .pb-sm-3 {
    padding-bottom: 3em !important;
  }
  .pl-sm-3 {
    padding-left: 3em !important;
  }
  .py-sm-3 {
    padding-top: 3em !important;
    padding-bottom: 3em !important;
  }
  .px-sm-3 {
    padding-left: 3em !important;
    padding-right: 3em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-3 {
    padding: 3em !important;
  }
  .pt-md-3 {
    padding-top: 3em !important;
  }
  .pr-md-3 {
    padding-right: 3em !important;
  }
  .pb-md-3 {
    padding-bottom: 3em !important;
  }
  .pl-md-3 {
    padding-left: 3em !important;
  }
  .py-md-3 {
    padding-top: 3em !important;
    padding-bottom: 3em !important;
  }
  .px-md-3 {
    padding-left: 3em !important;
    padding-right: 3em !important;
  }
}
.p-4 {
  padding: 4em;
}

.pt-4 {
  padding-top: 4em;
}

.pr-4 {
  padding-right: 4em;
}

.pb-4 {
  padding-bottom: 4em;
}

.pl-4 {
  padding-left: 4em;
}

.py-4 {
  padding-top: 4em;
  padding-bottom: 4em;
}

.px-4 {
  padding-left: 4em;
  padding-right: 4em;
}

@media screen and (min-width: 768px) {
  .p-sm-4 {
    padding: 4em !important;
  }
  .pt-sm-4 {
    padding-top: 4em !important;
  }
  .pr-sm-4 {
    padding-right: 4em !important;
  }
  .pb-sm-4 {
    padding-bottom: 4em !important;
  }
  .pl-sm-4 {
    padding-left: 4em !important;
  }
  .py-sm-4 {
    padding-top: 4em !important;
    padding-bottom: 4em !important;
  }
  .px-sm-4 {
    padding-left: 4em !important;
    padding-right: 4em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-4 {
    padding: 4em !important;
  }
  .pt-md-4 {
    padding-top: 4em !important;
  }
  .pr-md-4 {
    padding-right: 4em !important;
  }
  .pb-md-4 {
    padding-bottom: 4em !important;
  }
  .pl-md-4 {
    padding-left: 4em !important;
  }
  .py-md-4 {
    padding-top: 4em !important;
    padding-bottom: 4em !important;
  }
  .px-md-4 {
    padding-left: 4em !important;
    padding-right: 4em !important;
  }
}
.p-5 {
  padding: 5em;
}

.pt-5 {
  padding-top: 5em;
}

.pr-5 {
  padding-right: 5em;
}

.pb-5 {
  padding-bottom: 5em;
}

.pl-5 {
  padding-left: 5em;
}

.py-5 {
  padding-top: 5em;
  padding-bottom: 5em;
}

.px-5 {
  padding-left: 5em;
  padding-right: 5em;
}

@media screen and (min-width: 768px) {
  .p-sm-5 {
    padding: 5em !important;
  }
  .pt-sm-5 {
    padding-top: 5em !important;
  }
  .pr-sm-5 {
    padding-right: 5em !important;
  }
  .pb-sm-5 {
    padding-bottom: 5em !important;
  }
  .pl-sm-5 {
    padding-left: 5em !important;
  }
  .py-sm-5 {
    padding-top: 5em !important;
    padding-bottom: 5em !important;
  }
  .px-sm-5 {
    padding-left: 5em !important;
    padding-right: 5em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-5 {
    padding: 5em !important;
  }
  .pt-md-5 {
    padding-top: 5em !important;
  }
  .pr-md-5 {
    padding-right: 5em !important;
  }
  .pb-md-5 {
    padding-bottom: 5em !important;
  }
  .pl-md-5 {
    padding-left: 5em !important;
  }
  .py-md-5 {
    padding-top: 5em !important;
    padding-bottom: 5em !important;
  }
  .px-md-5 {
    padding-left: 5em !important;
    padding-right: 5em !important;
  }
}
.p-6 {
  padding: 6em;
}

.pt-6 {
  padding-top: 6em;
}

.pr-6 {
  padding-right: 6em;
}

.pb-6 {
  padding-bottom: 6em;
}

.pl-6 {
  padding-left: 6em;
}

.py-6 {
  padding-top: 6em;
  padding-bottom: 6em;
}

.px-6 {
  padding-left: 6em;
  padding-right: 6em;
}

@media screen and (min-width: 768px) {
  .p-sm-6 {
    padding: 6em !important;
  }
  .pt-sm-6 {
    padding-top: 6em !important;
  }
  .pr-sm-6 {
    padding-right: 6em !important;
  }
  .pb-sm-6 {
    padding-bottom: 6em !important;
  }
  .pl-sm-6 {
    padding-left: 6em !important;
  }
  .py-sm-6 {
    padding-top: 6em !important;
    padding-bottom: 6em !important;
  }
  .px-sm-6 {
    padding-left: 6em !important;
    padding-right: 6em !important;
  }
}
@media screen and (min-width: 992px) {
  .p-md-6 {
    padding: 6em !important;
  }
  .pt-md-6 {
    padding-top: 6em !important;
  }
  .pr-md-6 {
    padding-right: 6em !important;
  }
  .pb-md-6 {
    padding-bottom: 6em !important;
  }
  .pl-md-6 {
    padding-left: 6em !important;
  }
  .py-md-6 {
    padding-top: 6em !important;
    padding-bottom: 6em !important;
  }
  .px-md-6 {
    padding-left: 6em !important;
    padding-right: 6em !important;
  }
}
.pt-0-5 {
  padding-top: 0.5em;
}

.pb-0-5 {
  padding-bottom: 0.5em;
}

/*!========================================================================
 * 101. Utilities
 * ======================================================================!*/
.overflow {
  position: relative;
  overflow: hidden;
}

.position-relative {
  position: relative;
}

.position-fixed {
  position: fixed;
}

.of-cover {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  font-family: "object-fit: cover;";
}

.of-contain {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  font-family: "object-fit: contain;";
}

.hidden {
  opacity: 0;
  visibility: hidden;
}

.no-gutters {
  padding-left: 0;
  padding-right: 0;
}

.grayscale {
  filter: grayscale(70%);
}

.w-100 {
  width: 100% !important;
}

.w-100vh {
  width: 100vh !important;
}

.w-100vw {
  width: 100vw !important;
}

.h-100 {
  height: 100% !important;
}

.h-100vh {
  height: 100vh !important;
}

.h-100vw {
  height: 100vw !important;
}

.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

.backgroundblendmode .blend-difference {
  mix-blend-mode: difference;
  color: #fff;
}
.backgroundblendmode .blend-difference a {
  color: #fff;
}
.backgroundblendmode .blend-difference a:hover {
  opacity: 0.7;
}

.hidden_absolute {
  position: absolute;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  visibility: hidden;
}

#js-webgl {
  display: none;
}

.d-flex-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

.z-50 {
  z-index: 50;
}

.z-100 {
  z-index: 100;
}

.z-500 {
  z-index: 500;
}

.z-1000 {
  z-index: 1000;
}

.block-circle {
  border-radius: 100%;
}

.block-counter {
  padding-left: 6px;
  padding-right: 6px;
  position: relative;
}

.block-counter__counter {
  position: absolute;
  top: 0;
  left: 100%;
  font-size: calc(13 * 1px);
  font-family: var(--font-secondary);
  font-weight: bold;
  color: var(--color-gray-1);
  white-space: nowrap;
}
@media screen and (min-width: 320px) {
  .block-counter__counter {
    font-size: calc(13 * 1px + (16 - 13) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .block-counter__counter {
    font-size: calc(16 * 1px);
  }
}

.col-gutters {
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
}

.row-gutters {
  margin-top: -20px;
  margin-bottom: -20px;
}

.pointer-events-none {
  pointer-events: none !important;
}
.pointer-events-none *:not(.pointer-events-auto) {
  pointer-events: none !important;
}

.pointer-events-auto, .pointer-events-none .pointer-events-auto {
  pointer-events: auto !important;
}

.pointer-events-auto *, .pointer-events-none .pointer-events-auto * {
  pointer-events: auto !important;
}

.border-radius-100 {
  border-radius: 100%;
}

/*!========================================================================
 * 102. Widget
 * ======================================================================!*/
.widget {
  margin-bottom: 2em;
  font-size: 16px;
}
.widget p {
  font-size: 16px;
}
.widget p:last-of-type {
  margin-bottom: 0;
}
.widget select {
  width: 100%;
  max-width: 100%;
}
.widget ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  text-align: left;
}
.widget ul li {
  display: block;
  margin-bottom: 1em;
}
.widget ul li:last-child {
  margin-bottom: 0;
}
.widget ul ul {
  width: 100%;
  padding-left: 1em;
  margin-top: 1em;
}
.widget ul .sub-menu {
  padding-left: 1em;
  margin-top: 1em;
}

.widgettitle {
  display: block;
  font-family: var(--font-primary);
  font-size: calc(var(--paragraph-min-font-size) * 1px);
  line-height: var(--paragraph-line-height);
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 1em;
}
@media screen and (min-width: 320px) {
  .widgettitle {
    font-size: calc(var(--paragraph-min-font-size) * 1px + (var(--paragraph-max-font-size) - var(--paragraph-min-font-size)) * ((100vw - 320px) / 2240));
  }
}
@media screen and (min-width: 2560px) {
  .widgettitle {
    font-size: calc(var(--paragraph-max-font-size) * 1px);
  }
}
.widgettitle:after {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  background-color: var(--color-dark-1);
  margin-bottom: 0.5em;
}

/*!========================================================================
 * 103. Widget Archive
 * ======================================================================!*/
.widget_archive ul li {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.widget_archive ul li span {
  font-size: 13px;
  line-height: 1;
}

/*!========================================================================
 * 104. Widget Calendar
 * ======================================================================!*/
#wp-calendar {
  width: 100%;
  text-align: center;
}
#wp-calendar caption {
  caption-side: top;
  width: 100%;
  text-align: center;
  padding-top: 0;
  padding-bottom: 10px;
}
#wp-calendar th {
  font-weight: 600;
  padding: 5px;
  text-align: center;
}
#wp-calendar td {
  padding: 5px;
}
#wp-calendar td#next {
  text-align: right;
}
#wp-calendar td#prev {
  text-align: left;
}
#wp-calendar tbody a:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  border-radius: 100%;
  transform: translate(-50%, -50%);
  z-index: -1;
  transition: all 0.2s cubic-bezier(0.785, 0.135, 0.15, 0.86);
}
#wp-calendar tr {
  border-bottom: none;
}
#wp-calendar a {
  display: inline-block;
  position: relative;
  z-index: 50;
}

/*!========================================================================
 * 105. Widget Categories
 * ======================================================================!*/
.widget_categories ul li {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.widget_categories ul li span {
  font-size: 14px;
  line-height: 1;
}

/*!========================================================================
 * 106. Widget Menu Inline
 * ======================================================================!*/
.widget_rhye_menu_inline ul li {
  display: inline-block;
  margin-bottom: 0;
}
.widget_rhye_menu_inline ul li a {
  padding: 4px;
}
.widget_rhye_menu_inline ul li a:before {
  display: none;
}

@media screen and (max-width: 991px) {
  .widget_rhye_menu_inline .menu {
    text-align: center;
  }
}
/*!========================================================================
 * 107. Widget Nav Menu
 * ======================================================================!*/
.widget_nav_menu ul.menu > li {
  white-space: normal;
  word-break: break-word;
}
.widget_nav_menu ul.menu > li a {
  display: inline-block;
  padding: 0;
  font-size: 16px;
  line-height: 1.5;
  font-weight: normal;
  text-transform: none;
  letter-spacing: 0;
  color: var(--color-gray-1);
}
.widget_nav_menu ul.menu > li a:after, .widget_nav_menu ul.menu > li a:before {
  display: none;
}
.widget_nav_menu ul.menu > li a:hover {
  color: var(--color-dark-1);
}
.widget_nav_menu ul.menu > li.menu-item-has-children {
  margin-bottom: 1.5em !important;
}
.widget_nav_menu ul.menu > li.menu-item-has-children a:after {
  display: none;
}
.widget_nav_menu ul.sub-menu {
  background-color: transparent;
  position: relative;
  top: auto;
  left: auto;
  right: auto;
  opacity: 1;
  visibility: visible;
  box-shadow: none;
  border-bottom: none;
  transform: none;
  margin-top: 1em;
}
.widget_nav_menu ul.sub-menu > li {
  border-left: none;
}
.widget_nav_menu ul.sub-menu > li > a {
  padding: 3px 0;
  border-left: none;
}
.widget_nav_menu ul.sub-menu > li > a:hover {
  background-color: unset;
}

/*!========================================================================
 * 108. Widget Polylang
 * ======================================================================!*/
.widget_polylang {
  display: inline-block;
  font-size: 14px;
}
.widget_polylang select {
  width: auto;
  display: inline-block;
  padding: 5px 25px 5px 15px;
}
.widget_polylang ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.widget_polylang ul li {
  display: inline-block;
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 0;
}

/*!========================================================================
 * 109. Widget RSS
 * ======================================================================!*/
.widget_rss .rsswidget {
  font-family: var(--font-secondary);
  font-weight: bold;
}
.widget_rss .rss-date {
  display: block;
  width: 100%;
  font-size: 13px;
  margin-top: 0.5em;
  margin-bottom: 1em;
}
.widget_rss .rssSummary {
  margin-top: 1em;
  margin-bottom: 1em;
}
.widget_rss ul > li {
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
  padding-bottom: 1em;
}
.widget_rss ul > li:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

/*!========================================================================
 * 110. Widget Recent Comments
 * ======================================================================!*/
.widget_recent_comments ul li {
  padding: 20px 0;
  margin-bottom: 0 !important;
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}
.widget_recent_comments ul li:first-child {
  padding-top: 0;
}
.widget_recent_comments ul li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}
.widget_recent_comments ul li a {
  font-family: var(--font-secondary);
  font-weight: bold;
}
.widget_recent_comments .comment-author-link a {
  font-family: var(--font-primary);
  font-weight: normal;
}

/*!========================================================================
 * 111. Widget Recent Entries
 * ======================================================================!*/
.widget_recent_entries .post-date {
  display: block;
  width: 100%;
  font-size: 13px;
  margin-top: 0.25em;
  color: var(--color-gray-1);
}
.widget_recent_entries ul li {
  padding: 20px 0;
  margin-bottom: 0 !important;
  border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}
.widget_recent_entries ul li a {
  font-family: var(--font-secondary);
  font-weight: bold;
  line-height: 1.5;
}
.widget_recent_entries ul li a span {
  display: inline-block;
}
.widget_recent_entries ul li:first-child {
  padding-top: 0;
}
.widget_recent_entries ul li:last-child {
  padding-bottom: 0;
  border-bottom: none;
}

/*!========================================================================
 * 112. Widget Social
 * ======================================================================!*/
.widget_rhye_social ul {
  text-align: inherit;
}
.widget_rhye_social ul li {
  display: inline-block;
  margin-bottom: 0;
}

/*!========================================================================
 * 113. Widget WPML
 * ======================================================================!*/
.widget_icl_lang_sel_widget {
  display: inline-block;
  margin-bottom: 0;
  vertical-align: middle;
  font-size: 14px;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown {
  width: auto;
  border-radius: 2px;
  padding: 2px 5px;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown a {
  border: none;
  background: transparent;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown a:hover, .widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown a:focus {
  opacity: 1;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown a:hover, .widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown a:focus, .widget_icl_lang_sel_widget .wpml-ls-legacy-dropdown .wpml-ls-current-language:hover > a {
  background: transparent;
}
.widget_icl_lang_sel_widget .wpml-ls-sub-menu {
  border-top: none;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-list-horizontal {
  padding: 2px 5px;
}
.widget_icl_lang_sel_widget .wpml-ls-legacy-list-horizontal .wpml-ls-item {
  display: inline-block;
  margin-bottom: 0;
}

.lang-switch-no-padding-right .widget_icl_lang_sel_widget .wpml-ls-legacy-list-horizontal {
  padding-right: 0;
}

/*!========================================================================
 * 114. Widget CTA
 * ======================================================================!*/
.widget_rhye_cta {
  max-width: 700px;
}

/*!========================================================================
 * 115. Widget Text
 * ======================================================================!*/
.widget_text .textwidget > p {
  margin-top: 0;
  margin-bottom: 0;
}

/*!========================================================================
 * 116. Widget Logo
 * ======================================================================!*/
.widget_rhye_logo, .widget_rhye_logo {
  max-width: 360px;
}

@media screen and (max-width: 991px) {
  .widget_rhye_logo, .widget_rhye_logo {
    max-width: 100%;
  }
}